<template>
  <v-navigation-drawer
    id="core-navigation-drawer"
    v-model="drawer"
    :dark="barColor !== 'rgba(228, 226, 226, 1), rgba(255, 255, 255, 0.7)'"
    :expand-on-hover="expandOnHover"
    :right="$vuetify.rtl"
    :src="sideBarImage"
    mobile-breakpoint="960"
    app
    :width="getSideBarWidth"
    v-bind="$attrs"
    :mini-variant.sync="isMiniDrawer"
    :mini-variant-width="80"
  >
    <template v-slot:img="props" v-if="customerSidebarImg == null || customerSidebarImg === ''">
      <!-- 只有当未指定侧边栏背景图时，才增加黑色背景蒙版效果 -->
      <v-img
        :gradient="`to bottom, ${barColor}`"
        v-bind="props"
      />
    </template>

    <v-divider class="mb-1" />

    <v-list
      dense
      nav
    >
      <v-list-item>
        <!-- <v-list-item-avatar
          class="align-self-center"
          color="white"
          size="26"
        >
          <v-img
            :src='require("@/assets/dashboard.png")'
          />
        </v-list-item-avatar> -->

        <v-list-item-content v-show="!isMiniDrawer">
          <v-list-item-title
            class="text-center drawer-title"
            :class="getTitleClass"
            :style="getTitleStyle">
            {{ pageTitle }}
          </v-list-item-title>
        </v-list-item-content>
      </v-list-item>
    </v-list>

    <v-divider class="mb-2" />

    <v-list
      expand
      nav
      v-if="!isShowCircleButtonStyle"
    >
      <!-- Style cascading bug  -->
      <!-- https://github.com/vuetifyjs/vuetify/pull/8574 -->
      <div />

      <template v-for="(item, i) in computedItems">
        <base-item-group
          v-if="item.children"
          :key="`group-${i}`"
          :item="item"
        >
        </base-item-group>

        <base-item
          v-else
          :key="`item-${i}`"
          :item="item"
          :class="isMiniDrawer ? 'setIconMiddle' : ''"
        />
      </template>

      <!-- Style cascading bug  -->
      <!-- https://github.com/vuetifyjs/vuetify/pull/8574 -->
      <div />
    </v-list>

    <!--20231215 显示圆形导航栏按钮-->
    <v-list
      expand
      style="margin-top: 16px"
      v-else
    >
      <v-list-item-group v-if="!isMiniDrawer">
        <!--mt-3调整不同按钮间的上下间距-->
        <v-list-item v-for="(item, index) in computedItems" :key="index" @click="selectManagerItem(item)" class="mt-3">
          <v-row>
            <v-col cols="12" class="justify-center text-center align-center" style="margin-top: 3px">
              <!--todo 此处如果不想用透明效果，可以直接用如下color写法，删除:class行-->
              <!-- :color="drawerSelectedItemName === item.title ? 'primary' : 'white'" -->
              <v-btn small
                fab
                :class="drawerSelectedItemName === item.title ? 'primary' : 'v-btn--disabled'"
              >
                <v-icon
                  medium
                  :color="drawerSelectedItemName === item.title ? '' : 'black'"
                >
                  {{ item.icon }}
                </v-icon>
              </v-btn>
            </v-col>
            <v-col class="mt-n7">
              <div class="text-center footer-text pt-2">{{ item.title }}</div>
            </v-col>
          </v-row>
        </v-list-item>
      </v-list-item-group>
      <v-list-item-group v-else>
        <v-list-item v-for="(item, index) in computedItems" :key="index" @click="selectManagerItem(item)" class="mt-4">
          <v-btn
            fab
            :class="drawerSelectedItemName === item.title ? 'primary' : 'v-btn--disabled'"
          >
            <v-icon large>
              {{ item.icon }}
            </v-icon>
          </v-btn>
        </v-list-item>
      </v-list-item-group>

    </v-list>

    <template v-slot:append>
      <!-- 导航栏底部增加logo显示 tips: 调整logo大小即调整max-width，横向部分，调整class="ml-3"（表示偏移左边3个单元）-->
      <v-row justify="center" style="margin-bottom: 0">
        <v-img
          v-if="isShowCustomerLogo"
          contain
          :src='logoPath'
          :max-width="customerLogoMaxWidth"
          :class="customerLogoOffsetClass"
          v-show="!isMiniDrawer"
        />
      </v-row>
    <base-item
      v-if="isShowCopyRight"
      class="text-center"
      :item="{
        title: $t('upgrade'),
        // icon: 'mdi-package-up',
        // to: '/dashboard/upgrade',
      }"
      v-show="!isMiniDrawer"
    />
    <v-divider></v-divider>
    <!--增加底部导航栏切换按钮-->
      <base-item
        v-if="!isButtonStyleListItem"
        :item="toggleSidebarItem"
        @click.native="toggleMiniSidebar()"
      />
    </template>
  </v-navigation-drawer>
</template>

<script>
  // Utilities
  import {
    mapState, mapGetters,
  } from 'vuex'
  import { isShowLogo, logoFileName, drawerSidebarImg, logoMaxWidth, logoOffsetClass, customerVersion } from "@/plugins/websocket";
  // 如下客制化版本隐藏版权信息
  const hideCopyrightVersionArray = ['C1A1','C2A8','C2A9','C2B0']

  export default {
    name: 'DashboardCoreDrawer',

    props: {
      expandOnHover: {
        type: Boolean,
        default: false,
      },
    },

    data: () => ({

    }),

    computed: {
      ...mapState(['barColor', 'barImage', 'showBarImage', 'isMiniDrawer', 'isCloudServer', 'drawerSelectedItemName']),
      ...mapGetters(['isAdmin', 'isWindowsServer']),
      // 显示圆形导航栏按钮
      isShowCircleButtonStyle() {
        return customerVersion === 'C6A0'
      },
      // 双层显示icon和文字内容
      isButtonStyleListItem() {
        return customerVersion === 'C6A0'
      },
      isShowCopyRight() {
        //return !hideCopyrightVersionArray.includes(customerVersion)
        return false
      },
      pageTitle() {
        return this.$ws.getCustomerTitle()
      },
      isDefaultTile() {
        return this.pageTitle === 'IP广播系统'
      },
      getTitleClass() {
        const currentLang = this.$i18n.locale
        const titleLength = this.pageTitle.length

        // 根据语言和标题长度返回不同的CSS类
        if (currentLang === 'en') {
          return 'title-en'
        } else if (currentLang === 'zh-TW') {
          return 'title-zh-tw'
        } else if (titleLength > 8) {
          return 'title-long'
        } else {
          return 'title-normal'
        }
      },
      getTitleStyle() {
        const currentLang = this.$i18n.locale
        const titleLength = this.pageTitle.length

        let fontSize = '1.22em'
        let fontWeight = '600'
        let lineHeight = '1.2'
        let wordBreak = 'keep-all'

        // 英文标题通常较长，使用较小字体
        if (currentLang === 'en') {
          fontSize = titleLength > 25 ? '1.0em' : '1.1em'
          wordBreak = 'break-word'
          lineHeight = '1.1'
        }
        // 繁体中文
        else if (currentLang === 'zh-TW') {
          fontSize = titleLength > 8 ? '1.1em' : '1.22em'
        }
        // 简体中文
        else {
          if (this.isDefaultTile) {
            fontSize = '1.50em'
          } else if (titleLength > 8) {
            fontSize = '1.0em'
          } else if (titleLength > 6) {
            fontSize = '1.1em'
          }
        }

        return `
          font-size: ${fontSize};
          font-weight: ${fontWeight};
          line-height: ${lineHeight};
          word-break: ${wordBreak};
          overflow: initial;
          white-space: normal;
          padding: 4px 8px;
        `
      },
      getSideBarWidth() {
        // 根据标题长度和语言自动调整侧边栏宽度
        const currentLang = this.$i18n.locale
        const titleLength = this.pageTitle.length

        // 基础宽度
        let baseWidth = 180

        // 英文语言下固定增加宽度
        if (currentLang === 'en') {
          baseWidth = 240
        }
        // 繁体中文与中文宽度一致
        else if (currentLang === 'zh-TW') {
          baseWidth = 180
        }
        // 简体中文使用动态计算
        else {
          // 根据标题长度动态调整宽度
          if (titleLength > 8) {
            baseWidth = 220
          } else if (titleLength > 6) {
            baseWidth = 190
          } else {
            baseWidth = 180
          }
        }

        return baseWidth
      },
      customerSidebarImg() {
        return drawerSidebarImg
      },
      customerLogoOffsetClass() {
        // 如果隐藏版权信息，上移LOGO
        return this.isShowCopyRight ? logoOffsetClass : logoOffsetClass + ' ' + 'mb-4'
        // return false
      },
      customerLogoMaxWidth() {
        return logoMaxWidth
      },
      logoPath() {
        return require("@/assets/" + logoFileName)
      },
      isShowCustomerLogo() {
        return isShowLogo
      },
      drawer: {
        get () {
          return this.$store.state.drawer
        },
        set (val) {
          this.$store.commit('SET_DRAWER', val)
        },
      },
      computedItems () {
        return this.items.map(this.mapItem)
      },
      profile () {
        return {
          avatar: true,
          title: this.$t('avatar'),
        }
      },
      sideBarImage () {
        if (this.customerSidebarImg != null && this.customerSidebarImg !== '') {
          // console.log('sideBarImage1='+this.customerSidebarImg)
          return require('@/assets/' + this.customerSidebarImg)
        }
        if (!this.showBarImage) {
          // console.log('sideBarImage2')
          return require('@/assets/' + "sidebar0.jpg");
        }
        if (this.barImage != null && this.barImage.indexOf('sidebar') !== -1) {
          // console.log('sideBarImage3')
          return require('@/assets/' + this.barImage)
        }
        // console.log('sideBarImage4')
        return require('@/assets/' + "sidebar0.jpg")
      },
      toggleSidebarItem() {
        const mdiIcon = this.isMiniDrawer ? 'mdi-chevron-right' : 'mdi-chevron-left'
        return {
          title: this.isMiniDrawer ? ' ' : this.$t('toggleSidebar'),
          icon: mdiIcon,
        }
      },
      items() {
        const drawerItems = [
        {
          title: 'routes.controlCenter',
          icon: 'mdi-monitor-dashboard',
          to: '/dashboard/dashboard',
        },
        {
          title: 'routes.timerManagement',
          icon: 'mdi-calendar-clock',
          to: '/dashboard/timer',
        },
        {
          title: 'routes.deviceManagement',
          icon: 'mdi-monitor-cellphone-star',
          to: '/dashboard/device',
        },
        {
          title: 'routes.mediaManagement',
          icon: 'mdi-folder-multiple-image',
          to: '/dashboard/media',
        },
        {
          title: 'routes.accountManagement',
          icon: 'mdi-account',
          to: '/dashboard/account',
        }]
        // ******** 对于Windows版本, 导航栏不显示监控管理
        if (!this.isWindowsServer && !this.isCloudServer) {
          drawerItems.push({
            title: 'routes.monitorManagement',
            icon: 'mdi-webcam',
            to: '/dashboard/monitor',
          })
        }
        drawerItems.push({
          title: 'routes.logManagement',
          icon: 'mdi-book-open-outline',
          to: '/dashboard/log',
          },
          {
            title: 'routes.systemSettings',
            icon: 'mdi-cogs',
            to: '/dashboard/system',
          },
          {
            title: 'routes.systemMaintenance',
            icon: 'mdi-chart-bubble',
            to: '/dashboard/maintenance',
          }
        )
        //********视音微云C2B6版本增加信息发布
        if(customerVersion === 'C2B6') {
          drawerItems.push({
            title: 'routes.informationPublish',
            icon: 'mdi-message-text',
            to: '/dashboard/information',
          })
        }

        drawerItems.push({
            title: 'routes.logout',
            icon: 'mdi-logout',
            to: '/',
        })
        if (!this.isAdmin) {
          // 非管理员：显示左侧边栏，包含 控制中心、定时管理、设备管理、退出登录、系统设置。
          let operatorDrawerArray = ['routes.controlCenter', 'routes.timerManagement', 'routes.deviceManagement', 'routes.mediaManagement', 'routes.accountManagement', 'routes.logManagement', 'routes.logout']
          if(customerVersion === 'C2B6') {
            operatorDrawerArray = ['routes.controlCenter', 'routes.timerManagement', 'routes.deviceManagement', 'routes.mediaManagement', 'routes.accountManagement', 'routes.logManagement', 'routes.informationPublish', 'routes.logout']
          }
          else if(customerVersion === 'C5A1') {
            operatorDrawerArray = ['routes.controlCenter', 'routes.timerManagement', 'routes.deviceManagement', 'routes.accountManagement', 'routes.logManagement', 'routes.informationPublish', 'routes.logout']
          }
          return drawerItems.filter(item => operatorDrawerArray.includes(item.title))
        }
        return drawerItems
      }
    },

    methods: {
      mapItem (item) {
        return {
          ...item,
          children: item.children ? item.children.map(this.mapItem) : undefined,
          title: this.$t(item.title),
        }
      },
      toggleMiniSidebar() {
        this.$store.commit('updateIsMiniDrawer', !this.isMiniDrawer)
      },
      selectManagerItem(item) {
        this.$store.commit('setDrawerSelectedItemName', item.title)
        const routePath = item.to
        if (this.$route.path !== routePath) {
          this.$router.push({ path: routePath })
        }
      }
    }
  }
</script>

<style lang="sass">
  @import '~vuetify/src/styles/tools/_rtl.sass'

  #core-navigation-drawer
    .v-list-group__header.v-list-item--active:before
      opacity: .24

    .v-list-item
      &__icon--text,
      &__icon:first-child
        justify-content: center
        text-align: center
        width: 20px

        +ltr()
          margin-right: 24px
          margin-left: 12px !important

        +rtl()
          margin-left: 24px
          margin-right: 12px !important

    .v-list--dense
      .v-list-item
        &__icon--text,
        &__icon:first-child
          margin-top: 10px

    .v-list-group--sub-group
      .v-list-item
        +ltr()
          padding-left: 8px

        +rtl()
          padding-right: 8px

      .v-list-group__header
        +ltr()
          padding-right: 0

        +rtl()
          padding-right: 0

        .v-list-item__icon--text
          margin-top: 19px
          order: 0

        .v-list-group__header__prepend-icon
          order: 2

          +ltr()
            margin-right: 8px

          +rtl()
            margin-left: 8px
</style>

<style scoped>
  .setIconMiddle >>> .mdi:before {
    padding-left: 12px;
  }

  /* 标题样式优化 */
  .drawer-title {
    word-wrap: break-word;
    hyphens: auto;
  }

  .title-en {
    font-family: 'Roboto', sans-serif;
    letter-spacing: -0.5px;
    word-break: break-word;
    line-height: 1.1;
  }

  .title-zh-tw {
    font-family: 'Microsoft JhengHei', 'PingFang TC', sans-serif;
  }

  .title-long {
    font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  }

  .title-normal {
    font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  }

  /* 确保标题在不同宽度下都能正确显示 */
  .v-list-item__title.drawer-title {
    max-width: 100%;
    overflow: visible !important;
    text-overflow: initial !important;
    white-space: normal !important;
  }
</style>
