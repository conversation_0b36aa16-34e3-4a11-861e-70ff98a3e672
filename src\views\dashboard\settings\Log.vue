<template>
  <div>
    <!--snackBar-->
    <!--通用successSnackBar-->
    <v-snackbar
      v-model="commonSuccessSnackBar"
      color="primary"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ successMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="primary" fab small class="ml-6" v-bind="attrs" @click="commonSuccessSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--通用errorSnackBar-->
    <v-snackbar
      v-model="commonErrorSnackBar"
      color="error"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="error" fab small class="ml-6" v-bind="attrs" @click="commonErrorSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--设备选择对话框-->
    <v-dialog
      v-model="deviceSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('log.deviceSelection') }}</span>
        </v-card-title>
        <v-card-subtitle class="pt-9 text-lg-h3 text-center">
          <span>{{ $t('log.deviceSelectionHint1') }}</span>
          <span style="color: red">{{ $t('log.deviceSelectionHint2') }}</span>
          <span>{{ $t('log.deviceSelectionHint3') }}</span>
        </v-card-subtitle>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="deviceSelectSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('log.deviceKeywordPlaceholder')"
            :data="deviceList"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('log.deviceList'), $t('log.selectedDevices')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="deviceSelectDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="saveDeviceSelected"
          >
            {{ $t('common.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--通话录音设备选择对话框-->
    <v-dialog
      v-model="callRecordDeviceSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('log.deviceSelection') }}</span>
        </v-card-title>
        <v-card-subtitle class="pt-9 text-lg-h3 text-center">
          <span>{{ $t('log.deviceSelectionHint1') }}</span>
          <span style="color: red">{{ $t('log.deviceSelectionHint2') }}</span>
          <span>{{ $t('log.deviceSelectionHint3') }}</span>
        </v-card-subtitle>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="callRecordDeviceSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('log.deviceKeywordPlaceholder')"
            :data="callRecordDeviceList"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('log.deviceList'), $t('log.selectedDevices')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="callRecordDeviceSelectDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="saveCallRecordDeviceSelected"
          >
            {{ $t('common.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--删除通话录音确认对话框-->
    <v-dialog
      v-model="deleteCallRecordDialog"
      max-width="400"
      persistent
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('common.confirmDelete') }}
        </v-card-title>
        <v-card-text>
          {{ $t('log.callRecord.deleteConfirmMessage') }}
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="grey darken-1"
            text
            @click="deleteCallRecordDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="error"
            text
            @click="confirmDeleteCallRecord"
          >
            {{ $t('common.delete') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--主显示页面-->
    <v-container
      id="Timers"
      fluid
      style="margin-top: 2px"
    >
      <v-card class="mb-2">
        <v-card-text>
          <v-tabs v-model="activeTab" background-color="transparent">
            <v-tab>{{ $t('log.tabs.systemLog') }}</v-tab>
            <v-tab v-if="!isCloudIpSystem() && isAdmin">{{ $t('log.tabs.callRecord') }}</v-tab>
          </v-tabs>
          <v-tabs-items v-model="activeTab">
            <!-- 系统日志TAB -->
            <v-tab-item>
              <v-row justify="center" align="center" class="mt-3">
            <v-col cols="2">
              <v-select
                v-model="logTypeSelected"
                :items="logTypeList"
                filled
                item-value="value"
                item-text="text"
                name="logTypeSelected"
                :label="$t('log.logType')"
                class="section-high-class"
                :menu-props="{offsetY: true}"
                dense
              />
            </v-col>
            <v-col cols="2">
              <v-select
                v-model="deviceTypeSelected"
                :items="deviceTypeList"
                filled
                item-value="value"
                item-text="text"
                name="deviceTypeSelected"
                :label="$t('log.deviceType')"
                class="section-high-class"
                @input="deviceSelectRoute"
                :menu-props="{offsetY: true}"
                dense
              />
            </v-col>
            <v-chip
              v-show="!isSpecificDeviceTypeNotSelected && selectedDeviceName !== null"
              class="ml-3 mb-6 text-lg-h4"
              color="primary"
              text-color="white"
              large
            >
              {{ $t('log.selected') }}：{{ selectDeviceType + '--' + selectedDeviceName }}
            </v-chip>
            <!-- 日期选择 -->
            <v-col cols="3">
              <v-row>
                <v-col cols="6">
                  <v-menu
                    ref="menu"
                    v-model="menu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="date"
                        :label="$t('log.startDate')"
                        prepend-icon="mdi-calendar-month-outline"
                        readonly
                        v-bind="attrs"
                        hide-details
                        class="pb-6"
                        v-on="on"
                      />
                    </template>
                    <v-date-picker v-model="date" scrollable :max="currentDate" @input="menu = false" first-day-of-week="1" locale="zh-cn"/>
                  </v-menu>
                </v-col>
                <v-col cols="6">
                  <v-menu
                    ref="menu2"
                    v-model="menu2"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="date2"
                        :label="$t('log.endDate')"
                        prepend-icon="mdi-calendar-month-outline"
                        readonly
                        v-bind="attrs"
                        hide-details
                        class="pb-6"
                        v-on="on"
                      />
                    </template>
                    <v-date-picker v-model="date2" scrollable :min="getMinEndDate" :max="getMaxEndDate" @input="menu2 = false" first-day-of-week="1" locale="zh-cn"/>
                  </v-menu>
                </v-col>
              </v-row>
            </v-col>
            <!-- 按钮功能 -->
            <v-col cols="2" class="pb-7">
              <v-btn
                color="primary"
                :loading="queryLogLoading"
                :disabled="queryLogLoading"
                @click="selectLog"
              >
                {{ $t('log.query') }}
              </v-btn>
              <a v-show="exportFilePath !== null && this.logEntries.length > 0" :href="exportFilePath" download>
                <v-btn
                  color="primary"
                >
                  {{ $t('log.downloadCurrentLog') }}
                </v-btn>
              </a>
            </v-col>
              </v-row>
              <v-data-table
          v-model="logSelected"
          :headers="logHeaders"
          :items="logEntries"
          :search="logSearch"
          sort-by.sync="['id']"
          item-key="id"
          class="elevation-1 mt-n4"
          single-select
          :items-per-page="10"
          :page.sync="tablePage"
          :footer-props="{
            itemsPerPageOptions: [10,25,50,100],
            showFirstLastPage: true,
            showCurrentPage: true,
            firstIcon: 'mdi-arrow-collapse-left',
            lastIcon: 'mdi-arrow-collapse-right',
            prevIcon: 'mdi-minus',
            nextIcon: 'mdi-plus'
          }"
              >
                <template v-slot:item.LogType="{ item }">
                  <span>{{ getLogTypeName(item.LogType) }}</span>
                </template>
              </v-data-table>
            </v-tab-item>
            <!-- 通话录音TAB -->
             <v-tab-item v-if="!isCloudIpSystem()">
               <v-row class="mt-3">
                 <v-col cols="12">
                   <v-card class="mb-3">
                     <v-card-title>
                       {{ $t('log.callRecord.autoRecordSwitch') }}
                     </v-card-title>
                     <v-card-text>
                       <v-switch
                         v-model="localCallRecordSwitch"
                         :label="localCallRecordSwitch ? $t('common.enabled') : $t('common.disabled')"
                         @change="setCallRecordSwitch"
                       />
                     </v-card-text>
                   </v-card>
                 </v-col>
               </v-row>
               <v-row>
                 <v-col cols="2">
                   <v-select
                     v-model="callRecordDeviceTypeSelected"
                     :items="callRecordDeviceTypeList"
                     filled
                     item-value="value"
                     item-text="text"
                     name="callRecordDeviceTypeSelected"
                     :label="$t('log.callerDevice')"
                     class="section-high-class"
                     @input="onCallRecordDeviceTypeChange"
                     :menu-props="{offsetY: true}"
                     dense
                   />
                 </v-col>
                 <v-chip
                   v-show="!isCallRecordSpecificDeviceTypeNotSelected && callRecordSelectedDeviceName !== null"
                   class="ml-3 mb-6 text-lg-h4"
                   color="primary"
                   text-color="white"
                   large
                 >
                   {{ $t('log.selected') }}：{{ callRecordSelectDeviceType + '--' + callRecordSelectedDeviceName }}
                 </v-chip>
                 <v-col cols="3">
                   <v-menu
                     ref="startDateMenu"
                     v-model="startDateMenu"
                     :close-on-content-click="false"
                     transition="scale-transition"
                     offset-y
                     min-width="auto"
                   >
                     <template v-slot:activator="{ on, attrs }">
                       <v-text-field
                         v-model="startDate"
                         :label="$t('log.startDate')"
                         prepend-icon="mdi-calendar"
                         readonly
                         v-bind="attrs"
                         v-on="on"
                       />
                     </template>
                     <v-date-picker
                       v-model="startDate"
                       :max="getMaxStartDate"
                       :min="getMinStartDate"
                       @input="startDateMenu = false"
                     />
                   </v-menu>
                 </v-col>
                 <v-col cols="3">
                   <v-menu
                     ref="endDateMenu"
                     v-model="endDateMenu"
                     :close-on-content-click="false"
                     transition="scale-transition"
                     offset-y
                     min-width="auto"
                   >
                     <template v-slot:activator="{ on, attrs }">
                       <v-text-field
                         v-model="endDate"
                         :label="$t('log.endDate')"
                         prepend-icon="mdi-calendar"
                         readonly
                         v-bind="attrs"
                         v-on="on"
                       />
                     </template>
                     <v-date-picker
                       v-model="endDate"
                       :min="startDate"
                       :max="getCurrentDate"
                       @input="endDateMenu = false"
                     />
                   </v-menu>
                 </v-col>
                 <v-col cols="2">
                   <v-btn
                     color="primary"
                     @click="queryCallLogs"
                   >
                     {{ $t('log.query') }}
                   </v-btn>
                 </v-col>
               </v-row>
               <v-data-table
                 v-model="callLogSelected"
                 :headers="callLogHeaders"
                 :items="localCallLogs"
                 :items-per-page="10"
                 class="elevation-1"
                 :footer-props="{
                   showFirstLastPage: true,
                   firstIcon: 'mdi-arrow-collapse-left',
                   lastIcon: 'mdi-arrow-collapse-right',
                   prevIcon: 'mdi-minus',
                   nextIcon: 'mdi-plus'
                 }"
               >
                 <template v-slot:item.serial_number="{ index }">
                   {{ index + 1 }}
                 </template>
                 <template v-slot:item.file_size="{ item }">
                   {{ formatFileSize(item.file_size) }}
                 </template>
                 <template v-slot:item.record_type="{ item }">
                   {{ formatCallType(item.record_type) }}
                 </template>
                 <template v-slot:item.record_status="{ item }">
                   {{ formatRecordStatus(item.record_status) }}
                 </template>
                 <template v-slot:item.callee_list="{ item }">
                   <span :title="getFullCalleeList(item.callee_list)">
                     {{ formatCalleeList(item.callee_list) }}
                   </span>
                 </template>
                 <template v-slot:item.actions="{ item }">
                   <v-icon
                     v-if="item.record_status === 1"
                     large
                     @click.stop.prevent="downloadCallRecord(item)"
                     class="mx-1"
                     :title="$t('log.callRecord.download')"
                   >
                     mdi-play-box
                   </v-icon>
                   <v-icon
                     large
                     @click.stop.prevent="deleteCallRecord(item)"
                     class="mx-1"
                     :title="$t('common.delete')"
                   >
                     mdi-delete
                   </v-icon>
                 </template>
               </v-data-table>
             </v-tab-item>
          </v-tabs-items>
        </v-card-text>
      </v-card>
    </v-container>
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import { customerVersion } from '@/plugins/websocket'

  const Papa = require('papaparse')



  export default {
    name: 'Log',
    data: () => ({
      activeTab: 0,
      successMessages: '',
      commonSuccessSnackBar: false,
      errorMessages: '',
      commonErrorSnackBar: false,
      snackbarTimeout: 1500,
      logSelected: [],
      logHeaders: [],
      logEntries: [],
      logSearch: null,
      logTypeSelected: 0,
      deviceTypeSelected: -1,
      date: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 1).toISOString().substr(0, 10), // 开始日期
      date2: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 1).toISOString().substr(0, 10), // 结束日期
      currentDate: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 1).toISOString().substr(0, 10),
      menu: false,
      menu2: false,
      queryLogFlag: true, // true代表查询日志，false代表导出日志
      deviceSelectDialog: false, // 选择设备穿梭框
      deviceSelectSelectTransfer: [],
      selectedDeviceName: null,
      selectedDeviceMac: null,
      selectDeviceType: null,
      exportFilePath: null,
      queryLogLoading: false,
      tablePage: 1,
      deviceList: [],
      // 通话录音相关
      callLogSelected: [],
      callLogHeaders: [],
      startDate: null,
      endDate: null,
      startDateMenu: false,
      endDateMenu: false,
      localCallRecordSwitch: false,
      localCallLogs: [],
      // 通话录音设备类型选择相关
      callRecordDeviceTypeSelected: -1,
      callRecordDeviceSelectDialog: false,
      callRecordDeviceSelectTransfer: [],
      callRecordSelectedDeviceName: null,
      callRecordSelectedDeviceMac: null,
      callRecordSelectDeviceType: null,
      callRecordDeviceList: [],
      // 删除通话录音对话框
      deleteCallRecordDialog: false,
      deleteCallRecordItem: null
    }),
    computed: {
      ...mapState(['errorId', 'errorWsMessage', 'runLogPath', 'callRecordSwitchResult', 'queryCallLogResult']),
      ...mapGetters(['decodeZones', 'isAdmin', 'pagers', 'audioCollectors', 'fireCollectors', 'sequencePowers', 'audioMixers', 'remoteControlers', 'phoneGateways',
        'ampControlers', 'noiseDetectors' ]),
      // 未选中日志设备类型
      isSpecificDeviceTypeNotSelected() {
        return this.deviceTypeSelected === -1 || this.deviceTypeSelected === -2
      },
      logTypeList () {
        const list = [
          { text: this.$t('log.logTypes.all'), value: 0 },
          { text: this.$t('log.logTypes.runtime'), value: 1 },
          { text: this.$t('log.logTypes.playback'), value: 2 },
          { text: this.$t('log.logTypes.fire'), value: 4 },
          { text: this.$t('log.logTypes.paging'), value: 7 }]
        if (this.isAdmin) {
          list.push({ text: this.$t('log.logTypes.advanced'), value: 8 })
        }
        return list
      },
      // todo 设定最早可查询的日期
      // 获取最大结束日期，为开始日期+30天
      getMinEndDate () {
        return this.date
      },
      getMaxEndDate () {
        return this.calculateEndDate().toISOString().substr(0, 10)
      },
      deviceTypeList() {
        const list = [{text: this.$t('log.deviceTypes.allDevices'), value: -1}];
        if (this.decodeZones.length !== 0) {
          list.push({text: this.$t('deviceTypes.decodeTerminal'), value: 0})
        }
        if (this.pagers.length !== 0) {
          list.push({text: this.$t('deviceTypes.smartPager'), value: 1})
        }
        if (this.audioCollectors.length !== 0) {
          list.push({text: this.$t('deviceTypes.audioCollector'), value: 3})
        }
        if (this.fireCollectors.length !== 0) {
          list.push({text: this.$t('deviceTypes.fireCollector'), value: 4})
        }
        if (this.sequencePowers.length !== 0) {
          list.push({text: this.$t('deviceTypes.powerSequencer'), value: 5})
        }
        if (this.audioMixers.length !== 0) {
          if(customerVersion === 'C3A0') {
            list.push({text: this.$t('deviceTypes.audioCoprocessor'), value: 6})
          }
          else {
            list.push({text: this.$t('deviceTypes.audioRepeater'), value: 6})
          }
        }
        if (this.remoteControlers.length !== 0) {
          list.push({text: this.$t('deviceTypes.remoteController'), value: 7})
        }
        if (this.phoneGateways.length !== 0) {
          list.push({text: this.$t('deviceTypes.phoneGateway'), value: 8})
        }
        if (this.ampControlers.length !== 0) {
          list.push({text: this.$t('deviceTypes.ampController'), value: 9})
        }
        if (this.noiseDetectors.length !== 0) {
          list.push({text: this.$t('deviceTypes.noiseDetector'), value: 10})
        }
        return list
      },
      // 通话录音日期限制相关计算属性
      getCurrentDate() {
        return new Date().toISOString().substr(0, 10)
      },
      getMinStartDate() {
        // 最早可查询日期为当前日期前30天
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        return thirtyDaysAgo.toISOString().substr(0, 10)
      },
      getMaxStartDate() {
        // 开始日期不能超过结束日期，也不能超过当前日期
        const currentDate = this.getCurrentDate
        if (this.endDate && this.endDate < currentDate) {
          return this.endDate
        }
        return currentDate
      },
      // 通话录音设备类型列表
      callRecordDeviceTypeList() {
        const list = [{text: this.$t('log.deviceTypes.allDevices'), value: -1}];
        /*暂时只查询话筒的
        if (this.decodeZones.length !== 0) {
          list.push({text: this.$t('deviceTypes.decodeTerminal'), value: 0})
        }
        */
        if (this.pagers.length !== 0) {
          list.push({text: this.$t('deviceTypes.smartPager'), value: 1})
        }
        return list
      },
      // 未选中通话录音设备类型
      isCallRecordSpecificDeviceTypeNotSelected() {
        return this.callRecordDeviceTypeSelected === -1
      }
    },
    watch: {
      // 监听tab切换
      activeTab(newVal) {
        if (newVal === 1) { // 切换到通话录音页面
          this.queryCallRecordSwitch()
        }
      },
      // 监听语言变化
      '$i18n.locale'() {
        this.initializeTranslations()
      },
      // 统一错误处理
      errorId () {
        if (this.$route.fullPath !== '/dashboard/log') {
          return;
        }
        if (this.$store.state.errorId !== null) {
          this.queryLogLoading = false
          this.errorMessages = this.$store.state.errorWsMessage
          this.commonErrorSnackBar = true
        }
      },
      date () {
        if (new Date(this.date) > new Date(this.date2)) {
          this.date2 = this.date
          return
        }
        if (this.calculateEndDate() > new Date(this.date2)) {
          return
        }
        this.date2 = this.calculateEndDate().toISOString().substr(0, 10)
      },
      runLogPath () {
        if (this.runLogPath == null) {
          return
        }
        // 设定文件url
        // 20221123需要将特殊字符编码，否则访问url异常
        let runLogPath = this.runLogPath
        runLogPath=runLogPath.replace(/\%/g,"%25")
        runLogPath=runLogPath.replace(/\#/g,"%23")
        runLogPath=runLogPath.replace(/\&/g,"%26")
        this.exportFilePath = this.$cgiHost + '/' + runLogPath
        // console.log("exportFilePath="+this.exportFilePath)
        if (this.queryLogFlag) {
          // 获取csv文件并解析
          Papa.parse(this.exportFilePath, {
            download: true,
            header: true,
            delimiter: ',',
            complete: (results) => {
              results.data.forEach((d, index) => {
                d.id = index + 1
              })
              this.successMessages = this.$t('log.messages.queryLogSuccess')
              // 重新查询成功，表格页码回到第一页
              this.tablePage = 1
              this.commonSuccessSnackBar = true
              this.queryLogLoading = false
              this.logEntries = results.data.filter(d => d.LogContents != null && d.LogContents !== '')
            },
          })
        } else {
          // console.log('导出')
        }
        this.$store.commit('updateRunLogPath', null)
      },
      deviceTypeSelected () {
        if (this.isSpecificDeviceTypeNotSelected) {
          this.selectedDeviceName = null
          this.selectedDeviceMac = null
          this.selectDeviceType = null
        }
      },
      // 监听通话录音开关查询结果
      callRecordSwitchResult() {
        if (this.callRecordSwitchResult !== null) {
          if (this.callRecordSwitchResult.result === 0) {
            this.localCallRecordSwitch = this.callRecordSwitchResult.enable_call_record || false
            if (this.callRecordSwitchResult.set === 1) {
              this.successMessages = this.$t('common.operationSuccess')
              this.commonSuccessSnackBar = true
            }
          } else {
            this.errorMessages = this.$t('common.operationFailed')
            this.commonErrorSnackBar = true
          }
          this.$store.commit('updateCallRecordSwitchResult', null)
        }
      },
      // 监听通话录音日志查询结果
      queryCallLogResult() {
        if (this.queryCallLogResult !== null) {
          if (this.queryCallLogResult.result === 0) {
            // 处理查询结果，组合地址显示日志
            const processedLogs = (this.queryCallLogResult.call_logs || []).map(log => {
              if (log.file_path && !log.file_path.startsWith('http')) {
                log.file_path = this.$cgiHost + '/' + log.file_path
              }
              return log
            })
            this.localCallLogs = processedLogs
            this.successMessages = this.$t('log.messages.queryLogSuccess')
            this.commonSuccessSnackBar = true
          } else {
            this.errorMessages = this.$t('common.operationFailed')
            this.commonErrorSnackBar = true
          }
          this.$store.commit('updateQueryCallLogResult', null)
        }
      },
    },
    mounted () {
      this.initializeTranslations()
      this.queryLogLoading = false
      this.deviceTypeSelected = -1;
      this.callRecordDeviceTypeSelected = -1;
      // 设置默认日期为今天
      const today = new Date().toISOString().substr(0, 10)
      this.startDate = today
      this.endDate = today
    },
    methods: {
      initializeTranslations() {
        this.successMessages = this.$t('common.operationSuccess')
        this.errorMessages = this.$t('common.operationFailed')
        // 初始化表格头部
        this.logHeaders = [
          { text: this.$t('log.tableHeaders.serialNumber'), value: 'id', align: 'center', sortable: true, width: '5%' },
          { text: this.$t('log.tableHeaders.date'), value: 'Date', align: 'center', sortable: false, width: '10%' },
          { text: this.$t('log.tableHeaders.time'), value: 'Time', align: 'center', sortable: false, width: '10%' },
          { text: this.$t('log.tableHeaders.deviceNameOrUser'), value: 'Name', align: 'center', sortable: false, width: '15%' },
          { text: this.$t('log.tableHeaders.logType'), value: 'LogType', align: 'center', sortable: false, width: '15%' },
          { text: this.$t('log.tableHeaders.logContent'), value: 'LogContents', align: 'center', sortable: false, width: '50%' },
        ]
        // 初始化通话录音表格头部
        this.initializeCallRecordHeaders()
      },
      selectLog () {
        if (this.date == null || this.date2 == null) {
          this.errorMessages = this.$t('log.messages.pleaseSelectLogDate')
          this.commonErrorSnackBar = true
          return
        }
        if (!this.isSpecificDeviceTypeNotSelected && this.selectedDeviceName === null) {
          this.errorMessages = this.$t('log.messages.pleaseSelectDeviceOrSwitchMode')
          this.commonErrorSnackBar = true
          return
        }
        this.queryLogFlag = true
        // 1. 获取csv文件
        const filePath = this.getExportFilePath()
        const deviceName = this.isSpecificDeviceTypeNotSelected ? '' : this.selectedDeviceName
        const deviceMac = this.isSpecificDeviceTypeNotSelected ? '' : this.selectedDeviceMac
        this.$ws.exportRunLogs(this.logTypeSelected, this.date, this.date2, deviceName, deviceMac, filePath)
        this.queryLogLoading = true
        // 2. 解析csv文件，生成data对象
      },
      exportLog () {
        this.queryLogFlag = false
      },
      // 生成导出文件名称, 默认格式为RunLog_设备名_开始日期_结束日期_随机6位.csv
      getExportFilePath () {
        // const deviceName = '' // 默认为所有设备
        const randomNum = ('000000' + Math.floor(Math.random() * 999999)).slice(-6)
        let deviceName = 'AllDevice'
        if (!this.isSpecificDeviceTypeNotSelected && this.selectedDeviceName !== null) {
          deviceName = this.selectedDeviceName
        }
        const list = ['RunLog', deviceName, this.date, this.date2, randomNum]
        return list.join('_') + '.csv'
      },
      getLogTypeName (logType) {
        const logTypeMap = new Map([
          ['0', this.$t('log.logTypes.all')],
          ['1', this.$t('log.logTypes.runtime')],
          ['2', this.$t('log.logTypes.playback')],
          ['3', this.$t('log.logTypes.sync')],
          ['4', this.$t('log.logTypes.fire')],
          ['5', this.$t('log.logTypes.call')],
          ['6', this.$t('log.logTypes.monitor')],
          ['7', this.$t('log.logTypes.paging')],
          ['8', this.$t('log.logTypes.advanced')],
        ])
        return logTypeMap.get(logType)
      },
      // 指定设备时弹出选择穿梭框
      deviceSelectRoute (selectObj) {
        this.deviceSelectSelectTransfer = []
        if (selectObj === -1 || selectObj === -2) {
          return
        }
        switch (selectObj) {
          case 0:
            this.deviceList = this.decodeZones
            break;
          case 1:
            this.deviceList = this.pagers
            break;
          case 3:
            this.deviceList = this.audioCollectors
            break;
          case 4:
            this.deviceList = this.fireCollectors
            break;
          case 5:
            this.deviceList = this.sequencePowers
            break;
           case 6:
            this.deviceList = this.audioMixers
            break;
          case 7:
            this.deviceList = this.remoteControlers
            break;
          case 8:
            this.deviceList = this.phoneGateways
            break;
          case 9:
            this.deviceList = this.ampControlers
            break;
          case 10:
            this.deviceList = this.noiseDetectors
            break;
          default:
            return;
        }
        this.deviceSelectDialog = true
      },
      saveDeviceSelected () {
        if (this.deviceSelectSelectTransfer.length === 0) {
          this.errorMessages = this.$t('log.messages.pleaseSelectOneDevice')
          this.commonErrorSnackBar = true
          return
        } else if (this.deviceSelectSelectTransfer.length > 1) {
          this.errorMessages = this.$t('log.messages.canOnlySelectSingleDevice')
          this.commonErrorSnackBar = true
          return
        }
        const zone = this.$ws.getZoneByZoneMac(this.deviceSelectSelectTransfer[0])
        this.selectedDeviceName = zone != null ? zone.name : null
        this.selectedDeviceMac = zone != null ? zone.mac : null
        switch (this.deviceTypeSelected) {
          case 0: this.selectDeviceType = this.$t('deviceTypes.decodeTerminal'); break
          case 1: this.selectDeviceType = this.$t('deviceTypes.smartPager'); break
          case 3: this.selectDeviceType = this.$t('deviceTypes.audioCollector'); break
          case 4: this.selectDeviceType = this.$t('deviceTypes.fireCollector'); break
          case 5: this.selectDeviceType = this.$t('deviceTypes.powerSequencer'); break
          case 6: {
            if(customerVersion === 'C3A0') {
              this.selectDeviceType = this.$t('deviceTypes.audioCoprocessor');
            }
            else {
              this.selectDeviceType = this.$t('deviceTypes.audioRepeater');
            }
            break
          }
          case 7: this.selectDeviceType = this.$t('deviceTypes.remoteController'); break
          case 8: this.selectDeviceType = this.$t('deviceTypes.phoneGateway'); break
          case 9: this.selectDeviceType = this.$t('deviceTypes.ampController'); break
          case 10: this.selectDeviceType = this.$t('deviceTypes.noiseDetector'); break
          default: this.selectDeviceType = ''
        }
        this.deviceSelectDialog = false
      },
      // 格式化时间，由2021-01-21 >>> 2021/01/21 (暂不使用）
      formatDate (date) {
        if (!date) return null

        const [year, month, day] = date.split('-')
        return `${month}/${day}/${year}`
      },
      parseDate (date) {
        if (!date) return null

        const [month, day, year] = date.split('/')
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
      },
      calculateEndDate () {
        const startDate = new Date(this.date)
        const nextMonthDate = new Date(startDate.setMonth(startDate.getMonth() + 1))
        return new Date(this.currentDate) > nextMonthDate ? nextMonthDate : new Date(this.currentDate)
      },
      // 通话录音相关方法
      initializeCallRecordHeaders() {
        this.callLogHeaders = [
          { text: this.$t('log.callRecord.serialNumber'), value: 'serial_number', align: 'center', sortable: true, width: '8%' },
          // { text: this.$t('log.callRecord.callId'), value: 'call_id', align: 'center', sortable: false, width: '15%' }, // 隐藏通话ID
          { text: this.$t('log.callRecord.callerName'), value: 'caller_name', align: 'center', sortable: false, width: '12%' },
          { text: this.$t('log.callRecord.callerMac'), value: 'caller_mac', align: 'center', sortable: false, width: '8%' },
          { text: this.$t('log.callRecord.calleeList'), value: 'callee_list', align: 'center', sortable: false, width: '12%' },
          { text: this.$t('log.callRecord.startTime'), value: 'start_time', align: 'center', sortable: false, width: '9%' },
          { text: this.$t('log.callRecord.endTime'), value: 'end_time', align: 'center', sortable: false, width: '9%' },
          { text: this.$t('log.callRecord.duration'), value: 'duration', align: 'center', sortable: false, width: '8%' },
          { text: this.$t('log.callRecord.fileSize'), value: 'file_size', align: 'center', sortable: false, width: '8%' },
          { text: this.$t('log.callRecord.callType'), value: 'record_type', align: 'center', sortable: false, width: '8%' },
          { text: this.$t('log.callRecord.recordStatus'), value: 'record_status', align: 'center', sortable: false, width: '8%' },
          { text: this.$t('common.actions'), value: 'actions', align: 'center', sortable: false, width: '14%' }
        ]
      },
      queryCallRecordSwitch() {
        this.$ws.callRecordSwitch(0, null)
      },
      setCallRecordSwitch() {
        this.$ws.callRecordSwitch(1, this.localCallRecordSwitch)
      },
      queryCallLogs() {
        if (!this.startDate || !this.endDate) {
          this.errorMessages = this.$t('log.messages.pleaseSelectLogDate')
          this.commonErrorSnackBar = true
          return
        }
        // 根据设备选择情况决定是否传递caller_mac参数
        if (this.isCallRecordSpecificDeviceTypeNotSelected || !this.callRecordSelectedDeviceMac) {
          // 查询所有设备，不传caller_mac
          this.$ws.queryCallLog(this.startDate, this.endDate, null)
        } else {
          // 查询特定设备，传递caller_mac
          this.$ws.queryCallLog(this.startDate, this.endDate, this.callRecordSelectedDeviceMac)
        }
      },
      downloadCallRecord(item) {
        if (item.file_path) {
          // 创建一个隐藏的a标签来触发下载
          const link = document.createElement('a')
          link.href = item.file_path
          link.download = item.file_path.split('/').pop() || `call_record_${item.record_id}.wav`
          link.target = '_blank'
          link.style.display = 'none'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        } else {
          this.errorMessages = this.$t('log.callRecord.downloadError')
          this.commonErrorSnackBar = true
        }
      },
      playCallRecord(item) {
        if (item.file_path) {
          // 使用已经组合好的完整地址
          const audio = new Audio(item.file_path)
          audio.play().catch(error => {
            this.errorMessages = this.$t('log.callRecord.playError')
            this.commonErrorSnackBar = true
          })
        }
      },
      formatFileSize(bytes) {
        if (!bytes || bytes === 0) return '0 B'
        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      },
      // 格式化录音状态
      formatRecordStatus(status) {
        switch (status) {
          case 0: return this.$t('log.callRecord.recordStatusRecording')    // 录音中
          case 1: return this.$t('log.callRecord.recordStatusCompleted')   // 录音完成
          case 2: return this.$t('log.callRecord.recordStatusError')       // 录音错误
          case 3: return this.$t('log.callRecord.recordStatusInterrupted') // 录音中断
          default: return this.$t('common.unknown')
        }
      },
      // 格式化通话类型
      formatCallType(type) {
        switch (type) {
          case 0: return this.$t('log.callRecord.callTypeBroadcast')    // 广播寻呼
          case 1: return this.$t('log.callRecord.callTypeIntercom')    // 双向对讲
          default: return this.$t('common.unknown')
        }
      },
      // 通过MAC地址获取设备名称
      getDeviceNameByMac(mac) {
        if (!mac) return ''
        const device = this.decodeZones.find(zone => zone.mac === mac)
        return device ? device.name : mac
      },
      // 格式化被叫列表，将MAC地址转换为设备名称
      formatCalleeList(calleeList) {
        if (!calleeList) return ''
        const macList = calleeList.split(',')
        const nameList = macList.map(mac => this.getDeviceNameByMac(mac.trim()))
        
        if (nameList.length >= 2) {
          return this.$t('log.callRecord.deviceListEllipsis', {
            firstDevice: nameList[0],
            count: nameList.length
          })
        }
        return nameList.join(', ')
      },
      // 获取完整的被叫列表（用于悬浮提示）
      getFullCalleeList(calleeList) {
        if (!calleeList) return ''
        const macList = calleeList.split(',')
        const nameList = macList.map(mac => this.getDeviceNameByMac(mac.trim()))
        return nameList.join(', ')
      },
      // 通话录音设备类型选择相关方法
      onCallRecordDeviceTypeChange(selectObj) {
        this.callRecordDeviceSelectTransfer = []
        if (selectObj === -1) {
          this.callRecordSelectedDeviceName = null
          this.callRecordSelectedDeviceMac = null
          this.callRecordSelectDeviceType = null
          return
        }
        switch (selectObj) {
          case 0:
            this.callRecordDeviceList = this.decodeZones
            break;
          case 1:
            this.callRecordDeviceList = this.pagers
            break;
          default:
            return;
        }
        this.callRecordDeviceSelectDialog = true
      },
      saveCallRecordDeviceSelected() {
        if (this.callRecordDeviceSelectTransfer.length === 0) {
          this.errorMessages = this.$t('log.messages.pleaseSelectOneDevice')
          this.commonErrorSnackBar = true
          return
        } else if (this.callRecordDeviceSelectTransfer.length > 1) {
          this.errorMessages = this.$t('log.messages.canOnlySelectSingleDevice')
          this.commonErrorSnackBar = true
          return
        }
        const zone = this.$ws.getZoneByZoneMac(this.callRecordDeviceSelectTransfer[0])
        this.callRecordSelectedDeviceName = zone != null ? zone.name : null
        this.callRecordSelectedDeviceMac = zone != null ? zone.mac : null
        switch (this.callRecordDeviceTypeSelected) {
          case 0: this.callRecordSelectDeviceType = this.$t('deviceTypes.decodeTerminal'); break
          case 1: this.callRecordSelectDeviceType = this.$t('deviceTypes.smartPager'); break
          default: this.callRecordSelectDeviceType = ''
        }
        this.callRecordDeviceSelectDialog = false
      },
      // 设备过滤方法
      filterZoneByIpOrName(query, item) {
        return item.name.toLowerCase().indexOf(query.toLowerCase()) > -1 ||
               item.ip.toLowerCase().indexOf(query.toLowerCase()) > -1
      },
      // 判断是否为云端IP系统 - 方法已在generalMixin中定义，这里不需要重复定义
      // 删除通话录音
      deleteCallRecord(item) {
        this.deleteCallRecordItem = item
        this.deleteCallRecordDialog = true
      },
      // 确认删除通话录音
      confirmDeleteCallRecord() {
        if (this.deleteCallRecordItem) {
          // 调用WebSocket删除通话日志
          this.$ws.deleteCallLog(this.deleteCallRecordItem.call_id)
          this.successMessages = this.$t('common.deleteSuccess')
          this.commonSuccessSnackBar = true
          // 删除成功后刷新列表
          this.queryCallLogs()
        }
        this.deleteCallRecordDialog = false
        this.deleteCallRecordItem = null
      },
    },
  }
</script>

<style scoped>
  .section-high-class >>> .v-select__selection {
    border-top-width: 3px;
    margin-top: 6px;
  }
  /deep/ .v-card {
    border-radius: 6px !important;
    margin-top: 0px !important;
  }
</style>
