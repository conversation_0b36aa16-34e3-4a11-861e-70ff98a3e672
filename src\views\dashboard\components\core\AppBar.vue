<template>
  <v-app-bar
    id="app-bar"
    absolute
    app
    color="transparent"
    flat
    height="75"
  >
    <!--导航栏收起快捷键-->
    <v-btn
      class="mr-3"
      elevation="1"
      fab
      small
      :title="drawer ? $t('appbar.collapseNavbar') : $t('appbar.showNavbar')"
      light
      @click="setDrawer(!drawer)"
    >
      <v-icon v-if="drawer">
        mdi-unfold-less-vertical
      </v-icon>
      <v-icon v-if="!drawer">
        mdi-unfold-more-vertical
      </v-icon>
    </v-btn>

    <v-toolbar-title
      class="hidden-sm-and-down font-weight-bold"
      v-text="routeDisplayName"
    />
    <v-spacer />

    <v-chip
      class="ml-16 pl-3 text-h4"
      color="primary"
      text-color="white"
      large
    >
      <v-avatar left>
        <v-icon>mdi-alarm</v-icon>
      </v-avatar>
      {{ timestamp }}
    </v-chip>
    <v-spacer />

      <template
        v-if="$vuetify.breakpoint.mdAndUp"
        v-slot:append-outer
      >
        <v-btn
          class="mt-n2"
          elevation="1"
          fab
          small
          :disabled="$route.name !== $t('appbar.controlCenter')"
        >
          <v-icon>mdi-magnify</v-icon>
        </v-btn>
      </template>

    <div>

      <!-- 语言选择器 -->
      <LanguageSelector
        button-class="ml-2"
        @language-changed="onLanguageChanged"
      />

       <!-- 新增云控制按钮 -->
      <v-btn
        class="ml-2"
        min-width="0"
        text
        :title="$t('appbar.cloudControl')"
        @click="preShowQrDialog"
        v-if = "isShowCloudControl()"
      >
        <v-icon>mdi-cloud</v-icon>
      </v-btn>

      <!-- ... existing buttons ... -->
      </div>

      <!-- 新增二维码对话框 -->
      <v-dialog v-model="showQrDialog" max-width="600">
        <v-card>
          <v-card-title class="headline">
            <v-icon left :color="cloud_isConnected ? 'green' : 'gray'" size="35">mdi-wechat</v-icon>
            {{ $t('appbar.wechatScan') }}
          </v-card-title>
          <v-card-text class="text-center">
            <qrcode-vue
              v-if="cloudSwitch"
              :value="qrContent"
              :size="400"
              level="H"
            />
          </v-card-text>
          <v-card-actions class="justify-center">
            <v-btn
              v-show="isAdmin"
              color="primary"
              @click="confirmToggleCloudControl"
            >
              {{ cloudSwitch ? $t('appbar.disableCloudControl') : $t('appbar.enableCloudControl') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- 确认对话框 -->
      <v-dialog v-model="confirmDialog" max-width="400">
        <v-card>
          <v-card-title class="headline">{{ $t('common.confirm') }}</v-card-title>
          <v-card-text>
            {{ $t('common.confirm') }}{{ cloudSwitch ? $t('appbar.disableCloudControl') : $t('appbar.enableCloudControl') }}？
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="confirmDialog = false">{{ $t('common.cancel') }}</v-btn>
            <v-btn color="error" @click="toggleCloudControl">{{ $t('common.confirm') }}</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- 新增云控制授权对话框 -->
      <v-dialog v-model="showAuthDialog" max-width="500">
        <v-card>
          <v-card-title class="headline">
            {{ $t('appbar.cloudControl') }}
          </v-card-title>
          <v-card-text>
            <v-text-field
              :label="$t('common.machineCode')"
              :value="cloudControlMachineCode"
              readonly
              outlined
            ></v-text-field>
            <v-text-field
              :label="$t('common.enterRegistrationCode')"
              v-model="registrationCode"
              outlined
            ></v-text-field>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="showAuthDialog = false">{{ $t('common.cancel') }}</v-btn>
            <v-btn color="primary" @click="validateRegistrationCode">{{ $t('common.confirm') }}</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-btn
        class="ml-2"
        min-width="0"
        text
        to="/dashboard/dashboard"
        :title="$t('appbar.controlCenter')"
      >
        <v-icon>mdi-monitor-dashboard</v-icon>
      </v-btn>

      <v-btn
        v-show="isAdmin"
        class="ml-2"
        min-width="0"
        text
        to="/dashboard/account"
        :title="$t('appbar.accountManagement')"
      >
        <v-icon>mdi-account</v-icon>
      </v-btn>

      <!--增加退出按钮-->
      <v-btn
        class="ml-2"
        min-width="0"
        text
        to="/"
        :title="$t('appbar.logout')"
      >
        <v-icon @click="logout">
          mdi-logout
        </v-icon>
      </v-btn>
      <v-tooltip bottom nudge-left="45">
        <template v-slot:activator="{ attrs, on }">
          <span class="mt-n2 text-lg-h3 pl-3 mr-2" v-bind="attrs" v-on="on">{{ usernameAndType }}</span>
        </template>
        <span>{{ $t('appbar.currentUser') }}</span>
      </v-tooltip>
    </div>
  </v-app-bar>
</template>

<!-- 全局css设置处 -->
<style>
  /* todo 设置时间大小 */
  .v-chip__content {
    font-size: medium;
  }
  .v-select__selection--comma {
    /*margin: -5px 4px 7px 0;*/
    margin-left: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .v-data-footer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 0.99rem;
    padding: 0 8px;
  }
  .v-data-footer__select .v-select__selections .v-select__selection--comma {
    font-size: 0.95rem;
  }
  /*修改表格的样式*/
  .v-data-table header {
    font-size: 16px !important;
    font-weight: 400;
  }

  .v-data-table th {
    font-size: 17px !important;
    font-weight: 400;
  }

  .v-data-table td {
    font-size: 16px !important;
    font-weight: 500;
  }
  .offlineZone {
    background: #e9eaea;
  }
  .darkOfflineZone {
    background: #c0c4cc;
  }
  .onlineZone {
    background: white;
  }
  /*修改文本框label的字体大小*/
  .v-input .v-label{
    font-size: 1.0em !important;
    font-weight: bold;
  }
  .v-list-group__activator p {
    margin-bottom: 0;
  }
  .v-slider__thumb-label {
    font-size: 1.0em;
  }
  .v-text-field__slot {
    padding-top: 0;
    padding-bottom: 0;
  }
  /*20200905 增加文字滚动效果*/
  .one-line {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .animate {
    display: inline-block;
    white-space: nowrap;
    width: 160px;
    animation: 6s wordsLoop linear infinite normal;
  }
  @keyframes wordsLoop {
    0% {
      transform: translateX(130px);
      -webkit-transform: translateX(130px);
    }
    100% {
      transform: translateX(-100%);
      -webkit-transform: translateX(-100%);
    }
  }

  @-webkit-keyframes wordsLoop {
    0% {
      transform: translateX(130px);
      -webkit-transform: translateX(130px);
    }
    100% {
      transform: translateX(-100%);
      -webkit-transform: translateX(-100%);
    }
  }

  .v-pagination {
    justify-content: flex-end;
    /*padding-right: 60px;*/
  }
  /* todo 当没有选择分区时候，显示该footer，当选择分区的时候，显示全局控制框体 */
  .selectedList {
    color: #00cae3;
  }
  /*分页栏页码之间间距*/
  .v-pagination__item {
    margin-left: 10px;
    margin-right: 10px;
    font-size: 20px;
    min-width: 40px;
  }
  .v-pagination__navigation {
    margin-left: 10px;
    margin-right: 10px;
    font-size: 20px;
    min-width: 40px;
  }
  /*调整文本框中的文字居中显示*/
  .centered-input input {
    text-align: center
  }
  /*修改所有v-label的灰度*/
  .theme--light.v-label {
    color: rgb(21 21 20 / 75%);
  }
  /*修改所有按钮的文字大小*/
  .v-btn.v-size--default {
    font-size: 1.10rem;
  }
  .v-date-picker-table {
    position: relative;
    padding: 0 12px;
    height: 220px;
  }
  /* el-transfer调整 */
  .transfer-footer {
    margin-left: 20px;
    padding: 6px 5px;
  }
  .el-transfer-panel{
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    display: inline-block;
    vertical-align: middle;
    /*width: 40vh;*/ /* 如果改成vh样式，会随着缩放自适应，但是框体会比较宽造成观赏性差，宽度推荐还是使用固定px值，此值以文字内容长度为准 */
    width: 250px;
    height: 45vh; /* 高度推荐使用vh自适应，能够适配不同分辨率的情况，此值需大于最下方的.el-transfer-panel__list.is-filterable */
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    text-align: left;
  }
  /*用于调整分组编辑框体的高度*/
  .el-transfer-panel__list.is-filterable {
    height: 35vh;
  }
</style>

<script>
  // Components
  import { VHover, VListItem } from 'vuetify/lib'

  import QrcodeVue from 'qrcode.vue'
  import LanguageSelector from '@/components/LanguageSelector.vue'

  // Utilities
  import { mapState, mapMutations, mapGetters } from 'vuex'

  import { cloud_control_switch, customerVersion, cloud_control_register } from '@/plugins/websocket'

  export default {
    name: 'DashboardCoreAppBar',

    components: {
      AppBarItem: {
        render (h) {
          return h(VHover, {
            scopedSlots: {
              default: ({ hover }) => {
                return h(VListItem, {
                  attrs: this.$attrs,
                  class: {
                    'black--text': !hover,
                    'white--text primary elevation-12': hover,
                  },
                  props: {
                    activeClass: '',
                    dark: hover,
                    link: true,
                    ...this.$attrs,
                  },
                }, this.$slots.default)
              },
            },
          })
        },
      },
      QrcodeVue,
      LanguageSelector
    },

    props: {
      value: {
        type: Boolean,
        default: false,
      },
    },

    data: () => ({
      showQrDialog: false,
      qrContent: '',
      cloudSwitch: true,
      confirmDialog: false,
      showAuthDialog: false,  // 新增授权对话框状态
      registrationCode: ''    // 新增注册码字段
    }),

    computed: {
      ...mapState(['drawer', 'timestamp', 'user', 'serverId', 'user_uuid', 'cloud_control_switch', 'cloud_isConnected', 'cloud_control_valid', 'cloudControlMachineCode']),
      ...mapGetters(['isAdmin']),
      usernameAndType: function () {
        // return this.user === 'admin' ? this.user + '(管理员)' : this.user + '(操作员)'
        /* [********] 取消控制中心 右上角 管理员/操作员显示，只显示用户名 */
        return this.user
      },
      routeDisplayName() {
        // 路由名称到国际化键的映射
        const routeNameMap = {
          '控制中心': 'routes.controlCenter',
          '设备管理': 'routes.deviceManagement', 
          '媒体管理': 'routes.mediaManagement',
          '定时管理': 'routes.timerManagement',
          '账户管理': 'routes.accountManagement',
          '监控管理': 'routes.monitorManagement',
          '日志管理': 'routes.logManagement',
          '系统设置': 'routes.systemSettings',
          '系统维护': 'routes.systemMaintenance',
          '信息发布': 'routes.informationPublish',
          '出错啦': 'routes.error'
        };

        const currentRouteName = this.$route.name;
        const i18nKey = routeNameMap[currentRouteName];
        
        // 如果找到对应的国际化键，使用国际化翻译，否则返回原始路由名称
        return i18nKey ? this.$t(i18nKey) : currentRouteName;
      },
      /*
      wechatStatusColor() {
        return this.cloud_isConnected ? 'green' : 'gray'
      }
      */
    },

    watch: {
      cloud_control_switch: function () {
        this.cloudSwitch = this.cloud_control_switch
      },
    },

    methods: {
      ...mapMutations({
        setDrawer: 'SET_DRAWER',
      }),
      logout: function () {
        this.$store.commit('logout')
        this.$store.commit('updateLogoutBySelf', true)
        this.$router.push('/')
      },
      confirmToggleCloudControl() {
        this.confirmDialog = true;
      },
      toggleCloudControl() {
        this.cloudSwitch = !this.cloudSwitch;
        this.confirmDialog = false;
        cloud_control_switch(true, this.cloudSwitch)
      },
      preShowQrDialog() {
         if (!this.cloud_control_valid) {
          this.registrationCode='';
          this.showAuthDialog = true;
          return;
        }
        this.cloudSwitch = this.cloud_control_switch
        cloud_control_switch(false, false)
        if(customerVersion === 'C8A0')
        {
          this.qrContent = `http://mhitech.com.cn:2223/mini-api-C8A0/~~${this.user_uuid}~${this.serverId}~${this.user}~~`;
        }
        else
        {
          this.qrContent = `http://mhitech.com.cn:2223/mini-api/~~${this.user_uuid}~${this.serverId}~${this.user}~~`;
        }
        this.showQrDialog = true;
      },
      validateRegistrationCode() {
        cloud_control_register(this.registrationCode)
        this.showAuthDialog = false
      },
      isShowCloudControl() {
        return customerVersion === 'C0A0' || customerVersion === 'C0A1' || customerVersion === 'C8A0';
      },
      onLanguageChanged(langCode) {
        // 语言变更时的回调处理
        //console.log('Language changed to:', langCode)
      }
    }
  }
</script>
