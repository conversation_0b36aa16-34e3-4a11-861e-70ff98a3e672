<!-- v-data-table排序rowHandler -->
<template>
  <tr :class="itemClass" @click="onClick">
    <!--  默认表格内容align为text-center  -->
    <td v-for="(header, index) in headers" :key="index" class="text-center">
      <slot :item="item" :name="columnName(header)">
        <div>
            {{ getNonSlotValue(item, header) }}
        </div>
      </slot>
    </td>
  </tr>
</template>

<script>
import {mapState} from 'vuex'

export default {
  name: "DataTableRowHandler",
  components: {},
  props: {
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    headers: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      itemClass: '',
    };
  },
  computed: {
    ...mapState(['zoneMacSelectedForSort']),
  },
  methods: {
    columnName(header) {
      return `item.${header.value}`;
    },
    getNonSlotValue(item, header) {
      const val = item[header.value];

      if (val) {
        return val;
      }

      return "";
    },
    onClick() {
      if (this.zoneMacSelectedForSort == null || this.zoneMacSelectedForSort !== this.item.mac) {
        this.$store.commit('updateZoneMacSelectedForSort', this.item.mac)
        this.itemClass = 'selected-zone'
      } else {
        this.$store.commit('updateZoneMacSelectedForSort', null)
        this.itemClass = this.item.source === -1 ? 'offlineZone' : 'onlineZone';
      }
    }
  },
  watch: {
    zoneMacSelectedForSort() {
      if (this.item.mac === this.zoneMacSelectedForSort) {
        return
      }
      this.itemClass = this.item.source === -1 ? 'offlineZone' : 'onlineZone';
    }
  },
  mounted() {
    this.itemClass = this.item.source === -1 ? 'offlineZone' : 'onlineZone';
  }
};
</script>

<style scoped>
.v-table tr:hover:not(.v-table__expanded__content) {
  background: red !important;
}
.selected-zone {
  background: #c2c9f3 !important;
}
</style>
