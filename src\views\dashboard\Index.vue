<template>
  <v-app>
    <dashboard-core-app-bar />

    <dashboard-core-drawer />

    <dashboard-core-view />

    <dashboard-core-settings />

    <v-snackbar
      v-model="snackbar"
      color="error"
      :timeout="snackbarTimeout"
      absolute
      centered
      multi-line
      style="z-index: 999"
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="error" fab small class="ml-6" v-bind="attrs" @click="snackbar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <v-snackbar
      v-model="successSnackbar"
      color="primary"
      :timeout="snackbarTimeout"
      absolute
      centered
      multi-line
      style="z-index: 999"
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ successMessage }}
      <template v-slot:action="{ attrs }">
        <v-btn color="primary" fab small class="ml-6" v-bind="attrs" @click="successSnackbar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <v-overlay :value="overlay" :opacity="0.7" z-index="998">
      <span style="font-weight: 500; font-size: 1.50em">{{ $t('index.serverDisconnectedReconnecting') }}</span>
      <v-progress-circular indeterminate size="120" :width="12" color="primary" :rotate="-90" class="ml-4"/>
    </v-overlay>
    <v-overlay :value="restartOverlay" :opacity="0.7" z-index="998">
      <span style="font-weight: 500; font-size: 1.50em">{{ $t('index.serverRestarting') }}</span>
      <v-progress-circular
        :rotate="-90"
        :size="120"
        :width="18"
        :value="progressValue"
        color="primary"
        class="ml-4"
        style="font-weight: 500; font-size: 1.80em"
      >
        {{ progressValue }}
      </v-progress-circular>
    </v-overlay>
  </v-app>
</template>

<script>
  import { mapState } from 'vuex'
  import { decrypt } from '../../plugins/utils'
  import store from '../../store'

  // 重登录状态码映射 - 移除硬编码，改为动态获取

  export default {
    name: 'DashboardIndex',

    components: {
      DashboardCoreAppBar: () => import('./components/core/AppBar'),
      DashboardCoreDrawer: () => import('./components/core/Drawer'),
      DashboardCoreSettings: () => import('./components/core/Settings'),
      DashboardCoreView: () => import('./components/core/View'),
    },

    data: () => ({
      expandOnHover: false,
      errorMessages: '',
      successMessage: '',
      snackbar: false,
      successSnackbar: false,
      snackbarTimeout: 2500,
      overlay: false,
      restartOverlay: false,
      backToLoginPageTimer: null,
      // progressInterval: {},
      progressValue: 0,
      rebootSpeed: 2, // 每秒+2，即100/2=45s, 45s重启完成
      backToLoginPageSuccess: false, // 是否重连成功标志
      backToLoginPageCount: 1, // 重连次数，到达5次后回退到login页面
      // 定时
      updateRealTimeTimer: null, // 更新当前时间定时器
      updateZoneInfoTimer: null, // 更新分区信息定时器
    }),
    computed: {
        ...mapState(['wsStatus', 'statusCode', 'rebootBySelf', 'systemDateTime', 'systemBootTime', 'timestamp', 'logoutBySelf',
                    'sortDeviceCustomResult']),
        // 计算当前的客户标题
        currentTitle() {
          return this.$ws.getCustomerTitle()
        }
    },
    watch: {
      // 监听标题变化，动态更新浏览器标题
      currentTitle: {
        handler(newTitle) {
          if (newTitle) {
            this.updateDocumentTitle(newTitle)
          }
        },
        immediate: true
      },
      // 监听语言变化，更新浏览器标题
      '$i18n.locale': function(newLocale) {
        // 语言变化时重新计算并更新标题
        this.$nextTick(() => {
          const title = this.$ws.getCustomerTitle()
          this.updateDocumentTitle(title)
        })
      },
      // 重登踢出，跳转到登录页面
      statusCode: function () {
        if (this.statusCode === null) {
          return
        }
        // 适配当分区排序时，不弹出该错误提示
        if (this.sortDeviceCustomResult != null) {
          return
        }
        const errorReason = this.getReloginReason(this.statusCode + '');
        this.errorMessages = this.$t('index.reloginMessage', { reason: errorReason })
        this.snackbar = true
        this.$store.commit('updateLogoutBySelf', true)
        setTimeout(() => {
          this.$router.push('/')
        }, 2500)
      },
      // 服务器异常断开后，弹出提示框提醒用户，用户点击后返回到登录界面。
      wsStatus: function () {
        // statusCode为空，代表正常连接，非空情况下会退回主界面（如踢出）
        if (this.statusCode !== null) {
          return
        }
        // wsStatus为空返回，即正常情况下的关闭不需要重连
        if (this.wsStatus == null) {
          return
        }
        // console.log('重启参数如下： ' + this.wsStatus + '=-==== ' + this.rebootBySelf)
        if (this.wsStatus === 'closed' && this.rebootBySelf === true) {
          this.$store.commit('updateRebootBySelf', false);
          this.restartOverlay = true
          const myInterval = window.setInterval(() => {
            if (this.progressValue < 100) {
              this.progressValue += 2
              // console.log('重启进度： ' + this.progressValue)
            } else if (this.progressValue === 100) {
              // console.log('重启完成')
              this.$store.commit('updateLogoutBySelf', true)
              window.clearInterval(myInterval)
              this.$router.push('/')
            }
          }, 1000)
        } else if (this.wsStatus === 'closed' && this.rebootBySelf === false && this.logoutBySelf === false) {
          // 弹出对话框正在重连
          this.overlay = true
          window.clearInterval(this.backToLoginPageTimer)
          this.backToLoginPageTimer = null
          this.backToLoginPageSuccess = false
          this.backToLoginPageCount = 1
          this.backToLoginPageTimer = this.getBackToLoginPageInterval()
        }
      }
    },
    mounted () {
      // 初始化翻译文本
      this.initializeTranslations()
      // 设置初始浏览器标题
      this.$nextTick(() => {
        const title = this.$ws.getCustomerTitle()
        this.updateDocumentTitle(title)
      })
      this.clearTimerAndInterval(true)
      this.overlay = false
      this.progressValue = 0
      this.restartOverlay = false
      if (this.$store.state.statusCode !== null) {
        this.$store.commit('updateStatusCode', null)
      }

      this.addZoneAndTimeInterval()
      window.addEventListener('visibilitychange', this.visibilityChangeListener)
    },
    methods: {
      initializeTranslations() {
        // 初始化消息
        this.errorMessages = this.$t('index.serverConnectionError')
        this.successMessage = this.$t('index.serverReconnectSuccess')
      },
      getReloginReason(statusCode) {
        // 动态获取重登录原因文本
        const statusMap = new Map([
          ['1', this.$t('index.reloginReasons.permissionChanged')],
          ['2', this.$t('index.reloginReasons.userRemoved')],
          ['3', this.$t('index.reloginReasons.loginElsewhere')],
          ['4', this.$t('index.reloginReasons.systemRestart')],
          ['5', this.$t('index.reloginReasons.accountAbnormal')]
        ])
        return statusMap.get(statusCode) || this.$t('index.reloginReasons.accountAbnormal')
      },
      /**
       * 设置定时器
       * @returns {number}
       */
      getBackToLoginPageInterval() {
        return window.setInterval(() => {
          // console.log("this.$route.fullPath="+this.$route.fullPath)
          if (this.backToLoginPageCount >= 5 && !this.backToLoginPageSuccess) {
              this.errorMessages = this.$t('index.serverConnectionError')
              this.snackbar = true
              this.$store.commit('updateLogoutBySelf', true)
              this.backToLoginPageSuccess = true
              this.backToLoginPageCount = 1
              try {
                setTimeout(() => {
                  this.$router.push('/')
                }, 1500)
              } catch (e) {
                console.log(e)
              }
          } else if (!this.backToLoginPageSuccess) {
            if (this.wsStatus === 'closed') {
              console.log(this.$t('index.reconnectAttempt', { count: this.backToLoginPageCount++ }))
              this.$ws.init(false)
            } else if (this.wsStatus === 'open') {
              // 重连成功
              if(this.$route.fullPath !== '/') {
                this.backToLoginPageSuccess = true
                const username = this.$store.state.user
                const password = decrypt(this.$store.state.pwd)
                this.$ws.authSuccess(username, password, null, null)
                this.overlay = false
                this.successSnackbar = true
                window.clearInterval(this.backToLoginPageTimer)
                this.backToLoginPageTimer = null
                this.addZoneAndTimeInterval()
              }
            }
          }
        }, 1500)
      },
      /**
       * 可视化切换事件监听者
       * @returns {number}
       */
      visibilityChangeListener() {
        if(document.hidden) {
          // tab is now inactive
          // temporarily clear timer using clearInterval() / clearTimeout()
          //只有在WS打开的情况下才清除定时器，否则会导致服务器断开后网页无法回退到登录页面
          if (this.wsStatus === 'open') {
            this.clearTimerAndInterval(false)
          }
        }
        else {
          // console.log('激活' + this.wsStatus)
          // tab is active again
          // update system time and restart timers
          // console.log('ws status : ' + this.wsStatus)
          if (this.wsStatus === 'closed' && this.rebootBySelf === false && this.logoutBySelf === false) {
            this.backToLoginPageSuccess = false;
            this.backToLoginPageCount = 1;
            window.clearInterval(this.backToLoginPageTimer)
            this.backToLoginPageTimer = this.getBackToLoginPageInterval()
          } else if (this.wsStatus === 'open') {
            this.addZoneAndTimeInterval()
          }
        }
      },
      /**
       * 获取当前时间
       */
      getNow() {
        // console.log('update time: ' + this.timestamp)
        // 20210516 修复可能出现的时间显示NaN的问题(如果日期是invalid Date，重新从systemDateTime中获取)
        let currentDateString = this.$store.state.timestamp.replace(/-/g, '/')
        if (isNaN(new Date(currentDateString).getTime())) {
          currentDateString = this.$store.state.systemDateTime.replace(/-/g, '/')
        }

        const nowTime = new Date(new Date(currentDateString).getTime() + 1000);
        const year = nowTime.getFullYear()
        const month = (nowTime.getMonth() + 1) < 10 ? '0' + (nowTime.getMonth() + 1) : nowTime.getMonth() + 1
        const date = nowTime.getDate() < 10 ? '0' + nowTime.getDate() : nowTime.getDate()
        const hour = nowTime.getHours() < 10 ? '0' + nowTime.getHours() : nowTime.getHours()
        const minute = nowTime.getMinutes() < 10 ? '0' + nowTime.getMinutes() : nowTime.getMinutes()
        const second = nowTime.getSeconds() < 10 ? '0' + nowTime.getSeconds() : nowTime.getSeconds()
        const dateTime = year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
        this.$store.commit('updateRealTime', dateTime)
      },
      /**
       * 刷新分区状态至最新
        */
      copyZoneList() {
        if (this.$store.state.needRefreshZoneList) {
          this.$store.commit('copyZoneList')
        }
      },
      /**
       * clear interval
       */
      clearTimerAndInterval(removeListener) {
        window.clearInterval(this.backToLoginPageTimer)
        window.clearInterval(this.updateRealTimeTimer)
        window.clearInterval(this.updateZoneInfoTimer)
        this.backToLoginPageTimer = null
        this.updateRealTimeTimer = null
        this.updateZoneInfoTimer = null
        if (removeListener) {
          window.removeEventListener('visibilitychange', this.visibilityChangeListener);
        }
      },
      addZoneAndTimeInterval() {
        // 如果从后台返回，重连成功，重新开启更新时间和分区信息
        window.clearInterval(this.updateRealTimeTimer)
        window.clearInterval(this.updateZoneInfoTimer)
        this.updateRealTimeTimer = null
        this.updateZoneInfoTimer = null

        store.commit('updateSystemDateTime');
        this.updateRealTimeTimer = window.setInterval(() => this.getNow(), 1000)
        this.updateZoneInfoTimer = window.setInterval(() => this.copyZoneList(), 800)
      },
      // 更新浏览器标题
      updateDocumentTitle(title) {
        if (title && typeof title === 'string') {
          document.title = title
          // 同时更新 HTML 的 title 属性（用于 SEO 和可访问性）
          const htmlElement = document.querySelector('html')
          if (htmlElement) {
            htmlElement.setAttribute('title', title)
          }
        }
      }
    },
    // 清除定时器
    beforeDestroy () {
      this.clearTimerAndInterval(true)
    },
    beforeRouteLeave (to, from, next) {
      if (to.path === '/') {
        // 只有跳转到登录页面，才清空定时器（计时，刷新分区，及掉线监控）
        this.clearTimerAndInterval(true);
        next()
      }
    },
    destroyed () {
      sessionStorage.removeItem('vuex')
      this.$ws.close()
    },
  }
</script>
<style>
/* 修改通知栏的文字大小 */
.v-snack__content {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: 1.35rem;
  font-weight: 400;
  /*letter-spacing: 0.0178571429em;*/
  line-height: 2rem;
  margin-left: 10px;
  margin-right: auto;
  padding: 14px 16px;
  text-align: initial;
}
</style>
