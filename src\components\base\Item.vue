<template>
  <v-list-item
    :href="href"
    :rel="href && href !== '#' ? 'noopener' : undefined"
    :target="href && href !== '#' ? '_blank' : undefined"
    :to="item.to"
    :active-class="`primary ${!isDark ? 'black' : 'white'}--text`"
  >
    <v-list-item-icon
      v-if="text"
      class="v-list-item__icon--text"
      v-text="computedText"
    />

    <v-list-item-icon v-else-if="item.icon && !isButtonStyleListItem">
      <v-icon v-text="item.icon" />
    </v-list-item-icon>

    <v-list-item-content v-if="isButtonStyleListItem && (item.title || item.subtitle)">
      <v-icon v-text="item.icon" />
      <v-list-item-title v-text="item.title" class="text-center"/>
    </v-list-item-content>

    <v-list-item-content v-if="!isButtonStyleListItem && (item.title || item.subtitle)">
      <v-list-item-title v-text="item.title" />

      <v-list-item-subtitle v-text="item.subtitle" />
    </v-list-item-content>
  </v-list-item>
</template>

<script>
  import Themeable from 'vuetify/lib/mixins/themeable'
  import { customerVersion } from "@/plugins/websocket";

  export default {
    name: 'Item',

    mixins: [Themeable],

    props: {
      item: {
        type: Object,
        default: () => ({
          href: undefined,
          icon: undefined,
          subtitle: undefined,
          title: undefined,
          to: undefined,
        }),
      },
      text: {
        type: Boolean,
        default: false,
      },
    },

    computed: {
      // 双层显示icon和文字内容
      isButtonStyleListItem() {
        return customerVersion === 'C6A0'
      },
      computedText () {
        if (!this.item || !this.item.title) return ''

        let text = ''

        this.item.title.split(' ').forEach(val => {
          text += val.substring(0, 1)
        })

        return text
      },
      href () {
        return this.item.href || (!this.item.to ? '#' : undefined)
      },
    },
  }
</script>
