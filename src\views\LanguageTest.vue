<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            {{ $t('language.selectLanguage') }}
          </v-card-title>
          <v-card-text>
            <LanguageSelector 
              :is-icon="false"
              button-class="mb-4"
              button-color="primary"
            />
            
            <v-divider class="my-4"></v-divider>
            
            <h3>{{ $t('common.info') }}</h3>
            <p><strong>{{ $t('login.username') }}:</strong> {{ $t('login.usernamePlaceholder') }}</p>
            <p><strong>{{ $t('login.password') }}:</strong> {{ $t('login.passwordPlaceholder') }}</p>
            <p><strong>{{ $t('appbar.cloudControl') }}:</strong> {{ $t('appbar.wechatScan') }}</p>
            
            <v-divider class="my-4"></v-divider>
            
            <h3>{{ $t('routes.controlCenter') }}</h3>
            <v-list>
              <v-list-item>
                <v-list-item-icon>
                  <v-icon>mdi-monitor-dashboard</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title>{{ $t('routes.controlCenter') }}</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
              
              <v-list-item>
                <v-list-item-icon>
                  <v-icon>mdi-calendar-clock</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title>{{ $t('routes.timerManagement') }}</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
              
              <v-list-item>
                <v-list-item-icon>
                  <v-icon>mdi-monitor-cellphone-star</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title>{{ $t('routes.deviceManagement') }}</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
              
              <v-list-item>
                <v-list-item-icon>
                  <v-icon>mdi-folder-multiple-image</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title>{{ $t('routes.mediaManagement') }}</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </v-list>
            
            <v-divider class="my-4"></v-divider>
            
            <h3>{{ $t('common.confirm') }}</h3>
            <v-btn color="primary" class="mr-2">{{ $t('common.confirm') }}</v-btn>
            <v-btn color="secondary" class="mr-2">{{ $t('common.cancel') }}</v-btn>
            <v-btn color="success" class="mr-2">{{ $t('common.save') }}</v-btn>
            <v-btn color="error">{{ $t('common.delete') }}</v-btn>
            
            <v-divider class="my-4"></v-divider>
            
            <h3>{{ $t('tts.title') }}</h3>
            <p>{{ $t('tts.inputText') }}</p>
            <p>{{ $t('tts.fileName') }}: {{ $t('tts.audioFileNamePlaceholder') }}</p>
            
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import LanguageSelector from '@/components/LanguageSelector.vue'

export default {
  name: 'LanguageTest',
  
  components: {
    LanguageSelector
  },
  
  data() {
    return {
      
    }
  }
}
</script>

<style scoped>
h3 {
  margin-bottom: 16px;
  color: #1976d2;
}
</style>
