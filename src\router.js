/*
* this file indicate the route relationship of the application
* If you want to add one page, you need to add one route here
* */

import Vue from 'vue'
import Router from 'vue-router'
import store from './store'
import * as ws from './plugins/websocket'

Vue.use(Router)

const router = new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  routes: [
    // Login todo set requiresAuth for /*
    {
      name: 'login',
      path: '/',
      component: () => import('@/views/dashboard/Login'),
    },
    {
      // todo whether change the uri of dashboard part
      path: '/dashboard',
      component: () => import('@/views/dashboard/Index'),
      meta: {
        needLogin: true,
      },
      children: [
        // 控制中心
        {
          name: '控制中心',
          path: 'dashboard',
          component: () => import('@/views/dashboard/Dashboard'),
          meta: {
            needLogin: true,
          },
        },
        // 分区管理页面
        {
          name: '设备管理',
          path: 'device',
          component: () => import('@/views/dashboard/tables/PartitionSystem'),
          meta: {
            needLogin: true,
          },
        },
        // 媒体管理页面
        {
          name: '媒体管理',
          path: 'media',
          component: () => import('@/views/dashboard/settings/Media'),
          meta: {
            needLogin: true,
          },
        },
        // 定时管理页面
        {
          name: '定时管理',
          path: 'timer',
          component: () => import('@/views/dashboard/Timer'),
          meta: {
            needLogin: true,
          },
        },
        // Dashboard
        {
          name: '账户管理',
          path: 'account',
          component: () => import('@/views/dashboard/settings/Account'),
          meta: {
            needLogin: true,
          },
        },
        // Pages
        {
          name: '监控管理',
          path: 'monitor',
          component: () => import('@/views/dashboard/settings/Monitor'),
          meta: {
            needLogin: true,
          },
        },
        {
          name: '日志管理',
          path: 'log',
          component: () => import('@/views/dashboard/settings/Log'),
          meta: {
            needLogin: true,
          },
        },
        {
          name: '系统设置',
          path: 'system',
          component: () => import('@/views/dashboard/settings/System'),
          meta: {
            needLogin: true,
          },
        },
        {
          name: '系统维护',
          path: 'maintenance',
          component: () => import('@/views/dashboard/settings/Maintenance'),
          meta: {
            needLogin: true,
          },
        },
        // Upgrade
        {
          name: 'Upgrade',
          path: 'upgrade',
          component: () => import('@/views/dashboard/roadmap/Upgrade'),
          meta: {
            needLogin: true,
          },
        },
        {
          name: '信息发布',
          path: 'information',
          component: () => import('@/views/Information'),
          meta: {
            needLogin: true,
          },
        },
        // finally the default route, when none of the above matches:
        {
          name: '出错啦',
          path: '*',
          component: () => import('@/views/NotFound'),
        }
      ],
    },
  ],
})

// 全局钩子函数
router.beforeEach((to, from, next) => {
  to.matched.some((route) => {
    // 通过此操作可以判断哪些页面需要登录
    if (route.meta.needLogin) {
      if (store.state.loginResult === 0) {
        next()
      } else {
        next('/')
      }
    } else {
      next()
    }
  })
})

// 全局钩子函数
router.afterEach((to, from) => {
  const state = store.state
  const delayRequestTime = 1500
  if (to.name === 'login' || to.path === '/') {
    // store.commit('updateLoginResult', null)
    // store.commit('updateWebsocket', null)
    if (state.websocket != null && state.websocket.readyState === WebSocket.OPEN) {
      // 跳转到登录页面时断开websocket
      store.commit('closeWebsocket')
    }
    store.commit('updateLogoutBySelf', false)
    store.commit('resetState')
    return
  }
  switch (to.name) {
    case '设备管理':
      // 获取服务器上传路径下的所有固件
      if (state.websocket != null && state.websocket.readyState === 1 && state.wsStatus === 'open') {
        // 解决刷新问题，如果已有相应object，则延时请求（等待重新登录）
        setTimeout(() => {
          ws.getServerUploadFirmwares()
        }, state.existFirmwareNames.length === 0 ? 1 : delayRequestTime)
      }
      break
    case '控制中心':
      // 重新进入控制中心，分区/分组选择信息应重置
      store.commit('cleanSelectedPartitionAndGroups')
      break
    case '定时管理':
      // 获取服务器的定时点信息, 登录或者timer.xml刷新才会更新定时点信息
      // ws.getServerTimer()
        if(store.getters.sequencePowers.length > 0 && state.sequencePowerInfo.length === 0)
          ws.getSequencePowerInfo()
      break
    case '系统设置':
      // 20210318 每次进入系统设置重新获取网络信息，避免IP在外部修改后没有刷新的问题
      // 每次访问系统设置页面，获取存储信息并更新
      if (state.websocket != null && state.websocket.readyState === 1 && state.wsStatus === 'open') {
          ws.setSystemNetwork(0)
          ws.getSystemStorage()
      }
      break
    case '账户管理':
      // 获取账户信息（20220622 登录后即获取，且重新进入页面时不刷新）
      // if (state.user === 'admin') {
      //   const time = state.userInfoData == null ? 1 : delayRequestTime
      //   store.commit('resetUserInfoData')
      //   store.commit('resetZonesMac')
      //   setTimeout(() => ws.getUserInfo(state.user), time)
      // }
      break
    case '日志管理':
      break
    default:
      break
  }
})

export default router
