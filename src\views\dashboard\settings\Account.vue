<template>
  <div>
    <!--通用successSnackBar-->
    <v-snackbar
      v-model="commonSuccessSnackBar"
      color="primary"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ successMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="primary" fab small class="ml-6" v-bind="attrs" @click="commonSuccessSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--通用errorSnackBar-->
    <v-snackbar
      v-model="commonErrorSnackBar"
      color="error"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="error" fab small class="ml-6" v-bind="attrs" @click="commonErrorSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--删除用户对话框-->
    <!--对话框-->
    <v-dialog
      v-model="deleteUserDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title style="font-size: 2.0em;">{{ $t('account.removeUser') }}</v-toolbar-title>
        </v-toolbar>
        <v-card-text class="text-center mt-5">
          <span>{{ $t('account.aboutToRemoveUser') }}</span>
          <span class="font-weight-black text-decoration-underline">{{ getDeleteAccount() }}</span>
          <span>,{{ $t('account.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions class="mt-n4">
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteUserDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="deleteUser"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- 添加用户， 编辑用户通用对话框 -->
    <v-dialog
      v-model="userDialog"
      :max-width="isExpandDeviceSelection ? 650 : 320"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title v-show="isAddUser" style="font-size: 2.0em;">{{ $t('account.addUser') }}</v-toolbar-title>
          <v-toolbar-title v-show="!isAddUser" style="font-size: 2.0em;">{{ $t('account.editUser') }}</v-toolbar-title>
        </v-toolbar>
        <v-container>
          <v-row dense>
            <v-col cols="12" v-show="isShowMultiLevelAccount">
              <v-text-field
                v-model="account"
                prepend-icon="mdi-account"
                :label="$t('account.accountField')"
                :placeholder="$t('account.accountPlaceholder')"
                hide-details
                :disabled="!isAddUser"
              />
            </v-col>
            <v-col cols="12" v-show="isShowMultiLevelAccount">
              <v-text-field
                v-model="username"
                prepend-icon="mdi-account"
                :label="$t('account.nameField')"
                :placeholder="$t('account.namePlaceholder')"
                hide-details
              />
            </v-col>
            <v-col cols="12" v-show="!isShowMultiLevelAccount">
              <v-text-field
                v-model="username"
                prepend-icon="mdi-account"
                :label="$t('account.usernameField')"
                :placeholder="$t('account.usernamePlaceholder')"
                hide-details
                :disabled="!isAddUser"
              />
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="password"
                prepend-icon="mdi-lock"
                :label="$t('account.passwordField')"
                :placeholder="$t('account.passwordPlaceholder')"
                hide-details
                :append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                :type="showPassword ? 'text' : 'password'"
                @click:append="showPassword = !showPassword"
              />
            </v-col>
            <v-col cols="12" class="mt-4" v-show="isExpandDeviceSelection">
              <!-- todo i8n, max height test and setting -->
              <v-cascader
                :key="refreshCascader"
                v-model="selectedParentAccount"
                :label="$t('account.selectParent')"
                item-value="account"
                item-text="show_name"
                :items="filteredMultiLevelAccountList"
                outlined
                clearable
                dense
              />
            </v-col>
          </v-row>
          <!-- 增加用户权限设置, ******** 新增v-row使宽度自适应 -->
          <!-- 移除用户权限设置，移动到新的高级编辑对话框-->
          <!-- 只有二级账号能选择全局播放列表管理和定时点管理权限 -->
          <!--<v-row v-show="selectedParentAccount != null" class="mt-n1">
            <v-tooltip
                bottom
                open-delay="1000"
                nudge-right="40"
              >
              <template v-slot:activator="{ on, attrs }">
                <v-col cols="auto" v-bind="attrs" v-on="on">
                  <v-checkbox
                    v-model="isRepeatedLoginAllowed"
                    :label="$t('account.allowRepeatedLogin')"
                    class="ma-0 pt-n2 input-class"
                  />
                </v-col>
              </template>
              <span>{{ $t('account.allowRepeatedLoginTooltip') }}</span>
            </v-tooltip>

            <v-tooltip
              bottom
              open-delay="1000"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-col cols="auto" v-show="isShowAdvantagePermission"
                       v-bind="attrs" v-on="on">
                  <v-checkbox
                    v-model="isPlaylistAllowed"
                    :label="$t('account.globalPlaylistPermission')"
                    class="ma-0 pt-n2 input-class"
                  />
                </v-col>
              </template>
              <span>{{ $t('account.globalPlaylistPermissionTooltip') }}</span>
            </v-tooltip>

            <v-tooltip
              bottom
              open-delay="1000"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-col cols="auto" v-show="isShowAdvantagePermission"
                       v-bind="attrs" v-on="on">
                  <v-checkbox
                    v-model="isTimerAllowed"
                    :label="$t('account.globalTimerPermission')"
                    class="ma-0 pt-n2 input-class"
                  />
                </v-col>
              </template>
              <span>{{ $t('account.globalTimerPermissionTooltip') }}</span>
            </v-tooltip>

            <v-tooltip
              bottom
              open-delay="1000"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-col cols="auto" v-show="false"
                       v-bind="attrs" v-on="on">
                  <v-checkbox
                    v-model="isAudioCollectorAllowed"
                    :label="$t('account.audioCollectorPermission')"
                    class="ma-0 pt-n2 input-class"
                  />
                </v-col>
              </template>
              <span>{{ $t('account.audioCollectorPermissionTooltip') }}</span>
            </v-tooltip>
          </v-row>-->
        </v-container>
        <div v-if="isExpandDeviceSelection" class="pt-3 text-center">
          <el-transfer
            v-model="partitionSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('account.deviceKeywordPlaceholder')"
            :data="selectableZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('account.deviceList'), $t('account.selectedDevices')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions class="">
          <v-row class="ma-0 pa-0">
            <v-col cols="5" v-if="isExpandDeviceSelection">
              <v-select
                v-model="deviceTypeSelected"
                :items="deviceTypeList"
                item-value="value"
                item-text="text"
                name="deviceTypeSelected"
                :label="$t('account.deviceType')"
                class="section-high-class"
                :menu-props="{ top: true, offsetY: true }"
                @input="deviceSelectRoute"
                dense
              />
            </v-col>
            <v-col :cols="$i18n.locale === 'zh' ? 2 : 1" v-if="isExpandDeviceSelection">
              </v-col>
            <v-col :cols="isExpandDeviceSelection ? ($i18n.locale === 'zh' ? 5 : 6) : 12">
              <v-btn
                color="primary"
                text
                @click="userDialog = false"
                :class="$i18n.locale === 'zh' || (!isExpandDeviceSelection) ? '' : 'pl-6 ml-6'"
              >
                {{ $t('common.cancel') }}
              </v-btn>
              <v-btn
                color="primary"
                text
                @click="createOrEditUser()"
                class="ml-1"
              >
                {{ $t('account.save') }}
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="advancedDialog"
      :max-width="650"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title  style="font-size: 2.0em;">{{ $t('account.advancedSettings') }}</v-toolbar-title>
        </v-toolbar>

        <v-card-subtitle class="mt-7" style="font-size: 1.6em;">
          {{ $t('account.userPermissions') }}
        </v-card-subtitle>
        <v-divider class="mt-n2"></v-divider>
        <!--checkbox group-->
        <v-row class="ma-0">
          <v-col cols="6">
            <v-tooltip
              bottom
              open-delay="1000"
              nudge-right="40"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-col cols="auto" v-bind="attrs" v-on="on">
                  <v-checkbox
                    v-model="isRepeatedLoginAllowed"
                    :label="$t('account.permissions.allowRepeatedLogin')"
                    class="ma-0 pt-n2 input-class"
                  />
                </v-col>
              </template>
              <span>{{ $t('account.permissions.allowRepeatedLoginTooltip') }}</span>
            </v-tooltip>
          </v-col>

          <v-col cols="6">
            <v-tooltip
              bottom
              open-delay="1000"
              nudge-right="40"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-col cols="auto" v-bind="attrs" v-on="on" v-show="isShowCreateSubUserPermission">
                  <v-checkbox
                    v-model="isCreateSubAccountAllowed"
                    :label="$t('account.allowCreateSubAccount')"
                    class="ma-0 pt-n2 input-class"
                  />
                </v-col>
              </template>
              <span>{{ $t('account.allowCreateSubAccountTooltip') }}</span>
            </v-tooltip>
          </v-col>

          <v-col cols="6" class="mt-n5">
            <v-tooltip
              bottom
              open-delay="1000"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-col cols="auto" v-show="isShowAdvantagePermission"
                       v-bind="attrs" v-on="on">
                  <v-checkbox
                    v-model="isPlaylistAllowed"
                    :label="$t('account.permissions.globalPlaylistManagement')"
                    class="ma-0 pt-n2 input-class"
                  />
                </v-col>
              </template>
              <span>{{ $t('account.permissions.globalPlaylistManagementTooltip') }}</span>
            </v-tooltip>
          </v-col>

          <v-col cols="6" class="mt-n5">
            <v-tooltip
              bottom
              open-delay="1000"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-col cols="auto" v-show="isShowAdvantagePermission"
                       v-bind="attrs" v-on="on">
                  <v-checkbox
                    v-model="isTimerAllowed"
                    :label="$t('account.permissions.globalTimerManagement')"
                    class="ma-0 pt-n2 input-class"
                  />
                </v-col>
              </template>
              <span>{{ $t('account.permissions.globalTimerManagementTooltip') }}</span>
            </v-tooltip>
          </v-col>

          <v-col cols="6" class="mt-n5">
            <v-tooltip
              bottom
              open-delay="1000"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-col cols="auto" v-show="false"
                       v-bind="attrs" v-on="on">
                  <v-checkbox
                    v-model="isAudioCollectorAllowed"
                    :label="$t('account.permissions.audioCollectorUsage')"
                    class="ma-0 pt-n2 input-class"
                  />
                </v-col>
              </template>
              <span>{{ $t('account.permissions.audioCollectorUsageTooltip') }}</span>
            </v-tooltip>
          </v-col>
        </v-row>

        <v-row class="ma-0">
          <v-col cols="6">
            <v-btn
              color="primary darken-1"
              class="text-lg-h4 mt-n3 ml-4"
              @click="confirmSetUserAuthority"
            >
              {{ $t('account.setUserPermissions') }}
            </v-btn>
          </v-col>
        </v-row>

        <!-- C4BO版本，且管理员账户登录时，才显示存储空间信息和允许设置存储空间，其他客制化版本不显示。
              设置存储空间大小范围：50MB~1TB -->
        <div v-show="isAdmin && isCloudIpSystemB() && this.account !== 'admin'">
          <v-divider class=""></v-divider>
          <v-card-subtitle class="mt-4" style="font-size: 1.6em;">
            {{ $t('account.storageSpace') }}
          </v-card-subtitle>
          <v-divider></v-divider>
          <v-row class="ma-0">
            <v-col cols="12">
              <v-chip
                color="primary"
                dark
                class="mt-3 ml-4"
              >
                <v-icon left>mdi-database</v-icon>
                {{ $t('account.totalStorageSpace') }}：{{ storageCapacity }}MB，{{ $t('account.usedSpace') }}：{{ usedStorageCapacity }}MB，{{ $t('account.remainingSpace') }}：{{ remainStorageCapacity }}MB
              </v-chip>
            </v-col>
            <v-col cols="6">
              <v-text-field
                v-model="settingStorageCapacity"
                append-icon="mdi-pencil"
                :label="$t('account.setStorageSpace')"
                :hint="$t('account.storageSpaceHint')"
                :rules="[rules.required, rules.number, rules.storage]"
                persistent-hint
                clearable
                class="mt-n3 ml-4"
              />
            </v-col>
            <v-col cols="5">
              <v-btn
                color="primary darken-1"
                class="text-lg-h4 ml-4"
                @click="confirmSetUserStorageCapacity"
              >
                {{ $t('account.setStorageSpaceButton') }}
              </v-btn>
            </v-col>

          </v-row>
        </div>

      </v-card>
    </v-dialog>
    <!--操作员设置控制-->
    <v-container
      id="Account"
      fluid
      class="mt-n2"
    >
      <v-card class="mb-2">
        <v-card-title style="margin-top: -20px">
          <!--按钮栏-->
          <span class="text-lg-h3">{{ $t('account.accountManagement') }}</span>
          <span style="padding-left: 30px" />
          <v-btn
            color="primary"
            @click="preCreateUser"
            v-show="isAddUserOperator"
          >
            {{ $t('account.addUser') }}
          </v-btn>
          <v-btn
            color="primary"
            @click="preEditUser"
            v-show="!isShowMultiLevelAccount"
          >
            {{ $t('account.editUser') }}
          </v-btn>
          <v-btn
            v-show="!isShowMultiLevelAccount && getAccount.username !== 'admin'"
            color="primary"
            @click="preDeleteUser"
          >
            {{ $t('account.removeUser') }}
          </v-btn>
          <v-spacer />
          <v-text-field
            v-show="!isShowMultiLevelAccount"
            v-model="schemaSearch"
            append-icon="mdi-magnify"
            :label="$t('account.search')"
            single-line
            hide-details
            full-width
            style="padding-bottom: 35px"
          />
        </v-card-title>
        <v-data-table
          v-show="!isShowMultiLevelAccount"
          v-model="accountSelected"
          :headers="accountHeaders"
          :items="accountList"
          :search="schemaSearch"
          sort-by.sync="['uid']"
          item-key="username"
          class="elevation-1"
          single-select
          :loading="accountList.length === 0"
          :loading-text="$t('account.loadingText')"
          style="margin-top: -10px"
          :items-per-page="10"
          @click:row="rowClick"
          :footer-props="{
            itemsPerPageOptions: [10,25,50,100],
            showFirstLastPage: true,
            showCurrentPage: true,
            firstIcon: 'mdi-arrow-collapse-left',
            lastIcon: 'mdi-arrow-collapse-right',
            prevIcon: 'mdi-minus',
            nextIcon: 'mdi-plus'
          }"
        >
          <!--                <template v-slot:item.id="{ item }">-->
          <!--                  <span>{{ getEventList.map(function(x) {return x.mac; }).indexOf(item.mac) + 1 }}</span>-->
          <!--                </template>-->
          <template v-slot:item.authority="{ item }">
            <span>{{ getAccountType(item.username) }}</span>
          </template>
          <template v-slot:item.password="{ item }">
            <v-tooltip
              bottom
              open-on-hover
              open-delay="1000"
            >
              <template v-slot:activator="{ on, attrs }">
                <span v-bind="attrs" v-on="on">{{ getAccountPassword(item.password) }}</span>
              </template>
              <span>{{ item.password }}</span>
            </v-tooltip>
          </template>
          <template v-slot:item.zoneCounts="{ item }">
            <v-tooltip
              bottom
              open-on-hover
              open-delay="500"
            >
              <template v-slot:activator="{ on, attrs }">
                <span v-bind="attrs" v-on="on">{{ item.zoneCounts }}</span>
              </template>
              <span>{{ item.username !== 'admin' ? (item.zoneMacs == null || item.zoneMacs.length === 0 ? $t('account.notBoundZones') : getZonesNameByMacs(item.zoneMacs)) : $t('account.allZones') }}</span>
            </v-tooltip>
          </template>
          <!--<template v-slot:item.valid="{ item }">
                  <v-chip :color="getColorOfSchemaStatus(item.valid)" dark>{{ getSchemaValidStatus(item.valid) }}</v-chip>
                </template>-->
        </v-data-table>
<!--      todo 增加搜索功能（需要遍历整个循环）  -->
<!--        data.account.toLowerCase().includes(accountSearch.toLowerCase()))-->
        <el-table
          ref="table"
          class="ml-1"
          v-show="isShowMultiLevelAccount"
          :data="multiLevelAccountList"
          style="width: 100%"
          row-key="uid"
          default-expand-all
          height="83vh"
          :tree-props="{children: 'sub_users'}"
        >
            <el-table-column
              prop="uid"
              :label="$t('account.table.serialNumber')"
              width="180">
            </el-table-column>
            <el-table-column
              align="center"
              prop="account"
              :label="$t('account.table.account')"
            >
            </el-table-column>
            <el-table-column
              align="center"
              prop="user_name"
              :label="$t('account.table.name')"
            >
            </el-table-column>
            <el-table-column
              align="center"
              prop="user_authority.zone_count"
              :label="$t('account.table.boundDeviceCount')"
            >
              <template slot-scope="scope">
                <!-- 检查admin绑定数量是否正确 -->
                {{ scope.row.account === 'admin' ? allTypeZones.length : scope.row.user_authority.zone_count }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              :label="$t('account.table.actions')">
              <!-- todo 搜索功能难以实现 -->
              <!--<template slot="header" slot-scope="scope">
                <el-input
                  v-model="accountSearch"
                  size="mini"
                  placeholder="输入关键字搜索"/>
              </template>-->
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click="handleEdit(scope.$index, scope.row)">{{ $t('account.actions.edit') }}</el-button>
                <el-button
                  size="mini"
                  type="primary"
                  @click="handleAdvancedEdit(scope.$index, scope.row)">{{ $t('account.actions.advanced') }}</el-button>
                <el-button
                  size="mini"
                  type="danger"
                  style="color: #FFF"
                  v-show="scope.row != null && scope.row.account !== user"
                  @click="handleDelete(scope.$index, scope.row)">{{ $t('account.actions.delete') }}</el-button>
              </template>
            </el-table-column>
        </el-table>
      </v-card>
    </v-container>
  </div>
</template>

<script>
import {mapGetters, mapState} from 'vuex'
import {
  checkIfAudioCollectorAllowed, checkIfCreateSubUserAllowed,
  checkIfPlaylistAllowed,
  checkIfRepeatedLoginAllowed,
  checkIfTimerAllowed,
  getAllTypeList,
  getDeviceModelMappingList,
  USER_LIMITS_NONE,
  USER_LIMITS_PLAYLIST,
  USER_LIMITS_REPEATED_LOGIN,
  USER_LIMITS_TIMER,
  USER_LIMITS_CREATE_SUB_USER
} from "../../../store";
import {VCascader} from '@tookit/vma'
import { deviceModelEnum } from '@/store'
import { customerVersion } from '@/plugins/websocket'

export default {
    name: 'Account',
    components: {
      VCascader,
    },
    data: () => ({
      deleteUserDialog: false,
      partitionSelectTransfer: [], // 当前用户已选择的分区
      userDialog: false,
      advancedDialog: false, // 高级编辑对话框
      isAddUser: true, // 添加，编辑用户功能切换
      account: null, // 账号名
      username: null, // 用户名
      password: null, // 密码
      userType: null, // 用户类型
      accountSelected: [], // 已选中的账号
      accountList: [], // 账户列表
      schemaSearch: null,
      tab: null,
      successMessages: '',
      commonSuccessSnackBar: false,
      errorMessages: '',
      commonErrorSnackBar: false,
      snackbarTimeout: 1500,
      adminUsername: 'admin',
      adminPassword: 'admin',
      // 账号headers，将管理员账户和操作员账户设置页面合并，使用列表形式
      accountHeaders: [],
      showPassword: false,
      // 权限相关
      isPlaylistAllowed: false,
      isTimerAllowed: false,
      isAudioCollectorAllowed: false,
      isRepeatedLoginAllowed: false,
      isCreateSubAccountAllowed: false, //允许创建子账户
      deviceTypeSelected: -1,
      zonesOfDeviceType: [],
      preDeviceType: -1,
      tempZoneSelectedObject: {},
      // editRawZoneSelectObject: {}
      // 多级账户
      multiLevelAccountList: [],
      filteredMultiLevelAccountList: [],
      accountId: 1,
      selectedParentAccount: null,
      accountSearch: null,
      clickUserOfMultiLevel: null,
      /* 强制刷新级联表单 */
      refreshCascader: false,
      refreshTable: false,
      storageCapacity: null,
      usedStorageCapacity: null,
      remainStorageCapacity: null,
      settingStorageCapacity: null,
      rules: {
        required: value => !!value || '请填入数字',
        number: value => {
          const pattern = /^-?\d+$/
          return pattern.test(value) || '请填入数字'
        },
        storage: value => (parseInt(value) >= 50 && parseInt(value) <= 1048576)  || '有效范围：50MB~1TB (1048576MB)',
      },
    }),
    computed: {
      ...mapState(['eventList', 'errorId', 'errorWsMessage', 'userInfoData', 'zonesMacObject', 'user', 'addUserResult', 'apiName',
                   'deleteUserResult', 'editUserResult', 'user_type', 'localSongListInfo', 'setUserStorageCapacityResult',
                   'setUserAuthorityResult', 'authority']),
      ...mapGetters(['decodeZones', 'pagers', 'audioCollectors', 'fireCollectors', 'sequencePowers', 'audioMixers', 'remoteControlers', 'phoneGateways',
                    'ampControlers', 'noiseDetectors', 'allTypeZones', 'isAdmin']),
      // 可供选择的分区列表，只能选择上级所拥有的分区列表
      selectableZones() {
        if (this.zonesOfDeviceType.length === 0 || this.selectedParentAccount == null) {
          return []
        }
        if (this.selectedParentAccount === 'admin') {
          return this.zonesOfDeviceType
        }
        // 从zonesMacObject中取出上级的分区列表
        let parentZoneMacs = this.zonesMacObject[this.selectedParentAccount];
        if (parentZoneMacs == null) {
          parentZoneMacs = []
        }
        return this.zonesOfDeviceType.filter(item => parentZoneMacs.includes(item.mac));
      },
      selectedParentUserType() {
        if (this.selectedParentAccount == null) {
          return null
        }
        return this.getUserTypeByAccount(this.userInfoData, this.selectedParentAccount)
      },
      isShowMultiLevelAccount() {
        return this.user_type !== null
      },
      apiSuccessMsg () {
        return this.$store.getters.apiSuccessMsg
      },
      // 是否显示高级权限设置（允许重复登录除外，认为是基础权限）,父级账户为管理员（一级）且不是编辑自身时可以设置高级权限
      // C4B0不显示
      isShowAdvantagePermission () {
        return (this.isAddUser || this.clickUserOfMultiLevel.account !== this.user) && this.selectedParentUserType === 1 && !this.isCloudIpSystemB()
      },
      // 任意版本：管理员用户登录，且编辑对象是二级、三级账户时，显示权限：允许创建子账户
      isShowCreateSubUserPermission () {
        return this.isAdmin && (this.userType === 2 || this.userType === 3)
      },
      // 四级账户不允许添加其他用户(自身可以编辑-从树形列表的编辑操作中进入)
      isAddUserOperator () {
        if (this.isAdmin) {
          return true
        }
        // 当前账户如果没有创建子用户权限，则不显示添加用户按钮
        return this.user_type !== 4 && checkIfCreateSubUserAllowed(this.authority)
      },
      // 如果为true，则不显示分区选择穿梭框，框体变窄，不是编辑自身时显示设备选择穿梭框
      isExpandDeviceSelection () {
        if (this.isAddUser) {
          return true;
        }
        if (!this.isShowMultiLevelAccount) {
          return this.getAccount.username !== 'admin';
        }
        return this.clickUserOfMultiLevel.account !== this.user
      },
      getAccount () {
        // 多级账号开关打开
        if (this.isShowMultiLevelAccount) {
          return this.clickUserOfMultiLevel
        }
        // 多级账号关闭
        if (this.accountSelected.length > 0) {
          return this.accountSelected[0];
        }
        return {
          authority: '',
          password: '',
          uid: '',
          user_type: '',
          username: '',
          zoneCounts: '',
          zoneMacs: [],
        };
      },
      deviceTypeList() {
        const list = [{text: this.$t('deviceTypes.allDevices'), value: -1}]
        if (this.decodeZones.length !== 0) {
          list.push({text: this.$t('deviceTypes.decodeTerminal'), value: 0})
        }
        if (this.pagers.length !== 0) {
          list.push({text: this.$t('deviceTypes.smartPager'), value: 1})
        }
        if (this.audioCollectors.length !== 0) {
          list.push({text: this.$t('deviceTypes.audioCollector'), value: 3})
        }
        if (this.fireCollectors.length !== 0) {
          list.push({text: this.$t('deviceTypes.fireCollector'), value: 4})
        }
        if (this.sequencePowers.length !== 0) {
          list.push({text: this.$t('deviceTypes.powerSequencer'), value: 5})
        }
        if (this.audioMixers.length !== 0) {
          if(customerVersion === 'C3A0') {
            list.push({text: this.$t('deviceTypes.audioCoprocessor'), value: 6})
          }
          else {
            list.push({text: this.$t('deviceTypes.audioRepeater'), value: 6})
          }
        }
        if (this.remoteControlers.length !== 0) {
          list.push({text: this.$t('deviceTypes.remoteController'), value: 7})
        }
        if (this.phoneGateways.length !== 0) {
          list.push({text: this.$t('deviceTypes.phoneGateway'), value: 8})
        }
        if (this.ampControlers.length !== 0) {
          list.push({text: this.$t('deviceTypes.ampController'), value: 9})
        }
        if (this.noiseDetectors.length !== 0) {
          list.push({text: this.$t('deviceTypes.noiseDetector'), value: 10})
        }
        return list
      },
    },
    watch: {
      // 监听语言变化
      '$i18n.locale'() {
        this.initializeTranslations()
      },
      // 统一错误处理
      errorId: function () {
        if (this.$route.fullPath !== '/dashboard/account') {
          return;
        }
        if (this.$store.state.errorId !== null) {
          this.errorMessages = this.$store.state.errorWsMessage
          this.commonErrorSnackBar = true
        }
      },
      // 变更上级后，清空右侧已选择设备列表，仅对话框打开前提下
      selectedParentAccount() {
        if (this.userDialog) {
          this.partitionSelectTransfer = [];
        }
      },
      addUserResult: function () {
        if (this.addUserResult === 0) {
          this.successMessages = this.apiSuccessMsg
          this.commonSuccessSnackBar = true
          //this.$ws.getUserZone(this.username)
          this.userDialog = false
          this.accountSelected = []
          this.selectedParentAccount = null
          this.$store.commit('updateAddUserResult', null)
        }
      },
      deleteUserResult: function () {
        if (this.deleteUserResult === 0) {
          this.successMessages = this.apiSuccessMsg
          this.commonSuccessSnackBar = true
          this.deleteUserDialog = false
          this.accountSelected = []
          this.selectedParentAccount = null
          this.$store.commit('updateDeleteUserResult', null)
        }
      },
      editUserResult: function () {
        if (this.editUserResult === 0) {
          this.successMessages = this.apiSuccessMsg
          this.commonSuccessSnackBar = true
          //this.$ws.getUserZone(this.username)
          this.userDialog = false
          this.accountSelected = []
          this.selectedParentAccount = null
          this.$store.commit('updateEditUserResult', null)
        }
      },
      userInfoData: {
        handler: function () {
          console.log("userInfoData changed!")
          //this.updateAccountListFromUserInfo()
          // if (this.isShowMultiLevelAccount) {
          //   this.multiLevelAccountList = this.getMultiLevelAccountList()
          //   this.refreshCascader = !this.refreshCascader
          // } else {
          //   this.updateAccountListFromUserInfo();
          //   this.updateAccountZoneMacsFromUserInfo()
          // }
        },
        deep: true,
      },
      zonesMacObject: {
        handler: function () {
          console.log("zonesMacObject changed!")
          // 如果只放在userInfoData变化中处理，可能变化后会捕捉不到，而zoneMac每次都会变化，可以确保刷新
          if (this.isShowMultiLevelAccount) {
            this.multiLevelAccountList = this.getMultiLevelAccountList()
            this.refreshCascader = !this.refreshCascader
          } else {
            this.updateAccountListFromUserInfo();
            this.updateAccountZoneMacsFromUserInfo()
          }
        },
        deep: true
      },
      filteredMultiLevelAccountList() {
        this.refreshCascader = !this.refreshCascader
      },
      userDialog() {
        if (this.userDialog === false) {
          this.deviceTypeSelected = -1
          // this.zonesOfDeviceType = this.allTypeZones
          this.tempZoneSelectedObject = {}
          this.tempZoneSelectedObject['' + -1] = []
          // this.editRawZoneSelectObject = {}
        }
      },
      setUserStorageCapacityResult() {
        if (this.setUserStorageCapacityResult === 0) {
          this.successMessages = this.$t('account.messages.setStorageSuccess')
          this.commonSuccessSnackBar = true
          // 更新存储空间后回显修改后的存储空间
          this.multiLevelAccountList = this.getMultiLevelAccountList()
          this.storageCapacity = parseInt(this.settingStorageCapacity)
          this.remainStorageCapacity = this.getRemainStorageCapacity()
          this.$store.commit('updateSetUserStorageCapacityResult', null)
        }
      },
      setUserAuthorityResult() {
        if (this.setUserAuthorityResult === 0) {
          this.successMessages = this.$t('account.messages.setPermissionSuccess')
          this.commonSuccessSnackBar = true
          // 更新权限后回显修改后的权限
          this.multiLevelAccountList = this.getMultiLevelAccountList()
          // const account = this.getAccountObjectFromMultipleLevel()
          // this.isPlaylistAllowed = checkIfPlaylistAllowed(account.authority)
          // this.isTimerAllowed = checkIfTimerAllowed(account.authority)
          // this.isAudioCollectorAllowed = checkIfAudioCollectorAllowed(account.authority)
          // this.isRepeatedLoginAllowed = checkIfRepeatedLoginAllowed(account.authority)
          // this.isCreateSubAccountAllowed = checkIfCreateSubUserAllowed(account.authority)
          this.$store.commit('updateSetUserAuthorityResult', null)
        }
      },
    },
    mounted() {
      // 初始化翻译
      this.initializeTranslations()
      // 进入时显示用户信息（不重新获取）
      if (this.isShowMultiLevelAccount) {
        this.multiLevelAccountList = this.getMultiLevelAccountList()
      } else {
        this.updateAccountListFromUserInfo();
        this.updateAccountZoneMacsFromUserInfo()
      }
      this.deviceTypeSelected = -1
      this.tempZoneSelectedObject = {}
      this.tempZoneSelectedObject['' + -1] = []
      this.zonesOfDeviceType = this.allTypeZones.filter(zone=>zone.device_model !== deviceModelEnum.NetAudioMixerDecoder)
    },
    methods: {
      initializeTranslations() {
        // 初始化消息
        this.successMessages = this.$t('common.operationSuccess')
        this.errorMessages = this.$t('common.operationFailed')

        // 初始化表格标题
        this.accountHeaders = [
          { text: this.$t('account.headers.serialNumber'), value: 'uid', align: 'center', sortable: false },
          { text: this.$t('account.headers.name'), value: 'username', align: 'center', sortable: false },
          { text: this.$t('account.headers.password'), value: 'password', align: 'center', sortable: false },
          { text: this.$t('account.headers.accountType'), value: 'authority', align: 'center', sortable: false },
          { text: this.$t('account.headers.boundDeviceCount'), value: 'zoneCounts', align: 'center', sortable: false },
        ]
      },
      getAccountObjectFromMultipleLevel() {
        if (this.multiLevelAccountList.length === 0 || this.account == null) {
          return null
        }
        const userInfoData = this.userInfoData;
        const account = this.account
        let targetUser;
        if (userInfoData.account === account) {
          targetUser = userInfoData
        } else if (userInfoData.sub_users.find(u => u.account === account)) {
          // 2级账号
          targetUser = userInfoData.sub_users.find(u => u.account === account);
        } else {
          // 3级账号
          outer:
            for (let i = 0; i < userInfoData.sub_users.length; i++) {
              if (userInfoData.sub_users[i].sub_users && userInfoData.sub_users[i].sub_users.length > 0) {
                const tripleSubUsers = userInfoData.sub_users[i].sub_users;
                for (let j = 0; j < tripleSubUsers.length; j++) {
                  if (tripleSubUsers[j].account === account) {
                    targetUser = tripleSubUsers[j]
                    break outer;
                    // 4级账号
                  } else if (tripleSubUsers[j].sub_users && tripleSubUsers[j].sub_users.length > 0) {
                    const fourthSubUsers = tripleSubUsers[j].sub_users;
                    for (let k = 0; k < fourthSubUsers.length; k++) {
                      if (fourthSubUsers[k].account === account) {
                        targetUser = fourthSubUsers[j]
                        break outer;
                      }
                    }
                  }
                }
              }
            }
        }
        return targetUser
      },
      handleEdit(index, row) {
        this.clickUserOfMultiLevel = row
        this.clickUserOfMultiLevel.username = row.account
        // console.log(index, row);
        // 从级联菜单中过滤掉自身
        this.filteredMultiLevelAccountList = this.getFilteredMultiLevelAccount(row.account, row.user_type)

        this.isAddUser = false
        this.account = row.account
        this.username = row.user_name
        this.password = row.password
        this.showPassword = false
        // 编辑用户对话框，准备用户已有的权限数据
        this.isPlaylistAllowed = checkIfPlaylistAllowed(row.user_authority.authority)
        this.isTimerAllowed = checkIfTimerAllowed(row.user_authority.authority)
        this.isAudioCollectorAllowed = checkIfAudioCollectorAllowed(row.user_authority.authority)
        this.isRepeatedLoginAllowed = checkIfRepeatedLoginAllowed(row.user_authority.authority)
        this.isCreateSubAccountAllowed = checkIfCreateSubUserAllowed(row.user_authority.authority)
        // 回显数据
        this.deviceTypeSelected = -1
        this.preDeviceType = -1
        this.zonesOfDeviceType = this.allTypeZones.filter(zone=>zone.device_model !== deviceModelEnum.NetAudioMixerDecoder)
        this.selectedParentAccount = row.parent_username
        const selectedZoneMacs = this.zonesMacObject[row.account]
        this.partitionSelectTransfer = selectedZoneMacs
        // prepare object
        this.tempZoneSelectedObject = this.prepareTempObjectForAll(selectedZoneMacs)
        // parent account

        // 可选分区列表由watch selectedParentAccount生成
        // this.zonesOfDeviceType = this.selectableZones
        setTimeout(() => {
          this.userDialog = true
        }, 5)
      },
      handleAdvancedEdit(index, row) {
        // console.log(index, row);
        this.clickUserOfMultiLevel = row
        this.clickUserOfMultiLevel.username = row.account
        // console.log(index, row);
        // 从级联菜单中过滤掉自身
        // this.filteredMultiLevelAccountList = this.getFilteredMultiLevelAccount(row.account, row.user_type)

        this.isAddUser = false
        this.account = row.account
        this.username = row.user_name
        this.password = row.password
        this.userType = row.user_type
        this.showPassword = false
        // 编辑用户对话框，准备用户已有的权限数据
        this.isPlaylistAllowed = checkIfPlaylistAllowed(row.user_authority.authority)
        this.isTimerAllowed = checkIfTimerAllowed(row.user_authority.authority)
        this.isAudioCollectorAllowed = checkIfAudioCollectorAllowed(row.user_authority.authority)
        this.isRepeatedLoginAllowed = checkIfRepeatedLoginAllowed(row.user_authority.authority)
        this.isCreateSubAccountAllowed = checkIfCreateSubUserAllowed(row.user_authority.authority)
        // 回显数据
        // this.deviceTypeSelected = -1
        // this.preDeviceType = -1
        // this.zonesOfDeviceType = this.allTypeZones.filter(zone=>zone.device_model !== deviceModelEnum.NetAudioMixerDecoder)
        this.selectedParentAccount = row.parent_username
        // const selectedZoneMacs = this.zonesMacObject[row.account]
        // this.partitionSelectTransfer = selectedZoneMacs
        // // prepare object
        // this.tempZoneSelectedObject = this.prepareTempObjectForAll(selectedZoneMacs)
        // parent account

        // 存储空间回显
        this.storageCapacity = row.storage_capacity ? row.storage_capacity : 0
        this.settingStorageCapacity = this.storageCapacity ? this.storageCapacity : null
        this.usedStorageCapacity = this.getUsedStorageCapacity()
        this.remainStorageCapacity = this.getRemainStorageCapacity()

        setTimeout(() => {
          this.advancedDialog = true
        }, 5)
      },
      handleDelete(index, row) {
        // console.log(index, row);
        this.clickUserOfMultiLevel = row
        this.clickUserOfMultiLevel.username = row.account
        if (row.sub_users && row.sub_users.length > 0) {
          this.errorMessages = this.$t('account.errors.hasSubAccounts')
          this.commonErrorSnackBar = true
        }
        else {
          this.deleteUserDialog = true
        }
      },
      getUsedStorageCapacity() {
        const mySongs = this.localSongListInfo.filter(task => task.account === this.account)
        if (mySongs.length === 0) {
          return 0
        }
        const sizeSum = mySongs.map(song => song.size).reduce((a, b) => a + b, 0)
        // 歌曲size的单位是字节，需要转换成MB,保留小数点后一位
        return (sizeSum / 1024 / 1024).toFixed(1)
      },
      getRemainStorageCapacity() {
        if (this.usedStorageCapacity > this.storageCapacity) {
          return 0
        }
        return (this.storageCapacity - this.usedStorageCapacity).toFixed(1);
      },
      getMultiLevelAccountList() {
        if (this.userInfoData == null) {
          return []
        }
        this.accountId = 1;
        const list = JSON.parse(JSON.stringify(this.userInfoData))
        this.recursionSetZoneUid(list, this.userInfoData.account)
        // 当前登录账户的父账户为空白，代表不改变当前父账户（因为子账户也不知道自己的父级账户是什么，目前后台没有告知）
        list.parent_username=""
        return [list]
      },
      recursionSetZoneUid(userInfoData, parentUsername) {
        userInfoData.uid = this.accountId;
        this.accountId += 1;
        // 显示名称：账号名称（账号）
        userInfoData.show_name = userInfoData.user_name + " (" + userInfoData.account + "）"
        userInfoData.parent_username = parentUsername
        if (userInfoData.sub_users && userInfoData.sub_users.length > 0) {
          // 生成children对象供添加/编辑用户时选择上级,不添加四级账户）
          if (userInfoData.user_type < 3) {
            userInfoData.children = userInfoData.sub_users;
          }
          userInfoData.sub_users.forEach(user => this.recursionSetZoneUid(user, userInfoData.account))
        }
      },
      // 选择不同设备，修改框体
      deviceSelectRoute (selectObj) {
        //1. 更新tempObject
        switch (this.preDeviceType) {
          case -1:
            this.tempZoneSelectedObject = this.prepareTempObjectForAll(this.partitionSelectTransfer)
            break
          default:
            this.updateTempObjectForSingleType(this.preDeviceType, this.partitionSelectTransfer)
        }
        // 回显设备
        switch (selectObj) {
          case -1:
            this.zonesOfDeviceType = this.allTypeZones.filter(zone=>zone.device_model !== deviceModelEnum.NetAudioMixerDecoder)
            break
          case 0:
            this.zonesOfDeviceType = this.decodeZones.filter(zone=>zone.device_model !== deviceModelEnum.NetAudioMixerDecoder)
            break;
          case 1:
            this.zonesOfDeviceType = this.pagers
            break;
          case 3:
            this.zonesOfDeviceType = this.audioCollectors
            break;
          case 4:
            this.zonesOfDeviceType = this.fireCollectors
            break;
          case 5:
            this.zonesOfDeviceType = this.sequencePowers
            break;
          case 6:
            this.zonesOfDeviceType = this.audioMixers
            break;
          case 7:
            this.zonesOfDeviceType = this.remoteControlers
            break;
          case 8:
            this.zonesOfDeviceType = this.phoneGateways
            break;
          case 9:
            this.zonesOfDeviceType = this.ampControlers
            break;
          case 10:
            this.zonesOfDeviceType = this.noiseDetectors
            break;  
          default:
            break;
        }
        this.partitionSelectTransfer = this.tempZoneSelectedObject['' + selectObj]
        this.preDeviceType = selectObj
      },
      // 初始化用户信息
      updateAccountListFromUserInfo() {
        if (this.userInfoData == null) {
          return
        }
        // console.log("updateAccountListFromUserInfo:account...")
        let id = 1;
        this.accountList = [{
          uid: id++,
          username: this.userInfoData.dest_account,
          password: this.userInfoData.dest_password,
          user_type: this.userInfoData.user_type,
          authority: this.userInfoData.user_authority.authority,
          zoneCounts: this.userInfoData.user_authority.zone_count,
          zoneMacs: [],
        }]

        this.userInfoData.sub_users.forEach(user => {

          const subAccount = {
            uid: id++,
            username: user.account,
            password: user.password,
            user_type: user.user_type,
            authority: user.user_authority.authority,
            zoneCounts: user.user_authority.zone_count,
            zoneMacs: this.zonesMacObject[user.account],
          }
          this.accountList.push(subAccount)

          let tempUser = user
          while(tempUser.sub_user_count>0)
          {
            //console.log("tempUser1.account="+tempUser.account)
            if(tempUser.sub_users == null) {
              break
            }
            else {
              tempUser.sub_users.forEach(user => {
                let tempUser2 = user

                const subAccount = {
                uid: id++,
                username: user.account,
                password: user.password,
                user_type: user.user_type,
                authority: user.user_authority.authority,
                zoneCounts: user.user_authority.zone_count,
                zoneMacs: this.zonesMacObject[user.account],
                }
                this.accountList.push(subAccount)

                while(tempUser2.sub_user_count>0)
                {
                  if(tempUser2.sub_users == null) {
                    break
                  }
                  else {
                    tempUser2.sub_users.forEach(user => {
                        const subAccount = {
                          uid: id++,
                          username: user.account,
                          password: user.password,
                          user_type: user.user_type,
                          authority: user.user_authority.authority,
                          zoneCounts: user.user_authority.zone_count,
                          zoneMacs: this.zonesMacObject[user.account],
                        }
                        this.accountList.push(subAccount)
                    })

                    tempUser2 = tempUser2.sub_users
                  }
                }
              })

              tempUser = tempUser.sub_users
              //console.log("tempUser2.account="+tempUser.account)
            }
          }
        })

      },
      updateAccountZoneMacsFromUserInfo() {
        if (this.userInfoData == null) {
          return
        }
        Object.keys(this.zonesMacObject).forEach(key => {
          this.accountList.forEach((account) => {
            if (account.username === key) {
              account.zoneMacs = this.zonesMacObject[key]
            }
          })
        })
      },
      // 更改当前选项卡
      setCurrentTab: function (tab) {
        this.currentTab = tab.name
      },
      preCreateUser: function () {
        this.isAddUser = true
        this.account = ''
        this.username = ''
        this.password = ''
        this.showPassword = false
        // 创建新用户对话框，清空权限数据
        this.isPlaylistAllowed = false
        this.isTimerAllowed = false
        this.isAudioCollectorAllowed = false
        this.isRepeatedLoginAllowed = false
        this.deviceTypeSelected = -1
        this.tempZoneSelectedObject = {}
        this.tempZoneSelectedObject['' + -1] = []
        this.partitionSelectTransfer = []
        // 添加用户默认的上级为用户本身
        this.selectedParentAccount = this.user
        this.zonesOfDeviceType = this.selectableZones.filter(zone=>zone.device_model !== deviceModelEnum.NetAudioMixerDecoder)
        this.userDialog = true
        this.filteredMultiLevelAccountList = this.multiLevelAccountList
      },
      // 旧版客户端使用（不支持多级账号）
      preEditUser: function () {
        if (this.accountSelected.length === 0) {
          this.errorMessages = this.$t('account.errors.selectUserToEdit')
          this.commonErrorSnackBar = true
          return
        }
        this.isAddUser = false
        this.username = this.getAccount.username
        this.password = this.getAccount.password
        this.showPassword = false
        // 编辑用户对话框，准备用户已有的权限数据
        this.isPlaylistAllowed = checkIfPlaylistAllowed(this.getAccount.user_authority.authority)
        this.isTimerAllowed = checkIfTimerAllowed(this.getAccount.user_authority.authority)
        this.isAudioCollectorAllowed = checkIfAudioCollectorAllowed(this.getAccount.user_authority.authority)
        this.isRepeatedLoginAllowed = checkIfRepeatedLoginAllowed(this.getAccount.user_authority.authority)
        this.userDialog = true
        // 回显数据
        this.deviceTypeSelected = -1
        this.zonesOfDeviceType = this.allTypeZones.filter(zone=>zone.device_model !== deviceModelEnum.NetAudioMixerDecoder)
        this.partitionSelectTransfer = this.getAccount.zoneMacs
        // prepare object
        this.tempZoneSelectedObject = this.prepareTempObjectForAll(this.getAccount.zoneMacs)
        // this.editRawZoneSelectObject = this.tempZoneSelectedObject
      },
      prepareTempObjectForAll(zoneMacs) {
        // [0, 1, 3, 4, 5, 7]
        const allTypeList = getAllTypeList();
        const object = {}

        if (zoneMacs == null || zoneMacs.length === 0) {
          object['' + -1] = []
          return object
        }

        zoneMacs.forEach(mac => {
          // -1为全部设备
          if (object['' + -1] == null) {
            object['' + -1] = [mac]
          } else {
            if(!object['' + -1].includes(mac)) {
              object['' + -1].push(mac)
            }
          }

          const zone = this.$ws.getZoneByZoneMac(mac);
          allTypeList.forEach(number => {
            if (getDeviceModelMappingList(number).includes(zone.device_model)) {
              if (object['' + number] == null) {
                object['' + number] = [mac]
              } else {
                if(!object['' + number].includes(mac)) {
                  object['' + number].push(mac)
                }
              }
            }
          })
        })
        return object
      },
      updateTempObjectForSingleType(zoneType, zoneMacs) {
        if (this.tempZoneSelectedObject['' + zoneType] == null) {
          this.tempZoneSelectedObject['' + zoneType] = []
        }
        if (zoneMacs == null || zoneMacs.length === 0) {
          this.tempZoneSelectedObject['' + zoneType] = []
          if (this.tempZoneSelectedObject['-1'] != null && this.tempZoneSelectedObject['-1'].length !== 0) {
            this.tempZoneSelectedObject['-1'] = this.tempZoneSelectedObject['-1'].filter(mac => {
              const zone = this.$ws.getZoneByZoneMac(mac);
              return !getDeviceModelMappingList(this.preDeviceType).includes(zone.device_model);
            })
          }
        } else {
          // zoneMacs不为空
          this.tempZoneSelectedObject['' + zoneType] = zoneMacs
          if (this.tempZoneSelectedObject['-1'] != null && this.tempZoneSelectedObject['-1'].length !== 0) {
            this.tempZoneSelectedObject['-1'] = this.tempZoneSelectedObject['-1'].filter(mac => {
              const zone = this.$ws.getZoneByZoneMac(mac);
              return !getDeviceModelMappingList(this.preDeviceType).includes(zone.device_model);
            })
          }
          else {
            this.tempZoneSelectedObject['-1'] = []
          }
          zoneMacs.forEach(mac => !this.tempZoneSelectedObject['-1'].includes(mac) && this.tempZoneSelectedObject['-1'].push(mac))
          // console.log("zoneMacs length="+zoneMacs.length)
        }
      },
      preDeleteUser: function () {
        if (this.accountSelected.length === 0) {
          this.errorMessages = this.$t('account.errors.selectUserToDelete')
          this.commonErrorSnackBar = true
          return
        }
        this.deleteUserDialog = true
      },
      // 移除用户
      deleteUser: function () {
        if (this.isShowMultiLevelAccount) {
          this.$ws.removeUser(this.clickUserOfMultiLevel.account);
          return
        }
        this.$ws.removeUser(this.getAccount.username);
      },
      // 选中账号
      rowClick: function (item, row) {
        // this.accountSelected = []
        if (row.isSelected) {
          row.select(false)
        } else {
          row.select(true)
        }
      },
      // 获取账户类型
      getAccountType: function (username) {
        // return username === 'admin' ? '管理员' : '操作员'
        let user_type=0;
        for (let i = 0; i < this.accountList.length; i++) {
            if (this.accountList[i].username === username) {
              user_type = this.accountList[i].user_type
              break
            }
        }
        switch(user_type)
        {
          case 1:
            return this.$t('account.userTypes.admin')
          case 2:
            return this.$t('account.userTypes.secondLevel')
          case 3:
            return this.$t('account.userTypes.thirdLevel')
          case 4:
            return this.$t('account.userTypes.fourthLevel')
        }
      },
      // 获取账户密码
      getAccountPassword: function (password) {
        return '******'
      },
      getZonesNameByMacs: function (macs) {
        return this.$ws.getZoneNamesByZoneMacs(macs)
      },
      createOrEditUser: function () {
           if (this.account == null || this.account.length < 3 || this.account.length > 16) {
             this.errorMessages = this.$t('account.errors.accountLengthError')
             this.commonErrorSnackBar = true
             return
           }

        // todo 长度限制和同名限制，交给服务器处理
        // if (this.username != null && this.username.length > 10) {
        //   this.errorMessages = '用户名长度输入超过字数限制，请重新输入'
        //   this.commonErrorSnackBar = true
        //   return
        // }
        // if (isAddUser) {
        //   for (let i = 0; i < this.accountList.length; i++) {
        //     if (this.accountList[i].username === this.username) {
        //       this.errorMessages = '用户名已存在，请重新输入'
        //       this.commonErrorSnackBar = true
        //       return
        //     }
        //   }
        // }
        if (this.password == null || this.password.length < 3 || this.password.length > 16) {
          this.errorMessages = this.$t('account.errors.passwordLengthError')
          this.commonErrorSnackBar = true
          return
        }
        const commandUuid = this.$ws.getUuid()
        //处于当前类型的需要刷新
        if (this.preDeviceType === -1) {
          this.tempZoneSelectedObject['-1'] = this.partitionSelectTransfer
        } else {
          this.updateTempObjectForSingleType(this.preDeviceType, this.partitionSelectTransfer)
        }

        const selectedPartitionList = this.tempZoneSelectedObject['-1']
        const zoneCount = selectedPartitionList!=null ? selectedPartitionList.length:0
        const zoneMacs = selectedPartitionList
        // 组合用户权限，以发送给服务器
        // 设置添加用户时的权限为0
        const authority = this.isAddUser ? 0 : this.getUserAuthority()
        if (this.isAddUser) {
          if (this.isShowMultiLevelAccount) {
            this.$ws.addUserForMultiAccount(commandUuid, this.account, this.username, this.password, this.selectedParentAccount, zoneCount, authority);
          } else {
            this.$ws.addUser(commandUuid, this.username, this.password, zoneCount, authority);
          }
        } else {
          if (this.isShowMultiLevelAccount) {
            this.$ws.editUserForMultiAccount(commandUuid, this.account, this.username, this.password, this.selectedParentAccount, zoneCount, authority);
          } else {
            this.$ws.editUser(commandUuid, this.username, this.password, zoneCount, authority);
          }
        }

        const account = this.isShowMultiLevelAccount ? this.account : this.username
        this.$ws.setUserZone(commandUuid, account, this.password, zoneCount, zoneMacs)
      },
      // 计算用户权限整数
      getUserAuthority() {
        let authority = USER_LIMITS_NONE
        if (this.isPlaylistAllowed) {
          authority += USER_LIMITS_PLAYLIST
        }
        if (this.isTimerAllowed) {
          authority += USER_LIMITS_TIMER
        }
        if (this.isAudioCollectorAllowed) {
          // authority += USER_LIMITS_AUDIO_COLLECTOR
        }
        if (this.isRepeatedLoginAllowed) {
          authority += USER_LIMITS_REPEATED_LOGIN
        }
        if (this.isCreateSubAccountAllowed) {
          authority += USER_LIMITS_CREATE_SUB_USER
        }
        return authority
      },
      // 递归，由account获取userType
      getUserTypeByAccount(obj, account) {
        if (Array.isArray(obj)) {
          for (let i = 0; i < obj.length; i++) {
            const result = this.getUserTypeByAccount(obj[i], account);
            if (result != null) {
              return result;
            }
          }
        } else {
          if (obj.account === account) {
            return obj.user_type;
          }
          if (obj.sub_users && obj.sub_users.length > 0) {
            return this.getUserTypeByAccount(obj.sub_users, account)
          }
        }
        return null
      },
      // 级联菜单中不显示当前编辑的账户
      getFilteredMultiLevelAccount(account, userType) {
        if (this.multiLevelAccountList.length === 0 || this.multiLevelAccountList[0].account === account) {
          return []
        }
        const obj = this.multiLevelAccountList[0]
        if (obj.children == null || obj.children.length === 0) {
          return [obj]
        }
        if (this.user_type >= userType || userType > 3) {
          return [obj]
        }
        const distance = userType - this.user_type;
        switch (distance) {
          case 1:
            const copyObj = JSON.parse(JSON.stringify(obj))
            copyObj.children = copyObj.children.filter(x => x.account !== account)
            return [copyObj]
          case 2:
            const secondObj = JSON.parse(JSON.stringify(obj))
            for (let i = 0; i < secondObj.children.length; i++) {
              if (secondObj.children[i].children == null || secondObj.children[i].children.length === 0) {
                continue
              }
              for (let j = 0; j < secondObj.children[i].children.length; j++) {
                if (secondObj.children[i].children[j].account === account) {
                  secondObj.children[i].children.splice(j, 1)
                  return [secondObj]
                }
              }
            }
            return [secondObj]
          case 3:
            return [obj]
          default:
            return [obj]
        }
      },
      // 用于删除用户对话框
      getDeleteAccount() {
        if (this.isShowMultiLevelAccount) {
          return this.clickUserOfMultiLevel == null ? '' : this.clickUserOfMultiLevel.user_name + " (" + this.clickUserOfMultiLevel.account + "）"
        }
        return this.accountSelected.length === 0 ? '' : this.getAccount.username
      },
      confirmSetUserStorageCapacity() {
        if (this.settingStorageCapacity == null || !this.isNumeric(this.settingStorageCapacity)) {
          this.errorMessages = this.$t('account.errors.invalidStorageSize');
          this.commonErrorSnackBar = true;
          return;
        }
        if (parseInt(this.settingStorageCapacity) < 50 || parseInt(this.settingStorageCapacity) > 1048576) {
          this.errorMessages = this.$t('account.errors.storageSizeRange');
          this.commonErrorSnackBar = true;
          return;
        }
        this.$ws.setUserStorageCapacity(this.account, this.settingStorageCapacity)
      },
      isNumeric(value) {
        return /^\d+$/.test(value);
      },
      confirmSetUserAuthority() {
        const authority = this.getUserAuthority()
        this.$ws.setUserAuthority(this.account, authority)
      },
    },
  beforeRouteEnter(to, from, next) {
    next((vm) => [
      vm.$nextTick(() => {
        vm.$refs.table.doLayout()

        // 调整树形结构缩进
        const elements = document.querySelectorAll('.el-table__indent');
        elements.forEach(e => {
          let originalPadding = Number(e.style.paddingLeft.replace("px", ""))
          if (originalPadding !== 16 && originalPadding !== 32 && originalPadding !== 48) {
            return false
          }
          const nextSibling = e.nextSibling;
          const parentType = e.parentElement.parentElement.parentElement

          let addIndent = 0
          if (parentType.classList.contains("el-table__row--level-1")) {
            addIndent = 4
          } else if (parentType.classList.contains("el-table__row--level-2")) {
            addIndent = 12
          } else if (parentType.classList.contains("el-table__row--level-3")) {
            addIndent = 20
          } else if (parentType.classList.contains("el-table__row--level-4")) {
            addIndent = 28
          }

          if (nextSibling.classList.contains("el-table__expand-icon")) {
            addIndent = addIndent - 4
          }
          e.style.paddingLeft = originalPadding + addIndent + "px"
        })
      })
    ])
  },
}
</script>

<style scoped>
  /deep/ tr.v-data-table__selected {
    background: #c2c9f3 !important;
  }
  body .el-table th.gutter {
    display: table-cell !important;
  }
  .el-table__header th {
    background: #f2f2f6;
    -webkit-box-shadow: 0px 0px 0px 0px #d2d2d2;
    box-shadow: 0px 0px 0px 0px #d2d2d2;
  }
  .el-table__fixed-right{
    height: 100% !important;
  }
  /* Element-UI 的table 组件出现表格线条不对齐的问题 */
  body .el-table th.gutter {
    display: table-cell !important
  }
  /deep/ tr.el-table__indent {
    padding-left: 100px;
  }
</style>
