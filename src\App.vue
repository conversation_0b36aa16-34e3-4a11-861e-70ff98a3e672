<template>
  <div id="app">
    <router-view />
  </div>
</template>

<style>
html { overflow-y: auto }
html, body {
  height: 100%;
}
</style>

<script>
  import store from './store'
  import {mapGetters, mapState} from 'vuex'
  // 定义file_type文件类型，用于获取xml文件的更新
  const fileTypeEnum = Object.freeze({ Group: 1, PlayList: 2, Timer: 3, Device: 4, AudioCollector: 5, FireCollector: 6, Monitor: 7,
                      NetSequencePower: 10, NetPager: 11, RemoteControler: 12 })
  export default {
    name: 'App',
    data: () => ({
    }),
    computed: {
      ...mapState(['uuid', 'fileUpdate', 'userInfoData']),
      ...mapGetters(['isAdmin']),
      // 计算当前的客户标题
      currentTitle() {
        return this.$ws.getCustomerTitle()
      }
    },
    watch: {
      // 监听标题变化，动态更新浏览器标题
      currentTitle: {
        handler(newTitle) {
          if (newTitle) {
            this.updateDocumentTitle(newTitle)
          }
        },
        immediate: true
      },
      // 监听语言变化，更新浏览器标题
      '$i18n.locale': function(newLocale) {
        // 语言变化时重新计算并更新标题
        this.$nextTick(() => {
          const title = this.$ws.getCustomerTitle()
          this.updateDocumentTitle(title)
        })
      },
      uuid: function () {
        if (this.$store.state.uuid !== null) {
          // 登录成功，加载分区，分组及播放列表信息
          // 如果是操作员，先获取自己的分区
          //if (store.state.user !== 'admin') {
            //this.$ws.getUserZone(this.$store.state.user)
          //}
          // 0622 如果是管理员账户,登录后获取用户信息
          //if (this.isAdmin) {
            this.getUserAccountInfo();
          //}
          store.commit('updateZonesInfo')
          store.commit('updateAudioCollectors')
          store.commit('updateDetailAudioCollectorInfo')
          store.commit('updateFireCollector')
          store.commit('updateSequencePower')
          store.commit('updatePagingCenter')
          store.commit('updateAudioMixer')
          store.commit('updateRemoteControler')
          store.commit('updatePhoneGateway')
          store.commit('updateAmpControlers')
          store.commit('updateNoiseDetectors')
          store.commit('updateGroupsInfo')
          store.commit('updateListsInfo')
          store.commit('onlyUpdateTimerInfo')
          store.commit('updateSystemDateTime')
          store.commit('updateAudioMonitor')
          if (this.isAdmin) {
            store.commit('updateMonitors')
          }
          store.commit('updateTodayTimerInfo')
          store.commit('updateManualTaskInfo')
          store.commit('updateLocalSongListInfo')
          store.commit('updateRemoteControllerInfo')
          if (this.isAdmin) {
            store.commit('updateBackupServerInfo')
          }
          // store.commit('updateSystemNetwork')
          // store.commit('updateSystemStorage')
          this.$ws.getPlayMode()
        }
      },
      // 监控分区或其他的改变并实时更新
      fileUpdate: function () {
        // 当fileUpdate更新为初始值时（空对象），不做操作
        if (Object.keys(this.fileUpdate).length === 0) {
          return
        }
        const file = this.fileUpdate;
        switch (file.file_type) {
          case fileTypeEnum.Group:
            this.$store.commit('onlyUpdateGroupsInfo')
            break
          case fileTypeEnum.PlayList:
            this.$store.commit('onlyUpdateListInfo')
            break
          case fileTypeEnum.Device:
            // 20201109 无需主动请求更新分区信息（解码设备），Zone.xml更新，服务器会自动发送get_device_info至客户端
            // this.$store.commit('onlyUpdateZonesInfo')
            break
          case fileTypeEnum.Timer:
            this.$store.commit('onlyUpdateTimerInfo')
            // if (this.$route.name === '定时管理' || this.$route.path === 'dashboard/timer') {
            // 如果当前页面是定时管理，需要刷新
            // console.log('强制刷新')
            // this.$forceUpdate()
            // }
            break
          case fileTypeEnum.NetPager:
            // 20201109 此文件记录音频采集卡对应的音频参数，如采样率、声道数等，暂时参数固定，所以无需处理
            this.$store.commit('updatePagingCenter')
            break
          case fileTypeEnum.AudioCollector:
            // 20201109 此文件记录音频采集卡对应的音频参数，如采样率、声道数等，暂时参数固定，所以无需处理
             this.$store.commit('onlyUpdateAudioCollector')
             this.$store.commit('updateDetailAudioCollectorInfo')
            break
          case fileTypeEnum.FireCollector:
            // 20201109 后续处理
             this.$store.commit('updateFireCollector')
            break
          case fileTypeEnum.Monitor:
            this.$store.commit('updateMonitors')
            break;
          case fileTypeEnum.NetSequencePower:
            //设备基本状态变化，服务器会主动发get_device_info,无需再次获取
            //this.$store.commit('updateSequencePower')
            //******** 电源时序器参数变化后，服务器会主动发送get_sequence_power_info，无需获取详细参数
            break;
          case fileTypeEnum.RemoteControler:
            //设备基本状态变化，服务器会主动发get_device_info,无需再次获取
            //this.$store.commit('updateRemoteControler')
            //******** todo收到远程遥控器参数文件变化后，可以主动获取一次参数
            this.$store.commit('updateRemoteControllerInfo')
            break;
          default:
            break
        }
      },
      // 仅有管理员登录时，发送get_user_zone请求获取分区信息
      userInfoData: {
        handler: function () {
          if (this.userInfoData == null) {
            return
          }
          // console.log("updateAccountListFromUserInfo:app...")
          this.userInfoData.sub_users.forEach(user => {
            let tempUser = user
            while(tempUser.sub_user_count>0)
            {
              //console.log("tempUser1.account="+tempUser.account)
              if(tempUser.sub_users == null) {
                if (!this.$store.state.userAccountSendRequest[tempUser.account]) {
                  this.$ws.getUserInfo(tempUser.account);
                  // 标记为已发送请求
                  this.$store.state.userAccountSendRequest[tempUser.account] = true;
                }
                // console.log("ready get userInfo="+tempUser.account)
                break
              }
              else {
                tempUser.sub_users.forEach(user => {
                    let tempUser2 = user
                    while(tempUser2.sub_user_count>0)
                    {
                      if(tempUser2.sub_users == null) {
                        if (!this.$store.state.userAccountSendRequest[tempUser2.account]) {
                          this.$ws.getUserInfo(tempUser2.account);
                          // 标记为已发送请求
                          this.$store.state.userAccountSendRequest[tempUser2.account] = true;
                        }
                        // console.log("ready get userInfo="+tempUser2.account)
                        break
                      }
                      else {
                        tempUser2 = tempUser2.sub_users
                      }
                    }
                  })

                tempUser = tempUser.sub_users
                //console.log("tempUser2.account="+tempUser.account)
              }
            }
          })

        },
        deep: true,
      },
    },
    created () {
      if (this.$route.name !== 'login' && this.$store.state.loginResult === 0) {
        // 创建时发送websocket连接,12/01增加刷新时重登
        this.$store.commit('WEBSOCKET_INIT', true)
      }
      /*
      // 确保在组件创建时就设置正确的标题
      this.$nextTick(() => {
        const title = this.$ws.getCustomerTitle()
        this.updateDocumentTitle(title)
      })
      */
    },
    methods: {
      getUserAccountInfo() {
        if (this.userInfoData == null) {
          setTimeout(() => this.$ws.getUserInfo(this.$store.state.user), 1)
        }
      },
      // 更新浏览器标题
      updateDocumentTitle(title) {
        if (title && typeof title === 'string') {
          document.title = title
          // 同时更新 HTML 的 title 属性（用于 SEO 和可访问性）
          const htmlElement = document.querySelector('html')
          if (htmlElement) {
            htmlElement.setAttribute('title', title)
          }
        }
      }
    }
  }
</script>
