<template>
  <v-menu offset-y>
    <template v-slot:activator="{ on, attrs }">
      <v-btn
        :class="buttonClass"
        :icon="isIcon"
        :text="!isIcon"
        :color="buttonColor"
        v-bind="attrs"
        v-on="on"
        :title="$t('language.selectLanguage')"
      >
        <v-icon v-if="isIcon">mdi-translate</v-icon>
        <span v-else>
          <v-icon left>mdi-translate</v-icon>
          {{ getCurrentLanguageName() }}
        </span>
      </v-btn>
    </template>
    <v-list>
      <v-list-item
        v-for="(lang, index) in availableLanguages"
        :key="index"
        @click="changeLanguage(lang.code)"
        :class="{ 'primary--text': lang.code === currentLanguage }"
      >
        <v-list-item-icon v-if="lang.code === currentLanguage">
          <v-icon color="primary">mdi-check</v-icon>
        </v-list-item-icon>
        <v-list-item-title>{{ lang.name }}</v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script>
export default {
  name: 'LanguageSelector',
  
  props: {
    isIcon: {
      type: Boolean,
      default: true
    },
    buttonClass: {
      type: String,
      default: ''
    },
    buttonColor: {
      type: String,
      default: ''
    }
  },
  
  data() {
    return {
      availableLanguages: [
        { code: 'zh', name: '简体中文' },
        { code: 'zh-TW', name: '繁體中文' },
        { code: 'en', name: 'English' }
      ]
    }
  },
  
  computed: {
    currentLanguage() {
      return this.$i18n.locale
    }
  },
  
  methods: {
    changeLanguage(langCode) {
      this.$i18n.locale = langCode
      // 保存语言选择到本地存储
      localStorage.setItem('selectedLanguage', langCode)
      // 更新 Vuetify 的语言设置
      if (langCode === 'zh-TW') {
        this.$vuetify.lang.current = 'zhHant'
      } else if (langCode === 'en') {
        this.$vuetify.lang.current = 'en'
      } else {
        this.$vuetify.lang.current = 'zh'
      }

      // 更新 Element UI 的语言设置
      if (this.$setElementLocale) {
        this.$setElementLocale(langCode)
      }

      // 更新浏览器标题
      this.$nextTick(() => {
        if (this.$ws && this.$ws.getCustomerTitle) {
          const title = this.$ws.getCustomerTitle()
          this.updateDocumentTitle(title)
        }
      })

      // 发出语言变更事件
      this.$emit('language-changed', langCode)
    },
    
    getCurrentLanguageName() {
      const currentLang = this.availableLanguages.find(lang => lang.code === this.currentLanguage)
      return currentLang ? currentLang.name : '简体中文'
    },

    // 更新浏览器标题
    updateDocumentTitle(title) {
      if (title && typeof title === 'string') {
        document.title = title
        // 同时更新 HTML 的 title 属性（用于 SEO 和可访问性）
        const htmlElement = document.querySelector('html')
        if (htmlElement) {
          htmlElement.setAttribute('title', title)
        }
      }
    }
  },
  
  mounted() {
    // 从本地存储恢复语言设置
    const savedLanguage = localStorage.getItem('selectedLanguage')
    if (savedLanguage && ['zh', 'en', 'zh-TW'].includes(savedLanguage)) {
      this.changeLanguage(savedLanguage)
    }
  }
}
</script>

<style scoped>
.v-list-item--active {
  background-color: rgba(25, 118, 210, 0.12);
}
</style>
