import Vue from 'vue'
import {customerVersion} from "@/plugins/websocket";

Vue.mixin({
  methods: {
    /* 根据ip或者名称过滤分区 */
    filterZoneByIpOrName(query, item) {
      return item.name.indexOf(query) > -1 || item.ip.indexOf(query) > -1;
    },
    /* 判断是否是云广播系统C4A0或者C4B0 */
    isCloudIpSystem() {
      return customerVersion === 'C4A0' || customerVersion === 'C4B0' || customerVersion === 'C4A2';
    },
    isNotCloudIpSystem() {
      return !this.isCloudIpSystem();
    },
    isCloudIpSystemB() {
      return customerVersion === 'C4B0';
    },
  }
})
