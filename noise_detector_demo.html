<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>噪声自适应终端设置界面演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            color: #1976d2;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .switch {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .switch input {
            margin-right: 10px;
        }
        .channels-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .channel-card {
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            color: white;
            font-weight: bold;
            transition: transform 0.3s ease;
        }
        .channel-card:hover {
            transform: translateY(-2px);
        }
        .channel-active {
            background-color: #4caf50;
        }
        .channel-inactive {
            background-color: #9e9e9e;
        }
        .average-display {
            background-color: #2196f3;
            color: white;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .segments-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        .segment-control {
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background-color: #fafafa;
        }
        .segment-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #666;
        }
        .input-group {
            margin-bottom: 10px;
        }
        .input-group label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .input-group input {
            width: 100%;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 14px;
        }
        .fixed-noise-display {
            background-color: #f5f5f5;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 10px;
            text-align: center;
            margin-bottom: 10px;
        }
        .noise-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .noise-value {
            font-size: 18px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 5px;
        }
        .fixed-label {
            font-size: 10px;
            color: #999;
        }
        .partition-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .btn {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #1565c0;
        }
        .actions {
            text-align: right;
            margin-top: 30px;
        }
        .btn-secondary {
            background-color: #757575;
            margin-right: 10px;
        }
        .btn-secondary:hover {
            background-color: #616161;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>噪声自适应终端设置界面演示</h1>
            <p>Noise Adaptive Terminal Settings Demo</p>
        </div>

        <!-- 启用开关 -->
        <div class="section">
            <div class="section-title">启用设置</div>
            <div class="switch">
                <input type="checkbox" id="enableSwitch" onchange="toggleEnable()">
                <label for="enableSwitch">启用噪声自适应</label>
            </div>
        </div>

        <!-- 噪声检测器通道 -->
        <div class="section" id="channelsSection" style="display: none;">
            <div class="section-title">噪声检测器通道</div>
            <div class="channels-grid" id="channelsGrid">
                <!-- 通道将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 平均噪声值 -->
        <div class="section" id="averageSection" style="display: none;">
            <div class="section-title">平均噪声值</div>
            <div class="average-display" id="averageDisplay">
                平均噪声值: 0 dB
            </div>
        </div>

        <!-- 噪声/音量设置 -->
        <div class="section" id="settingsSection" style="display: none;">
            <div class="section-title">噪声/音量设置</div>

            <!-- 8段式调节 -->
            <div class="segments-grid" id="segmentsGrid">
                <!-- 段控制将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 分区绑定 -->
        <div class="section" id="partitionSection" style="display: none;">
            <div class="section-title">分区绑定</div>
            <div class="partition-section">
                <span>已选择分区: <span id="selectedCount">0</span></span>
                <button class="btn" onclick="selectPartitions()">选择分区</button>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions">
            <button class="btn btn-secondary">取消</button>
            <button class="btn" onclick="saveSettings()">确认</button>
        </div>
    </div>

    <script>
        // 模拟数据
        let channelData = [65, 72, 0, 58, 81, 0, 69, 75]; // 8个通道的噪声值
        let fixedNoiseValues = [30, 40, 50, 60, 70, 80, 90, 100]; // 8段固定噪声值
        let volumeValues = [10, 20, 30, 40, 50, 60, 70, 80]; // 8段音量值

        function toggleEnable() {
            const enabled = document.getElementById('enableSwitch').checked;
            const sections = ['channelsSection', 'averageSection', 'settingsSection', 'partitionSection'];

            sections.forEach(id => {
                document.getElementById(id).style.display = enabled ? 'block' : 'none';
            });

            if (enabled) {
                generateChannels();
                generateSegments();
                updateAverage();
            }
        }

        function generateChannels() {
            const grid = document.getElementById('channelsGrid');
            grid.innerHTML = '';
            
            for (let i = 0; i < 8; i++) {
                const card = document.createElement('div');
                card.className = `channel-card ${isChannelValid(i) ? 'channel-active' : 'channel-inactive'}`;
                card.innerHTML = `
                    <div>CH${i + 1}</div>
                    <div>${isChannelValid(i) ? channelData[i] + 'dB' : '无效'}</div>
                `;
                grid.appendChild(card);
            }
        }

        function generateSegments() {
            const grid = document.getElementById('segmentsGrid');
            grid.innerHTML = '';

            for (let i = 0; i < 8; i++) {
                const segment = document.createElement('div');
                segment.className = 'segment-control';
                segment.innerHTML = `
                    <div class="segment-title">段 ${i + 1}</div>
                    <div class="fixed-noise-display">
                        <div class="noise-label">噪声值 (dB)</div>
                        <div class="noise-value">${fixedNoiseValues[i]} dB</div>
                        <div class="fixed-label">固定值</div>
                    </div>
                    <div class="input-group">
                        <label>音量值 (%)</label>
                        <input type="number" min="1" max="100" value="${volumeValues[i]}"
                               onchange="updateVolume(${i}, this.value)">
                    </div>
                `;
                grid.appendChild(segment);
            }
        }

        function isChannelValid(index) {
            return channelData[index] > 0 && channelData[index] >= 30 && channelData[index] <= 130;
        }

        function updateAverage() {
            const validChannels = channelData.filter((value, index) => isChannelValid(index));
            const average = validChannels.length > 0 ? 
                Math.round(validChannels.reduce((sum, val) => sum + val, 0) / validChannels.length) : 0;
            
            document.getElementById('averageDisplay').textContent = `平均噪声值: ${average} dB`;
        }

        function updateVolume(segmentIndex, newValue) {
            volumeValues[segmentIndex] = parseInt(newValue);
        }

        function validateVolumeSettings() {
            // 检查每段音量值是否在有效范围内
            for (let i = 0; i < 8; i++) {
                const volume = volumeValues[i];
                if (volume < 1 || volume > 100) {
                    return {
                        isValid: false,
                        message: `第${i + 1}段（${fixedNoiseValues[i]}dB）的音量值无效，应在1-100之间`
                    };
                }
            }

            // 检查音量值是否递增（下一段不能比上一段低）
            for (let i = 1; i < 8; i++) {
                const currentVolume = volumeValues[i];
                const previousVolume = volumeValues[i - 1];
                if (currentVolume < previousVolume) {
                    return {
                        isValid: false,
                        message: `第${i + 1}段（${fixedNoiseValues[i]}dB）的音量值${currentVolume}%不能低于第${i}段（${fixedNoiseValues[i - 1]}dB）的音量值${previousVolume}%`
                    };
                }
            }

            return { isValid: true };
        }

        function selectPartitions() {
            // 模拟分区选择
            const count = Math.floor(Math.random() * 5) + 1;
            document.getElementById('selectedCount').textContent = count;
        }

        function saveSettings() {
            const validation = validateVolumeSettings();
            if (!validation.isValid) {
                // 模拟项目中的错误显示方式
                showErrorMessage(validation.message);
                return;
            }

            showSuccessMessage('设置已保存！\n\n功能包括：\n- 噪声自适应启用/关闭\n- 8路噪声检测器通道监控\n- 平均噪声值计算\n- 8段式噪声/音量设置（固定噪声值：30-100dB）\n- 音量值验证（1-100%，递增）\n- 分区绑定功能');
        }

        function showErrorMessage(message) {
            // 创建错误提示框
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: #f44336;
                color: white;
                padding: 16px 24px;
                border-radius: 4px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 1000;
                max-width: 400px;
                font-size: 14px;
                line-height: 1.4;
            `;
            errorDiv.textContent = message;

            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                float: right;
                margin-left: 10px;
                cursor: pointer;
                padding: 0;
                line-height: 1;
            `;
            closeBtn.onclick = () => document.body.removeChild(errorDiv);

            errorDiv.appendChild(closeBtn);
            document.body.appendChild(errorDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (document.body.contains(errorDiv)) {
                    document.body.removeChild(errorDiv);
                }
            }, 3000);
        }

        function showSuccessMessage(message) {
            // 创建成功提示框
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: #4caf50;
                color: white;
                padding: 16px 24px;
                border-radius: 4px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 1000;
                max-width: 400px;
                font-size: 14px;
                line-height: 1.4;
            `;
            successDiv.textContent = message;

            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                float: right;
                margin-left: 10px;
                cursor: pointer;
                padding: 0;
                line-height: 1;
            `;
            closeBtn.onclick = () => document.body.removeChild(successDiv);

            successDiv.appendChild(closeBtn);
            document.body.appendChild(successDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (document.body.contains(successDiv)) {
                    document.body.removeChild(successDiv);
                }
            }, 3000);
        }
    </script>
</body>
</html>
