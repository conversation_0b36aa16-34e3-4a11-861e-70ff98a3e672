<template>
  <v-app>
    <v-snackbar
      v-model="snackbar"
      :color="snackbarColor"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn :color="snackbarColor" fab small class="ml-6" v-bind="attrs" @click="snackbar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <v-container
      class="fill-height"
      fluid
      :style="dynamicBackgroundStyle"
    >
      <v-row
        align="center"
        justify="center"
      >
          <v-card
            class="elevation-12 transparent"
            width="550px"
          >
            <v-toolbar
              color="primary"
            >
              <v-toolbar-title style="font-size: 2.0em;">{{ pageTitle }}</v-toolbar-title>
              <v-spacer />
              <!-- 语言选择器 -->
              <LanguageSelector
                button-color="white"
                @language-changed="onLanguageChanged"
              />
            </v-toolbar>
            <v-card-text>
              <v-form v-model="valid" @keyup.native.enter="login">
                <v-text-field
                  ref="username"
                  v-model="username"
                  :rules="usernameRules"
                  prepend-icon="mdi-account"
                  :label="$t('login.username')"
                  :placeholder="$t('login.usernamePlaceholder')"
                  required
                  height="50px"
                />
                <v-text-field
                  ref="password"
                  v-model="password"
                  :rules="username === 'admin' ? [] : passwordRulesForOperator"
                  :append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                  :type="showPassword ? 'text' : 'password'"
                  prepend-icon="mdi-lock"
                  :label="$t('login.password')"
                  :placeholder="$t('login.passwordPlaceholder')"
                  counter
                  required
                  height="50px"
                  @click:append="showPassword = !showPassword"
                />
              </v-form>
            </v-card-text>
            <v-divider class="mt-5" />
            <v-card-actions>
              <v-spacer />
              <v-btn
                align-center
                justify-center
                color="primary"
                :loading="loading"
                :disabled="loading"
                @click="login"
                class="text-lg-h3"
                type="submit"
              >
                {{ $t('login.loginButton') }}
              </v-btn>
            </v-card-actions>
          </v-card>
      </v-row>
    </v-container>
    <div v-if="checkIfShowICP" style="position:fixed;text-align:center;bottom:20px;margin:0 auto;width:100%;font-size:18px">
      <a target="_blank" style="color: #ffffff" href="https://beian.miit.gov.cn">粤ICP备2023052512号</a>&nbsp;
      <span style="color:orange">{{ $t('upgrade') }}</span> </div>
    </div>
  </v-app>
</template>

<style>
/* 修改通知栏的文字大小 */
.v-snack__content {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: 1.35rem;
  font-weight: 400;
  /*letter-spacing: 0.0178571429em;*/
  line-height: 2rem;
  margin-left: 10px;
  margin-right: auto;
  padding: 14px 16px;
  text-align: initial;
}

/* 当不需要滚动时，隐藏滚动条 */
html {
  overflow: auto !important;
  -ms-overflow-style: none;
}
</style>

<style scoped>
  /*修改文本框label的字体大小*/
  .v-input >>> .v-label{
    font-size: 1.2em !important;
  }
  .transparent {
    background-color: white!important;
    opacity: 0.85; /*透明度修改*/
    border-color: transparent!important;
  }
</style>

<script>
  import { mapState } from 'vuex'
  import { loginBackgroundImg, customerVersion }  from "@/plugins/websocket";
  import LanguageSelector from '@/components/LanguageSelector.vue'

  export default {
    components: {
      LanguageSelector
    },
    data: function () {
      return {
        loading: false, // 加载框
        valid: false, // 表单校验是否成功
        username: '',
        password: '',
        errorMessages: '',
        snackbar: false,
        snackbarTimeout: 1500,
        snackbarColor: 'primary',
        showPassword: false,
        websocketConnectionTimeout: 2000, // 1500ms未建立websocket连接成功，则停止连接
        isAlreadyAlertNetworkIssue: false, // 用于标志错误提示框已出现，防止重复网络错误提示
      }
    },
    computed: {
      ...mapState(['loginResult', 'wsStatus', 'uuid', 'user', 'websocket', 'errorId', 'errorWsMessage']),
      pageTitle() {
        return this.$ws.getCustomerTitle()
      },
      backgroundImagePath() {
        if (loginBackgroundImg == null || loginBackgroundImg === '') {
          return null
        }
        return require("@/assets/" + loginBackgroundImg);
      },
      dynamicBackgroundStyle() {
        // 如果当前用户设定的背景图片不为空，则添加背景图片至登录页面
        return this.backgroundImagePath ? {
          backgroundImage: `url(${this.backgroundImagePath})`,
          backgroundSize: `cover`,
          height: `100vh`
        } : '';
      },
      checkIfShowICP() {
        return (customerVersion === 'C0A1')
      },
      usernameRules() {
        return [
          v => !!v || this.$t('login.usernameRequired'),
        ]
      },
      passwordRulesForOperator() {
        return [
          v => !!v || this.$t('login.passwordRequired')
        ]
      }
    },
    watch: {
      // 统一错误处理
      errorId: function () {
        if (this.$route.fullPath !== '/') {
          return;
        }
        if (this.$store.state.errorId !== null) {
          this.loading = false
          this.errorMessages = this.$store.state.errorWsMessage
          this.snackbarColor = 'red'
          this.snackbar = true
        }
      },
      wsStatus() {
        if (this.$route.fullPath !== '/') {
          return;
        }
        if (this.wsStatus === 'open') {
          // ws连接建立，只有当点击登录按钮（此时loading为true)，才发送登录请求
          // 20220226 否则弹出错误提示（出现这种情况比如设定的时间刚好连接上，但是按钮loading状态已经解除）
          if (this.loading === true) {
            const password = this.username === 'admin' && this.password === '' ? 'admin' : this.password
            this.$ws.authSuccess(this.username, password)
            this.$store.commit('SET_PASSWORD', password)
          } else if (!this.isAlreadyAlertNetworkIssue) {
            this.snackbarColor = 'red'
            this.errorMessages = this.$t('login.serverConnectionFailed')
            this.snackbar = true
          }
        }
      },
      loginResult: function () {
        if (this.loginResult !== 0) {
          return
        }
        this.snackbarColor = 'primary';
        this.errorMessages = this.$t('login.loginSuccess');
        this.snackbar = true;
        setTimeout(() => {
          // 防止多次点击造成重复发送登录请求
          this.$router.push({ path: '/dashboard/dashboard' })
        }, 400)
      },
    },
    // Sends action to Vuex that will log you in and redirect to the dash otherwise, error
    methods: {
      login: function () {
        this.isAlreadyAlertNetworkIssue = false
        // 判断表单状态
        if (!this.valid) {
          this.snackbarColor = 'red'
          this.snackbar = true
          return
        }
        const password = this.username === 'admin' && this.password === '' ? 'admin' : this.password
        this.loading = true
        if (this.wsStatus === 'open') {
          // 服务器已连接情况下，发送登录请求
          this.$ws.authSuccess(this.username, password);
          this.$store.commit('SET_PASSWORD', password);
        } else {
          this.$ws.init(false);
          setTimeout(() => {
            if (this.wsStatus !== 'open') {
              this.snackbarColor = 'red'
              this.loading = false
              this.errorMessages = this.$t('login.serverConnectionFailed')
              this.isAlreadyAlertNetworkIssue = true
              this.snackbar = true
            }
          }, this.websocketConnectionTimeout);
        }
      },
      onLanguageChanged(langCode) {
        // 语言变更时的回调处理
        this.errorMessages = this.$t('login.loginError')
      },
    },
    mounted() {
      if (this.$isPlaceDefaultUserAdmin) {
        // 如果配置项IS_PLACE_DEFAULT_USER_ADMIN为true，则预设admin为用户名
        this.username = 'admin'
      }
      this.loading = false
      this.isAlreadyAlertNetworkIssue = false

      // 设置默认错误消息
      this.errorMessages = this.$t('login.loginError')
    }
  }
</script>
