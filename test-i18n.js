// 简单的多语言功能测试脚本
const fs = require('fs');
const path = require('path');

// 读取语言文件
const zhFile = path.join(__dirname, 'src/locales/zh.json');
const enFile = path.join(__dirname, 'src/locales/en.json');
const zhTWFile = path.join(__dirname, 'src/locales/zh-TW.json');

console.log('🌍 多语言功能测试');
console.log('==================');

try {
  // 检查语言文件是否存在
  const zhExists = fs.existsSync(zhFile);
  const enExists = fs.existsSync(enFile);
  const zhTWExists = fs.existsSync(zhTWFile);
  
  console.log('✅ 语言文件检查:');
  console.log(`   简体中文 (zh.json): ${zhExists ? '✓' : '✗'}`);
  console.log(`   英文 (en.json): ${enExists ? '✓' : '✗'}`);
  console.log(`   繁体中文 (zh-TW.json): ${zhTWExists ? '✓' : '✗'}`);
  
  if (zhExists && enExists && zhTWExists) {
    // 读取并解析语言文件
    const zh = JSON.parse(fs.readFileSync(zhFile, 'utf8'));
    const en = JSON.parse(fs.readFileSync(enFile, 'utf8'));
    const zhTW = JSON.parse(fs.readFileSync(zhTWFile, 'utf8'));
    
    console.log('\n✅ 语言文件内容检查:');
    
    // 检查关键翻译键
    const keyPaths = [
      'login.title',
      'login.username',
      'login.password',
      'appbar.cloudControl',
      'routes.controlCenter',
      'routes.deviceManagement',
      'language.selectLanguage',
      'common.confirm',
      'common.cancel'
    ];
    
    keyPaths.forEach(keyPath => {
      const keys = keyPath.split('.');
      let zhValue = zh;
      let enValue = en;
      let zhTWValue = zhTW;
      
      for (const key of keys) {
        zhValue = zhValue && zhValue[key];
        enValue = enValue && enValue[key];
        zhTWValue = zhTWValue && zhTWValue[key];
      }
      
      const hasZh = zhValue !== undefined;
      const hasEn = enValue !== undefined;
      const hasZhTW = zhTWValue !== undefined;
      
      console.log(`   ${keyPath}:`);
      console.log(`     简体中文: ${hasZh ? '✓' : '✗'} ${hasZh ? `"${zhValue}"` : ''}`);
      console.log(`     英文: ${hasEn ? '✓' : '✗'} ${hasEn ? `"${enValue}"` : ''}`);
      console.log(`     繁体中文: ${hasZhTW ? '✓' : '✗'} ${hasZhTW ? `"${zhTWValue}"` : ''}`);
    });
    
    console.log('\n✅ 组件文件检查:');
    
    // 检查关键组件文件
    const componentFiles = [
      'src/components/LanguageSelector.vue',
      'src/views/dashboard/Login.vue',
      'src/views/dashboard/components/core/AppBar.vue',
      'src/views/dashboard/components/core/Drawer.vue',
      'src/i18n.js'
    ];
    
    componentFiles.forEach(file => {
      const exists = fs.existsSync(path.join(__dirname, file));
      console.log(`   ${file}: ${exists ? '✓' : '✗'}`);
    });
    
    console.log('\n🎉 多语言功能测试完成！');
    console.log('\n📝 使用说明:');
    console.log('1. 在登录页面和AppBar中可以看到语言选择器（翻译图标）');
    console.log('2. 点击语言选择器可以切换简体中文、英文、繁体中文');
    console.log('3. 语言选择会保存到本地存储，刷新页面后会恢复');
    console.log('4. 所有界面文本都会根据选择的语言动态更新');
    
  } else {
    console.log('\n❌ 部分语言文件缺失，请检查文件是否正确创建');
  }
  
} catch (error) {
  console.error('❌ 测试过程中出现错误:', error.message);
}
