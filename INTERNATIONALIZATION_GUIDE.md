# 多语言功能实现指南

## 概述

本项目已成功实现了完整的多语言功能，支持简体中文、英文和繁体中文三种语言。用户可以在登录页面和应用程序的AppBar中切换语言，语言选择会自动保存并在页面刷新后恢复。

## 功能特性

### ✅ 已实现的功能

1. **三种语言支持**
   - 简体中文 (zh)
   - 英文 (en) 
   - 繁体中文 (zh-TW)

2. **语言选择器组件**
   - 可重用的 `LanguageSelector` 组件
   - 支持图标模式和文本模式
   - 显示当前选中语言的标识

3. **语言切换位置**
   - 登录页面工具栏
   - 应用程序AppBar
   - 可在任何页面添加

4. **持久化存储**
   - 语言选择保存到 localStorage
   - 页面刷新后自动恢复语言设置
   - 应用启动时自动加载保存的语言

5. **全面的国际化覆盖**
   - 登录页面所有文本
   - 导航菜单项
   - 按钮和操作文本
   - 错误和提示消息
   - 对话框内容

## 文件结构

```
src/
├── i18n.js                          # 国际化配置文件
├── locales/                         # 语言文件目录
│   ├── zh.json                      # 简体中文
│   ├── en.json                      # 英文
│   └── zh-TW.json                   # 繁体中文
├── components/
│   └── LanguageSelector.vue         # 语言选择器组件
├── mixins/
│   └── languageMixin.js             # 语言切换混入
└── views/
    └── dashboard/
        ├── Login.vue                # 登录页面（已国际化）
        └── components/core/
            ├── AppBar.vue           # 应用栏（已国际化）
            └── Drawer.vue           # 侧边栏（已国际化）
```

## 使用方法

### 1. 在组件中使用翻译

```vue
<template>
  <div>
    <!-- 使用 $t() 函数进行翻译 -->
    <h1>{{ $t('login.title') }}</h1>
    <button>{{ $t('common.confirm') }}</button>
  </div>
</template>
```

### 2. 添加语言选择器

```vue
<template>
  <div>
    <!-- 图标模式 -->
    <LanguageSelector />
    
    <!-- 文本模式 -->
    <LanguageSelector :is-icon="false" />
    
    <!-- 自定义样式 -->
    <LanguageSelector 
      button-class="my-custom-class"
      button-color="primary"
      @language-changed="onLanguageChanged"
    />
  </div>
</template>

<script>
import LanguageSelector from '@/components/LanguageSelector.vue'

export default {
  components: {
    LanguageSelector
  },
  methods: {
    onLanguageChanged(langCode) {
      console.log('语言已切换到:', langCode)
    }
  }
}
</script>
```

### 3. 添加新的翻译键

在三个语言文件中添加相同的键：

**zh.json:**
```json
{
  "newSection": {
    "title": "新标题",
    "description": "新描述"
  }
}
```

**en.json:**
```json
{
  "newSection": {
    "title": "New Title",
    "description": "New Description"
  }
}
```

**zh-TW.json:**
```json
{
  "newSection": {
    "title": "新標題",
    "description": "新描述"
  }
}
```

## 翻译键结构

### 主要分类

- `login.*` - 登录相关
- `appbar.*` - 应用栏相关
- `routes.*` - 路由/菜单相关
- `language.*` - 语言选择相关
- `common.*` - 通用操作
- `tts.*` - 文字转语音相关

### 示例翻译键

```json
{
  "login": {
    "title": "登录",
    "username": "用户名",
    "password": "密码",
    "loginButton": "登录",
    "loginSuccess": "登录成功"
  },
  "routes": {
    "controlCenter": "控制中心",
    "deviceManagement": "设备管理",
    "mediaManagement": "媒体管理"
  },
  "common": {
    "confirm": "确认",
    "cancel": "取消",
    "save": "保存"
  }
}
```

## 技术实现

### 1. Vue I18n 配置

```javascript
// src/i18n.js
import Vue from 'vue'
import VueI18n from 'vue-i18n'
import en from 'vuetify/lib/locale/en'
import zh from 'vuetify/lib/locale/zh-Hans'
import zhHant from 'vuetify/lib/locale/zh-Hant'

Vue.use(VueI18n)

const messages = {
  zh: {
    ...require('@/locales/zh.json'),
    $vuetify: zh,
  },
  en: {
    ...require('@/locales/en.json'),
    $vuetify: en,
  },
  'zh-TW': {
    ...require('@/locales/zh-TW.json'),
    $vuetify: zhHant,
  },
}

export default new VueI18n({
  locale: process.env.VUE_APP_I18N_LOCALE || 'zh',
  fallbackLocale: process.env.VUE_APP_I18N_FALLBACK_LOCALE || 'zh',
  messages,
  silentTranslationWarn: true,
})
```

### 2. 语言切换逻辑

```javascript
changeLanguage(langCode) {
  this.$i18n.locale = langCode
  localStorage.setItem('selectedLanguage', langCode)
  
  // 更新 Vuetify 语言
  if (langCode === 'zh-TW') {
    this.$vuetify.lang.current = 'zhHant'
  } else if (langCode === 'en') {
    this.$vuetify.lang.current = 'en'
  } else {
    this.$vuetify.lang.current = 'zh'
  }
}
```

## 测试

运行测试脚本验证多语言功能：

```bash
node test-i18n.js
```

## 最佳实践

1. **一致性**: 确保所有语言文件包含相同的键
2. **命名规范**: 使用有意义的嵌套键名
3. **翻译质量**: 确保翻译准确且符合当地习惯
4. **测试**: 在所有语言下测试界面布局
5. **维护**: 添加新功能时同步更新所有语言文件

## 扩展支持

要添加新语言：

1. 在 `src/locales/` 创建新的语言文件
2. 在 `src/i18n.js` 中添加语言配置
3. 在 `LanguageSelector.vue` 中添加语言选项
4. 更新 Vuetify 语言映射（如需要）

## 总结

多语言功能已完全集成到项目中，提供了：
- 完整的用户界面国际化
- 便捷的语言切换体验
- 持久化的语言设置
- 可扩展的架构设计

用户现在可以根据需要在简体中文、英文和繁体中文之间自由切换，所有界面元素都会相应更新。
