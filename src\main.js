// =========================================================
// * Vuetify Material Dashboard - v2.1.0
// =========================================================
//
// * Product Page: https://www.creative-tim.com/product/vuetify-material-dashboard
// * Copyright 2019 Creative Tim (https://www.creative-tim.com)
//
// * Coded by Creative Tim
//
// =========================================================
//
// * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

import '@mdi/font/css/materialdesignicons.css'
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import './plugins/base'
import './plugins/chartist'
import './plugins/vee-validate'
import './plugins/jsmpeg.min'
import './plugins/generalMixin'
import vuetify from './plugins/vuetify'
import i18n from './i18n'
import * as websocket from './plugins/websocket'
import * as utils from './plugins/utils'
import {Transfer, Button, Checkbox, Tree, Table, TableColumn, Input, InputNumber, CheckboxButton, CheckboxGroup} from 'element-ui'
import VueSlider from 'vue-slider-component'
import 'vue-slider-component/theme/default.css'
// import 'element-ui/lib/theme-chalk/index.css'
import axios from 'axios'

// Element UI 国际化
import locale from 'element-ui/lib/locale'
import langZh from 'element-ui/lib/locale/lang/zh-CN'
import langEn from 'element-ui/lib/locale/lang/en'
import langZhTW from 'element-ui/lib/locale/lang/zh-TW'

Vue.use(Transfer)
Vue.use(Button)
Vue.use(Tree)
Vue.use(Table)
Vue.use(TableColumn)
Vue.use(Input, InputNumber, Checkbox, CheckboxButton, CheckboxGroup)
Vue.config.productionTip = false
Vue.prototype.$ws = websocket
Vue.prototype.$util = utils
Vue.component('VueSlider', VueSlider)

// 创建全局的Element UI语言切换函数
Vue.prototype.$setElementLocale = function(langCode) {
  if (langCode === 'zh-TW') {
    locale.use(langZhTW)
  } else if (langCode === 'en') {
    locale.use(langEn)
  } else {
    locale.use(langZh)
  }
}

/**
 * 外置配置默认值（当增加外部配置，需在此处增加默认值，防止读取配置文件出错）
 * @type {Map<string, string>}
 */
const configDefaultValueMap = new Map([
  ["WEBSOCKET_PORT", '8081'],   // websocket连接端口
  ["CAMERA_PORT", '8082'],   // 摄像头连接端口
  ["CGI_PORT", '9999'],   // cgi服务连接端口
  ["IS_PLACE_DEFAULT_USER_ADMIN", 'true']   // cgi服务连接端口
])

/**
 * get property from config file, if value is null, then read the default value
 * @param response
 * @param configName
 */
function getConfigValue(response, configName) {
  if (response == null || response.data == null) {
    return configDefaultValueMap.get(configName)
  }
  const data = response.data
  const configValue = data[configName]
  return (configValue == null || configValue.trim() === '') ? configDefaultValueMap.get(configName) : configValue
}

let startApp = function () {
  const protocol = (document.location.protocol === "https" || document.location.protocol === "https:") ? "https" : "http"
  const host = document.location.host // ip:port of current page
  const propertyFilePath = protocol + '://' + host + '/' + 'config.json'
  // console.log("document.location.protocol="+document.location.protocol+",properyFilePath="+propertyFilePath)
  // 固定发送请求到 http://ip:port/config.json，防止找不到json文件而刷新页面失败
  axios.get(propertyFilePath, {headers: {"Cache-Control": "no-cache"}})
    .then((res) => {
      const websocketIP = process.env.VUE_APP_IS_DEV === 'true' ? process.env.VUE_APP_BASE_URL : document.location.hostname // ip地址
      const websocketPort = getConfigValue(res, 'WEBSOCKET_PORT') // ws端口
      const cameraPort = getConfigValue(res, 'CAMERA_PORT') // 摄像头端口
      const cgiPort = getConfigValue(res, 'CGI_PORT') // cgi端口
      const isPlaceDefaultUserAdmin = getConfigValue(res, 'IS_PLACE_DEFAULT_USER_ADMIN') === 'true'
      Vue.prototype.$wsHost = 'ws://' + websocketIP + ':' + websocketPort
      Vue.prototype.$cameraHost = 'ws://' + websocketIP + ':' + cameraPort
      Vue.prototype.$cgiHost = 'http://' + websocketIP + ':' + cgiPort
      Vue.prototype.$cgiPort = cgiPort
      Vue.prototype.$isPlaceDefaultUserAdmin = isPlaceDefaultUserAdmin
      //增加对https的支持，目前只用于明辉智能，wss和cgi均通过后台服务器反向代理实现
      if(protocol === "https") {
        Vue.prototype.$wsHost = 'wss://' + websocketIP + '/wss/' + websocketPort
        Vue.prototype.$cgiHost = 'https://' + websocketIP + '/upload/' + websocketPort
      }

      Vue.prototype.$cgiAddSongAddress = Vue.prototype.$cgiHost + '/cgi-bin/Addsong.cgi'
      Vue.prototype.$cgiUpdateDeviceAddress = Vue.prototype.$cgiHost + '/cgi-bin/DeviceUpdata.cgi'
      Vue.prototype.$cgiUpgradeSystemAddress = Vue.prototype.$cgiHost + '/cgi-bin/UpgradeSystem.cgi'
      Vue.prototype.$cgiBackupFileUpdateAddress = Vue.prototype.$cgiHost + '/cgi-bin/BackupFileUpdate.cgi'

      // 恢复保存的语言设置
      const savedLanguage = localStorage.getItem('selectedLanguage')
      if (savedLanguage && ['zh', 'en', 'zh-TW'].includes(savedLanguage)) {
        i18n.locale = savedLanguage
        // 更新 Vuetify 的语言设置
        if (savedLanguage === 'zh-TW') {
          vuetify.framework.lang.current = 'zhHant'
          // 设置 Element UI 语言为繁体中文
          locale.use(langZhTW)
        } else if (savedLanguage === 'en') {
          vuetify.framework.lang.current = 'en'
          // 设置 Element UI 语言为英文
          locale.use(langEn)
        } else {
          vuetify.framework.lang.current = 'zh'
          // 设置 Element UI 语言为简体中文
          locale.use(langZh)
        }
      } else {
        // 默认设置为简体中文
        locale.use(langZh)
      }

      new Vue({
        router,
        websocket,
        store,
        vuetify,
        i18n,
        render: h => h(App),
      }).$mount('#app')
    }).catch(error => {
    console.log(error.response == null ? error.response : error.response.data.error)
  })
}

startApp()
