# 判断是否开发环境，开发环境连接远程服务器，生产环境连接本地服务器
NODE_ENV=development
VUE_APP_IS_DEV=true

# 是否在开发环境中打印request和response日志，日志较多时关闭
VUE_APP_SHOW_WS_REQUEST=false
VUE_APP_SHOW_WS_RESPONSE=false

# 开发环境配置
#IPV4_AMAZON_ADDR = '***********'             //  云广播
#IPV4_TENCENT_ADDR = '**************'          //  通过腾讯云服务器中转数据，适用于开发者所处网络环境不支持IPV6的情况（办公室主机）
#IPV6_ADDR = 'mhitech.dynv6.net'       // 直连服务器IPV6地址，如果开发者所处网络环境支持IPV6，首选此地址（办公室主机）

# 开发环境websocket连接地址
# 本地Windows端
#VUE_APP_BASE_URL=*************
# 远程Linux端
VUE_APP_BASE_URL=************
