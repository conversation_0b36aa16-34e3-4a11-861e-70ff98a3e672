{"toggleSidebar": "Toggle Sidebar", "avatar": "IP Broadcasting System", "buttons": "Buttons", "calendar": "Calendar", "charts": "Charts", "components": "Components", "ct": "CT", "dtables": "Data Tables", "eforms": "Extended Forms", "error": "Error <PERSON>s", "etables": "Extended Tables", "example": "Example", "forms": "Forms", "fullscreen": "Fullscreen Map", "google": "Google Maps", "grid": "Grid System", "icons": "Icons", "lock": "Lock Screen", "maps": "Maps", "multi": "Multi-level Collapse", "notifications": "Notifications", "pages": "Pages", "plan": "Choose <PERSON>", "pricing": "Pricing", "my-profile": "My Profile", "edit-profile": "Edit Profile", "register": "Register Page", "rforms": "Regular Forms", "rtables": "Regular Tables", "rtl": "RTL Support", "search": "Search", "tables": "Tables", "tabs": "Tabs", "tim": "Creative Tim", "timeline": "Timeline", "typography": "Typography", "upgrade": "2020-2022 All Rights Reserved", "user": "User Profile", "vforms": "Validation Forms", "widgets": "Widgets", "wizard": "<PERSON>", "customer": {"title": {"ipBroadcastSystem": "IP Broadcasting System", "cloudBroadcastSystem": "Cloud Broadcast System", "villageVillageBroadcastSystem": "Village Broadcast System", "aiSmartLinkSystem": "AI Broadcasting System", "iotDigitalThingsLinkSystem": "IoT Broadcasting System", "iotCloudPlatform": "IoT Broadcast System", "cloudInterconnectControlSystem": "Cloud Control System", "smartCloudBroadcastSystem": "Smart Broadcast System"}}, "login": {"title": "<PERSON><PERSON>", "username": "Username", "password": "Password", "usernamePlaceholder": "Please enter username", "passwordPlaceholder": "*********", "loginButton": "<PERSON><PERSON>", "usernameRequired": "Tip: Username cannot be empty", "passwordRequired": "Tip: Password cannot be empty", "loginError": "Incorrect username or password", "loginSuccess": "Login successful", "serverConnectionFailed": "Server connection failed, please try again later", "currentUser": "Current logged in user"}, "appbar": {"collapseNavbar": "Collapse Navbar", "showNavbar": "Show Navbar", "controlCenter": "Control Center", "cloudControl": "WeChat Cloud Control", "wechatScan": "Use WeChat scan to bind", "enableCloudControl": "Enable Cloud Control", "disableCloudControl": "Disable Cloud Control", "accountManagement": "Account Management", "logout": "Logout", "currentUser": "Current logged in user"}, "language": {"zh": "简体中文", "en": "English", "zh-TW": "繁體中文", "selectLanguage": "Select Language"}, "timeTips": {"hours": "hours ", "minutes": "minutes ", "seconds": "seconds ", "days": "days "}, "tts": {"title": "Text to Speech", "inputText": "Please enter text", "fileName": "File Name", "audioFileName": "Audio File Name", "audioFileNamePlaceholder": "Please enter the completed audio file name", "textTooLong": "Text length cannot exceed {maxBytes} bytes"}, "routes": {"controlCenter": "Control Center", "deviceManagement": "Device Management", "mediaManagement": "Media Management", "timerManagement": "Timer Management", "accountManagement": "Account Management", "monitorManagement": "Monitor Management", "logManagement": "Log Management", "systemSettings": "System Settings", "systemMaintenance": "System Maintenance", "informationPublish": "Information Publishing", "logout": "Logout", "error": "<PERSON><PERSON><PERSON>urred"}, "deviceTypes": {"allDevices": "All Devices", "decodeTerminal": "Decode Terminal", "smartPager": "Smart Pager", "audioCollector": "Audio Collector", "fireCollector": "Fire Collector", "powerSequencer": "Power Sequencer", "audioCoprocessor": "Audio Coprocessor", "audioRepeater": "Audio Repeater", "remoteController": "Remote Controller", "phoneGateway": "Phone Gateway", "ampController": "Amplifier Controller", "noiseDetector": "Noise Adaptive Terminal"}, "dashboard": {"soundCardRecord": "Sound Card Recording", "soundCardOutputDevice": "Sound Card Output Device", "soundCardInputDevice": "Sound Card Input Device", "deviceVolume": "Device Volume", "transmissionMode": "Transmission Mode", "lowBandwidth": "Low Bandwidth", "highQuality": "High Quality", "startRecord": "Start Recording", "stopRecord": "Stop Recording", "speaker": "Speaker", "maleVoice": "Male Voice", "femaleVoice": "Female Voice", "volume": "Volume", "pitch": "Pitch", "speed": "Speed", "importTxtFile": "Import TXT File", "browseTxtFile": "Browse Local File (UTF-8 encoding)", "load": "Load", "defaultParams": "Default Parameters", "startSynthesis": "Start Synthesis", "systemRegister": "System Registration", "systemNotRegistered": "Tip: Current system is not registered, please register to unlock all features", "registrationCode": "Registration Code", "enterRegistrationCode": "Please enter registration code", "aboutToDelete": "About to delete", "groups": "groups", "pleaseConfirm": "Please confirm", "createGroup": "Create Group", "enterGroupName": "Please enter new group name", "zoneList": "Zone List", "selectedZones": "Selected Zones", "enterZoneKeyword": "Please enter zone keyword", "groupName": "Group Name", "stopTask": "Stop Task", "aboutToStop": "About to stop", "tasks": "tasks", "oneKeyAlarm": "One-Key Alarm", "confirmStartStopAlarm": "Please confirm whether to start/stop one-key alarm function?", "setPlayModeSuccess": "Set play mode successfully", "applyToAllManualTasks": "Apply to all manual tasks? Otherwise it will take effect in new manual tasks", "restoreTodayTimer": "Restore Today's Timer Tasks", "aboutToRestore": "About to restore today's timer tasks", "cancelTodayTimer": "Cancel Today's Timer Tasks", "aboutToCancel": "About to cancel today's timer tasks", "stopTimerTask": "Stop Timer Task", "aboutToStopTimer": "About to stop timer task", "playSource": "Play Program Source", "audioMixerConfirm": "Your selected zones include audio coprocessor, are you sure to continue?", "audioSourceList": "Audio Source List", "onlineZones": "Online Zones", "showOnlineZones": "Show Online Zones", "showAllZones": "Show All Zones", "selectAll": "Select All", "deselectAll": "Deselect All", "selected": "Selected", "currentConditionSelected": "Current condition selected", "totalSelected": "Total selected", "search": "Search", "itemsPerPage": "per page", "newGroup": "New Group", "editGroup": "Edit Group", "deleteGroup": "Delete Group", "zoneCount": "Zone Count", "user": "User", "containsZones": "Contains Zones", "groupHasNoZones": "This group has no zones added", "listView": "List View", "noGroupSelected": "No group selected", "selectedGroupHasNoZones": "Selected group has no zone information added", "audioCollection": "Audio Collection", "powerSequencer": "Power Sequencer", "restoreTodayTimerTask": "Restore Today's Timer Tasks", "playMode": "Set Play Mode", "previousTrack": "Previous Track", "play": "Play", "pause": "Pause", "nextTrack": "Next Track", "stop": "Stop", "oneKeyAlarmBtn": "One-Key Alarm", "monitorControl": "Monitor Control", "openListening": "Open Listening Control", "closeListening": "Close Listening Control", "soundCardRecordBtn": "Sound Card Recording", "closeSoundCardRecord": "Close Sound Card Recording", "textToSpeech": "Text to Speech", "playSourceBtn": "Play Program Source", "setIdleStatus": "Set Zone to Idle Status", "allZones": "All Zones", "allGroups": "All Groups", "messages": {"playSourceSuccess": "Play program source successfully", "playCollectSuccess": "Play audio collector source successfully", "ttsSuccess": "Text to speech synthesis successful", "registerSuccess": "Registration successful, redirecting to login page", "setPlayModeSuccess": "Set manual task play mode successfully", "taskStopped": "Task stopped", "setPlayStatusSuccess": "Set manual task play status successfully", "controlPrevNextSuccess": "Control manual task play {word} successfully", "alarmTaskStatus": "Alarm task {status}", "cancelTimerSuccess": "Cancel specified today's timer task successfully, will auto-restore tomorrow", "timerTaskStopped": "Timer task stopped", "restoreTimerSuccess": "<PERSON><PERSON> specified today's timer task successfully", "timerTimePassed": "Timer point time has passed, cannot restore", "groupNameEmpty": "Group name cannot be empty", "selectAtLeastOneZone": "Please select at least one zone", "selectOneGroupToEdit": "Please select one group to edit", "canOnlyEditOneGroup": "Can only edit one group at a time, please reselect", "groupNameAlreadyUsed": "This group name is already used, please use another name", "selectAtLeastOneGroup": "Please select at least one group", "selectZoneOrGroup": "Please select zone or group", "selectSourceFromList": "Please select program source from audio source list", "itemsPerPageCannotBeZero": "Items per page cannot be 0 or negative, please re-enter", "selectZoneOrGroupForVolume": "Please select zone or group for volume adjustment", "noTxtFileSelected": "No txt file selected, cannot load", "loadTxtFileSuccess": "Load txt file successfully", "textContentEmpty": "Text content cannot be empty, please re-enter or import txt text", "audioFileNameEmpty": "Audio file name cannot be empty, please re-enter", "registrationCodeEmpty": "Registration code cannot be empty, please re-enter", "noMonitorSpeakerSet": "No monitor speaker set currently, please retry", "monitorSpeakerOffline": "Monitor speaker offline, please retry", "selectOneZoneForListening": "Please select one zone for listening", "canOnlySelectOneZoneForListening": "Can only select one zone for listening, please reselect", "selectOneZoneForMonitor": "Please select one zone to open video monitoring", "canOnlySelectOneZoneForMonitor": "Can only select one zone to open video monitoring, please reselect", "runSoundCardClient": "Please run sound card recording client", "setVolumeNotSuccessful": "Setting not successful, please retry", "startRecordingFailed": "Start recording failed", "startRecording": "Start recording", "todayTimerCount": "Today's {status} timer point count is 0", "manualTaskCountZero": "Manual task count is 0", "createGroupSuccess": "Create group successfully", "editGroupSuccess": "Edit group successfully", "deleteGroupSuccess": "Delete group successfully", "adjustVolumeSuccess": "Adjust volume successfully", "stopRecording": "Stop recording", "stopPlaySuccess": "Stop play successfully"}, "status": {"executed": "Executed", "executing": "Executing", "executeAfter": "Execute after", "playing": "Playing", "paused": "Paused", "stopped": "Stopped", "started": "Started", "notExecuted": "Not executed", "cancelled": "Cancelled"}, "tabs": {"zones": "Zones", "groups": "Groups", "listView": "List View", "timerTasks": "Timer Tasks", "manualTasks": "Manual Tasks"}, "tooltips": {"resetVolumeSpeedPitch": "Reset volume, speed and pitch to 50", "openVideoMonitor": "Open video monitoring for this zone", "openListeningFor": "Open program source listening ({name})", "stopListeningFor": "Stop listening speaker ({name})", "songDuration": "Duration: {duration}", "listInfo": "Count: {count}", "userListInfo": "User: {user}<br/>Count: {count}", "executeAfterTime": "Execute after {time}", "audioCollection": "Audio Collection:", "powerSequencer": "Power Sequencer:", "cancelTodayTimer": "Cancel today's timer task", "stopTimer": "Stop timer task"}, "labels": {"itemsPerPage": "Items per page", "listView": "List view", "zoneCamera": "Zone camera display", "zoneVolume": "Zone volume display", "audioCollector": "Audio collector related", "filename": "Add animation only when filename is too long", "bindingCamera": "Format binding camera format", "noDataTip": "Format no data tip", "addSearchFunction": "Add search function", "pagination": "Pagination plugin", "zoneControl": "Zone master control", "groupControl": "Group master control", "listControl": "List master control", "timerControl": "Timer task control", "manualControl": "Manual task control", "playControl": "Play control bar", "comments": "Dialog related", "soundCardDialog": "Sound card recording dialog", "ttsDialog": "TTS dialog", "systemRegDialog": "System registration dialog", "groupDialog": "Group dialog", "stopTaskDialog": "Stop task", "alarmDialog": "Start/stop one-key alarm", "playModeDialog": "Apply play mode to all manual tasks", "restoreTimerDialog": "Restore today's timer task dialog", "cancelTimerDialog": "Cancel today's timer task dialog", "stopTimerDialog": "Stop timer task dialog", "playSourceDialog": "Play program source dialog", "errorSnackBar": "Common errorSnackBar", "successSnackBar": "Common successSnackBar", "audioSourceList": "Audio source list", "mainZoneControl": "Main zone control", "tabSwitch": "Tab switch", "hideProgress": "Hide loading progress bar", "tableView": "Table view", "playModeSection": "Play mode", "previousTrack": "Previous track", "playPause": "Play/Pause", "nextTrack": "Next track", "stopBtn": "Stop", "controlButtons": "Control buttons", "alarmBtn": "One-key alarm", "monitorBtn": "Monitor control", "listeningBtn": "Open listening control", "closeListeningBtn": "Close listening control", "soundCardBtn": "Sound card recording", "closeSoundCardBtn": "Close sound card recording", "ttsBtn": "TTS voice", "playSourceBtn": "Play program source", "idleBtn": "Set zone to idle status"}}, "playMode": {"single": "Single Play", "singleLoop": "Single Loop", "sequential": "Sequential Play", "loop": "Loop Play", "random": "Random Play"}, "table": {"number": "Number", "user": "User", "taskName": "Task Name", "zones": "Zones", "playingSong": "Playing Song", "playMode": "Play Mode", "status": "Status", "name": "Name", "startTime": "Start Time", "endTime": "End Time", "volume": "Volume", "selectedZones": "Selected Zones", "selectedGroups": "Selected Groups", "audioSource": "Audio Source", "operation": "Operation", "deviceIP": "Device IP", "bindingCamera": "Binding Camera", "runningStatus": "Running Status"}, "validation": {"maxCharacters": "Maximum {max} characters", "textRequired": "Text cannot be empty", "ttsContentMaxLength": "Text content cannot exceed 5000 characters", "fileNameMaxLength": "File name cannot exceed 20 characters", "invalidLength": "Invalid character length, requires {len} characters", "passwordRequirements": "Password must contain uppercase letters, numeric characters and special characters", "invalidInput": "Invalid input, please re-enter"}, "status": {"online": "Online", "offline": "Offline", "unregistered": "Unregistered"}, "information": {"title": "Please operate in the information publishing client!", "openClient": "Click to open client"}, "notFound": {"title": "404 Not Found", "message": "Sorry, the page you are looking for is not found", "backToControl": "Back to Control Center"}, "store": {"operation": "Operation", "operationFailed": "Operation failed", "success": " successful", "serverDisconnected": "Server disconnected", "conflictTimingPoints": "Conflict timing points:\n", "music": "Music", "enableTimingPoint": "Enable timing point", "disableTimingPoint": "Disable timing point", "addUser": "Add user", "removeUser": "Remove user", "editUser": "Edit user", "setMonitorSpeaker": "Set monitor speaker", "resetMonitorFunction": "No speaker selected, reset monitor function", "setSourceMonitor": "Set source monitor", "stopMonitorSpeaker": "Stop monitor speaker", "setFireChannel": "Set fire channel", "textToSpeech": "Text to speech", "errors": {"unknownMistake": "Current operation failed, please try again", "normalOperation": "Operation normal", "inadequateUserRights": "Insufficient user rights", "userAlreadyExists": "User already exists", "userDoesNotExist": "User does not exist", "userPasswordError": "User password error", "userNotLoggedIn": "User not logged in", "userIsLoggedIn": "User is logged in", "targetDoesNotExist": "Target does not exist", "targetDoesExist": "Target already exists", "numberOfTargetsDoesNotMatch": "Number of targets does not match", "targetFormatDoesNotMatch": "Target format does not match", "targetParameterDoesNotMatch": "Target parameter does not match", "busyTarget": "Target busy", "goalOffline": "Target offline", "partitionIdDoesNotMatch": "Selected partition ID does not match", "quantitativeLimit": "Quantitative limit reached", "sipBusy": "Current SIP device is busy", "sipNumberOffline": "SIP number offline", "sipUnknownError": "SIP operation failed, please try again", "sipAccountDoesNotExist": "SIP account does not exist", "sipDialingRulesDoNotExist": "SIP dialing rules do not exist", "sipDialingRulesAreNotUniform": "SIP dialing rules are not uniform", "timingSchemeIdError": "Timing scheme ID error", "timingPointIdError": "Timing point ID error", "timingSchemeNameIncorrect": "Timing scheme name incorrect (empty or exceeds maximum length)", "timingPointNameIncorrect": "Timing point name incorrect (empty or exceeds maximum length)", "timingPointNameError": "Date time error", "specifiedDateEarlierThanToday": "Specified date is earlier than today", "startDateLaterThanEndDate": "Start date is later than end date", "startDateTodayStartTimeEarlier": "Start date is today and start time is earlier than now", "startTimeLaterThanEndTime": "Start time is later than end time", "noEquipmentSelected": "No equipment selected", "noSongSelected": "No song selected", "timingPointExceedsLimit": "Timing point exceeds quantity limit", "timingSchemeExceedsLimit": "Timing scheme exceeds quantity limit", "waitingToReceivePartitionData": "Waiting to receive partition group data", "upgradeFileDoesNotExist": "Upgrade file does not exist", "syntheticParameterError": "Synthesis parameter error", "speakerDoesNotExist": "Speaker does not exist", "syntheticTextTooLong": "Synthesis text too long", "synthesisFailure": "Synthesis failure", "audioTooShort": "Audio too short", "workingModeDoesNotMatch": "Working mode does not match", "currentSourceCannotBeMonitored": "Current source cannot be monitored", "deviceDoesNotExist": "Device does not exist", "deviceOffline": "Device offline", "logFileDownload": "Log file downloading", "logFileDoesNotExist": "Log file does not exist", "registerFail": "Server registration failed", "operationTimeout": "Operation timeout", "networkNotMatch": "Terminal devices and server are not in the same subnet. If operation fails, please check network configuration", "timingPointConflict": "Timing point conflict detected, please resolve before enabling!", "manualAlarmSongNotExist": "Alarm sound file sysAlarm.mp3 not found, please upload", "existSubUserCanNotDeleted": "This account has sub accounts, cannot delete", "exceededStorageCapacity": "Storage capacity exceeded", "deviceNotSupportFunction": "Device does not support this function"}}, "deviceStatus": {"offline": "Offline", "idle": "Idle", "localPlay": "Local Play", "networkOnDemand": "Net Play", "scheduled": "Scheduled", "audioMixing": "Audio Mixing", "intercom": "Intercom", "monitorEventTrigger": "Monitor Event Trigger", "monitoring": "Monitoring", "fireAlarm": "Fire Alarm", "networkPaging": "Paging", "hundredVolt": "100V", "sipCall": "SIP", "api": "API", "netRadio": "Radio", "phoneGateway": "Phone Gateway", "audioCollection": "Audio Collection"}, "timer": {"deleteSchema": "Delete Timer <PERSON>", "aboutToDeleteSchema": "About to delete timer schema", "pleaseConfirm": "Please confirm", "setActiveSchema": "Set Current Timer <PERSON>", "onlyOneSchemaActive": "Tip: Only one timer schema can be active at the same time", "aboutToSetSchemaActive": "About to set this timer schema active", "copySchema": "<PERSON><PERSON>", "aboutToCopySchema": "About to copy timer schema", "deleteTimer": "Delete Timer Point", "aboutToDeleteTimer": "About to delete timer point", "copyTimer": "Copy Timer Point", "aboutToCopyTimer": "About to copy timer point", "sortTimer": "Sort Timer Points", "aboutToSortTimer": "About to sort all timer points by start time in ascending order", "createTimer": "Create Timer Point", "editTimer": "Edit Timer Point", "timerName": "Timer Point Name", "timerNamePlaceholder": "Please enter timer point name", "effective": "Effective", "volumeFollowDevice": "Volume follows device", "dateSelection": "1. Date Selection", "weeklyLoop": "Weekly Loop", "specifyDate": "Specify Date", "startDate": "Start Date", "endDate": "End Date", "timeSelection": "2. Time Selection", "startTime": "Start Time", "endTime": "End Time", "manualInput": "Manual Input:", "fixEndTime": "Fix End Time", "deviceSelection": "3. <PERSON><PERSON>", "decoder": "Decoder Terminal", "powerSequencer": "Power Sequencer", "selectedZoneCount": "Selected Zone Count:", "zoneSelection": "Zone Selection", "selectedGroupCount": "Selected Group Count:", "groupSelection": "Group Selection", "selectedDeviceCount": "Selected Device Count:", "selectedChannelCount": "Selected Channel Count:", "powerSequencerSelection": "Power Sequencer Selection", "sourceSelection": "4. Audio Source Selection", "songPlay": "Song Play", "audioCollection": "Audio Collection", "noAudioCollector": "Tip: No audio collector found, please check device.", "noPlaylist": "Tip: Playlist is empty, cannot select songs, please add playlist in control center", "playlist": "Playlist", "playMode": "Play Mode", "listSongs": "List Songs", "addSingle": "Add Single", "addAll": "Add All", "deleteSingle": "Delete Single", "createSchema": "Create <PERSON><PERSON>", "editSchema": "<PERSON> <PERSON><PERSON>", "schemaNamePlaceholder": "Please enter timer schema name", "powerSequencerSelectionTitle": "Power Sequencer Selection", "reset": "Reset", "zoneSelectionTitle": "Zone Selection", "zoneList": "Zone List", "selectedZones": "Selected Zones", "zoneKeywordPlaceholder": "Please enter zone keyword", "close": "Close", "groupSelectionTitle": "Group Selection", "groupList": "Group List", "selectedGroups": "Selected Groups", "groupKeywordPlaceholder": "Please enter group keyword", "timerScheme": "<PERSON><PERSON>", "createTimerScheme": "Create <PERSON><PERSON>", "editTimerScheme": "<PERSON> <PERSON><PERSON>", "deleteTimerScheme": "Delete Timer <PERSON>", "copyTimerScheme": "<PERSON><PERSON>", "setCurrentSchemeActive": "Set Current Schema Active", "search": "Search", "timerPointSettings": "Timer Point Settings", "createTimerPoint": "Create Timer Point", "editTimerPoint": "Edit Timer Point", "deleteTimerPoint": "Delete Timer Point", "enableTimerPoint": "Enable Timer Point", "disableTimerPoint": "Disable Timer Point", "copyTimerPoint": "Copy Timer Point", "sortTimerPoints": "Sort Timer Points", "edit": "Edit", "delete": "Delete", "noTimerSchemeSet": "No timer schema set currently", "headers": {"id": "ID", "user": "User", "name": "Name", "timerMode": "Timer Mode", "playCycle": "Play Cycle", "startTime": "Start Time", "endTime": "End Time", "playMode": "Play Mode", "volume": "Volume", "selectedZones": "Selected Zones", "selectedGroups": "Selected Groups", "playSource": "Play Source", "status": "Status", "actions": "Actions", "selectedPartitions": "Selected Partitions", "audioSource": "Audio Source"}, "playModes": {"sequential": "Sequential Play", "loop": "Loop Play", "random": "Random Play"}, "validation": {"schemaNameRequired": "Tip: Timer schema name cannot be empty", "schemaNameLength": "Tip: Input exceeds character limit"}, "messages": {"createSchemaSuccess": "Create timer schema successfully", "editSchemaSuccess": "Edit timer schema successfully", "deleteSchemaSuccess": "Delete timer schema successfully", "setActiveSchemaSuccess": "Set current timer schema successfully", "createTimerSuccess": "Create timer point successfully", "editTimerSuccess": "Edit timer point successfully", "deleteTimerSuccess": "Delete timer point successfully", "copySchemaSuccess": "Copy timer schema successfully", "copyTimerSuccess": "Copy timer point successfully", "sortTimerSuccess": "Sort timer points successfully", "noAudioCollectorSelected": "No audio collector selected"}, "table": {"id": "ID", "user": "User", "name": "Name", "timerMode": "Timer Mode", "playCycle": "Play Cycle", "startTime": "Start Time", "endTime": "End Time", "playMode": "Play Mode"}, "audioCollector": {"selected": "Selected audio collector:"}, "selectedSongs": "Selected Songs", "estimatedDuration": "Estimated Play Duration", "pleaseSelectSchema": "Please select timer schema", "noTimerInCurrentSchema": "No timer point configured in current timer schema", "noActiveSchema": "No active timer schema exists currently", "noTimerForCurrentAccount": "No timer point configured for current account", "errors": {"selectSchemaToEdit": "Please select a timer schema to edit", "selectSchemaToDelete": "Please select a timer schema to delete", "selectSchemaToSet": "Please select a timer schema to set", "selectSchemaToCopy": "Please select a timer schema to copy", "selectTimerToSet": "Please select a timer point to set", "selectSchemaForTimer": "Please select a timer schema for timer point operation", "selectTimerToDelete": "Please select a timer point for deletion", "selectTimerToEdit": "Please select a timer point for editing", "timerNameEmpty": "Timer point name is empty or invalid, please re-enter", "selectDateScheme": "Please select timer point date scheme", "startDateTooEarly": "Timer point start date must be later than current date, please reset", "noStartTimeForFix": "Timer start time not set, cannot fix end time", "noSongsForFix": "No timer songs selected, cannot fix end time", "songsCannotFinishToday": "Timer songs cannot finish playing today, cannot fix end time", "weekdayNotSelected": "Timer weekday not selected", "endDateBeforeStartDate": "Timer end date must be later than start date, please reset", "startOrEndTimeNotSet": "Timer start time or end time not set, please reset", "endTimeBeforeStartTime": "Timer end time must be later than start time, please reset", "needZoneOrGroup": "Timer needs at least one zone or group, please reset", "needPlayMode": "Timer needs to set play mode, please reset", "noSongsSelected": "No timer songs selected, please reselect", "noAudioCollectorSelected": "No audio collector selected, please reselect", "noPowerSequencerSelected": "No power sequencer selected, please reselect"}, "comments": {"commonSuccessSnackBar": "Common success snackbar", "commonErrorSnackBar": "Common error snackbar", "dialogs": "Dialogs", "copyTimerPoint": "Copy timer point", "timerPointSorting": "Timer point sorting", "createEditTimerDialog": "Create/edit timer point common dialog", "addDividerLine": "Add divider grid line", "audioSourceSelection": "Audio source selection", "setLabelSize": "Set label size", "songSelection": "Song selection"}, "steps": {"dateSelection": "1. Date Selection", "timeSelection": "2. Time Selection", "deviceSelection": "3. Zone Selection"}, "labels": {"duration": "Duration"}, "modes": {"weeklyLoop": "Weekly Loop", "dateLoop": "Date Loop"}, "status": {"active": "Active", "inactive": "Inactive"}, "weekdays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}, "system": {"registration": {"title": "System Registration", "status": "Registration Status", "machineCode": "Machine Code", "code": "Registration Code", "codePlaceholder": "Please enter registration code", "register": "Register", "unregistered": "Unregistered", "registered": "Registered", "registeredWithExpiry": "Registered (Expiry: {date})", "dongleDetected": "<PERSON><PERSON> detected", "dongleExpired": "Dongle authorization expired", "codeExpired": "Registration code authorization expired", "success": "Registration successful, redirecting to login page", "failed": "Registration failed, please check registration code!", "codeRequired": "Registration code cannot be empty, please re-enter"}, "info": {"title": "System Information", "version": "System Version", "webAddress": "Web Address", "authorization": "System Authorization", "currentTime": "Current Time", "startTime": "Start Time", "runTime": "Run Time", "examMode": "Exam <PERSON>", "enable": "Enable", "examModeTooltip": "When exam mode is enabled, only administrators can log in"}, "network": {"title": "Network Information", "settings": "Network Settings", "macAddress": "MAC Address", "ipAddress": "IP Address", "subnetMask": "Subnet Mask", "gateway": "Gateway", "dnsServer": "DNS Server", "configInstruction": "Please enter the corresponding network configuration below and click the button to set system network", "set": "Set", "configSuccess": "System network configuration successful, redirecting to login page", "fieldsRequired": "IP address/subnet mask/gateway/DNS server cannot be empty, please re-enter", "invalidIp": "IP address format error, please re-enter"}, "storage": {"title": "Storage Information", "totalSpace": "Total Storage Space", "availableSpace": "Available Storage Space", "totalMemory": "Total Memory", "availableMemory": "Available Memory"}, "time": {"title": "System Date and Time", "instruction": "Please select date and time below, then click the set button to confirm changes", "set": "Set", "success": "Set system date and time successfully"}}, "account": {"removeUser": "Remove User", "aboutToRemoveUser": "About to remove user", "pleaseConfirm": "Please confirm", "addUser": "Add User", "editUser": "Edit User", "accountField": "Account", "accountPlaceholder": "Please enter account (letters, numbers and underscores starting with letter)", "nameField": "Name", "namePlaceholder": "Please enter account alias (Chinese supported, can be left empty if same as account)", "usernameField": "Username", "usernamePlaceholder": "Please enter username", "passwordField": "Password", "passwordPlaceholder": "Please enter account password", "selectParent": "Select Parent", "allowRepeatedLogin": "Allow Repeated <PERSON>gin", "allowRepeatedLoginTooltip": "When enabled, allows this account to log in repeatedly", "globalPlaylistPermission": "Global Playlist Management Permission", "globalPlaylistPermissionTooltip": "When enabled, can manage all users' playlists", "globalTimerPermission": "Global Timer Management Permission", "globalTimerPermissionTooltip": "When enabled, can manage all users' timer points", "audioCollectorPermission": "Audio Collector Usage Permission", "audioCollectorPermissionTooltip": "When enabled, can use audio collector", "deviceKeywordPlaceholder": "Please enter zone keyword", "deviceList": "Device List", "selectedDevices": "Selected Devices", "deviceType": "Device Type", "save": "Save", "advancedSettings": "Advanced Settings", "userPermissions": "User Permissions", "allowCreateSubAccount": "Allow Create Sub Account", "allowCreateSubAccountTooltip": "When enabled, allows this account to create sub accounts", "setUserPermissions": "Set User Permissions", "storageSpace": "Storage Space", "totalStorageSpace": "Total Storage Space", "usedSpace": "Used Space", "remainingSpace": "Remaining Space", "setStorageSpace": "Set Storage Space (MB)", "storageSpaceHint": "Size range: 50MB~1TB (1048576MB)", "setStorageSpaceButton": "Set Storage Space", "accountManagement": "Account Management", "search": "Search", "loadingText": "Querying account information, please wait", "notBoundZones": "Not Bound Zones", "allZones": "All Zones", "serialNumber": "Serial Number", "account": "Account", "name": "Name", "boundDeviceCount": "Bound Device Count", "operation": "Operation", "edit": "Edit", "advanced": "Advanced", "delete": "Delete", "headers": {"serialNumber": "Serial Number", "account": "Account", "name": "Name", "password": "Password", "accountType": "Account Type", "boundDeviceCount": "Bound Device Count", "operation": "Operation"}, "accountTypes": {"admin": "Administrator", "level2": "Level 2 User", "level3": "Level 3 User", "level4": "Level 4 User"}, "messages": {"selectUserToEdit": "Please select a user to edit", "selectUserToDelete": "Please select a user to delete", "accountLengthError": "Account length error, please enter 3~16 characters", "passwordLengthError": "Password length error, please enter 3~16 characters", "cannotDeleteWithSubUsers": "This account has sub accounts, cannot delete!", "setStorageSuccess": "Set storage space successfully", "setPermissionSuccess": "Set user permissions successfully", "enterCorrectStorageSize": "Please enter correct storage space size number", "storageRangeError": "Storage space valid size range is 50MB~1TB (1048576MB)"}, "validation": {"required": "Please enter number", "number": "Please enter number", "storageRange": "Valid range: 50MB~1TB (1048576MB)"}, "permissions": {"allowRepeatedLogin": "Allow Repeated <PERSON>gin", "allowRepeatedLoginTooltip": "When enabled, allows this account to log in repeatedly", "globalPlaylistManagement": "Global Playlist Management Permission", "globalPlaylistManagementTooltip": "When enabled, can manage all users' playlists", "globalTimerManagement": "Global Timer Management Permission", "globalTimerManagementTooltip": "When enabled, can manage all users' timer points", "audioCollectorUsage": "Audio Collector Usage Permission", "audioCollectorUsageTooltip": "When enabled, can use audio collector"}, "table": {"serialNumber": "Serial Number", "account": "Account", "name": "Name", "boundDeviceCount": "Bound Device Count", "actions": "Actions"}, "actions": {"edit": "Edit", "advanced": "Advanced", "delete": "Delete"}, "errors": {"hasSubAccounts": "This account has sub accounts, cannot delete!", "selectUserToEdit": "Please select a user to edit", "selectUserToDelete": "Please select a user to delete", "accountLengthError": "Account length error, please enter 3~16 characters", "passwordLengthError": "Password length error, please enter 3~16 characters", "invalidStorageSize": "Please enter correct storage space size number", "storageSizeRange": "Storage space valid size range is 50MB~1TB (1048576MB)"}, "userTypes": {"admin": "Administrator", "secondLevel": "Level 2 User", "thirdLevel": "Level 3 User", "fourthLevel": "Level 4 User"}}, "log": {"title": "Log Management", "tabs": {"systemLog": "System Log", "callRecord": "Broadcast Record"}, "deviceSelection": "Device Selection", "deviceSelectionHint1": "Tip: Specify", "deviceSelectionHint2": "single", "deviceSelectionHint3": "device for log query", "deviceKeywordPlaceholder": "Please enter device keyword", "deviceList": "Device List", "selectedDevices": "Selected Devices", "logType": "Log Type", "deviceType": "Device Type", "callerDevice": "<PERSON><PERSON>", "selected": "Selected", "startDate": "Start Date", "endDate": "End Date", "query": "Query", "downloadCurrentLog": "Download Current Log", "callRecord": {"autoRecordSwitch": "Auto Record Switch", "enable": "Enable", "disable": "Disable", "serialNumber": "Serial Number", "recordId": "Record ID", "callId": "Call ID", "callerMac": "Caller MAC", "callerName": "Caller Name", "calleeList": "Callee List", "recordType": "Record Type", "recordStatus": "Record Status", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration (seconds)", "filePath": "File Path", "fileSize": "File Size", "download": "Download Record", "play": "Play Record", "playError": "Play failed", "downloadError": "Download failed", "comingSoon": "Feature under development...", "deleteConfirmMessage": "Are you sure you want to delete this call record? This action cannot be undone.", "recordStatusRecording": "Recording", "recordStatusCompleted": "Completed", "recordStatusError": "Error", "recordStatusInterrupted": "Interrupted", "deviceListEllipsis": "{firstDevice} and {count} devices", "callType": "Call Type", "callTypeBroadcast": "Broadcast Paging", "callTypeIntercom": "Intercom Call"}, "logTypes": {"all": "All Logs", "runtime": "Runtime Log", "playback": "Playback Log", "sync": "Sync Log", "fire": "Fire Log", "call": "Call Log", "monitor": "Monitor Log", "paging": "Paging Log", "advanced": "Advanced Operation Log"}, "deviceTypes": {"allDevices": "All Devices", "none": "None", "decoder": "Decode Terminal", "pager": "Smart Pager", "audioCollector": "Audio Collector", "fireCollector": "Fire Collector", "powerSequencer": "Power Sequencer", "audioProcessor": "Audio Coprocessor", "audioRepeater": "Audio Repeater", "remoteController": "Remote Controller", "phoneGateway": "Phone Gateway", "ampController": "Amplifier Controller", "noiseDetector": "Noise Adaptive Terminal"}, "headers": {"serialNumber": "Serial Number", "date": "Date", "time": "Time", "deviceNameOrUser": "Device Name/User", "logType": "Log Type", "logContent": "Log Content"}, "messages": {"queryLogSuccess": "Query log successfully", "querySuccess": "Query log successfully", "pleaseSelectLogDate": "Please select query log date", "accountOnlySupportsSpecificDevice": "Current account only supports specific device query, please reselect device", "pleaseSelectDeviceOrSwitchMode": "Current mode is single device log query, please select device or switch query mode", "pleaseSelectOneDevice": "Please select one device for log query, please retry", "canOnlySelectSingleDevice": "Can only select single device for log query, please retry"}, "tableHeaders": {"serialNumber": "Serial Number", "date": "Date", "time": "Time", "deviceNameOrUser": "Device Name/User", "logType": "Log Type", "logContent": "Log Content"}}, "settings": {"themeSettings": "Theme Settings", "darkMode": "Dark Mode", "sidebarBackground": "Sidebar Background", "backgroundImage": "Background Image", "guideSettings": "Guide Settings", "documentation": "Documentation"}, "maintenance": {"aboutToPerform": "About to perform", "pleaseConfirm": "Please confirm operation", "uploadConfigFile": "Upload Config File", "selectConfigFile": "Select Config File", "dataReset": "Data Reset", "selectDataToReset": "Please select data items to reset and confirm", "factoryReset": "Factory Reset", "factoryResetWarning": "Warning: After factory reset, all your configurations will be lost!!", "factoryResetCaution": "Factory reset is not necessary unless required, please operate with caution!", "serverRestart": "<PERSON>", "serverRestartWarning": "Warning: Server restart will interrupt all ongoing audio playback!!", "serverRestartNotice": "After server restart, you will return to login page, please wait a moment before logging in again!", "backupRestore": "Backup/Restore", "serverConfigFileCount": "Current server config file count:", "delete": "Delete", "masterBackupServer": "Master-Backup Server", "serverType": "Server Type", "masterServer": "Master Server", "backupServer": "Backup Server", "serverStatus": "Server Status", "functionSwitch": "Function Switch", "disableMasterBackup": "Disable Master-Backup", "enableMasterBackup": "Enable Master-Backup", "masterServerIP": "Master Server IP", "backupServerIP": "Backup Server IP", "setBackupServer": "Set Backup Server", "systemUpgrade": "System Upgrade", "currentServerVersion": "Current Server Version:", "upgradeWarning": "Warning: Server upgrade will interrupt all ongoing audio playback, system will restart automatically after successful upgrade!!", "selectUpgradePackage": "Select Upgrade Package", "serverUpgrade": "Server Upgrade", "dataTypes": {"groupFiles": "Group Files", "playlistFiles": "Playlist Files", "timerFiles": "Timer Files", "zoneFiles": "Zone Files", "pagerFiles": "Pager Files", "audioCollectionFiles": "Audio Collection Files", "fireCollectionFiles": "Fire Collection Files", "monitorDevices": "Monitor Devices", "upgradeFirmware": "Upgrade Firmware", "powerSequencerFiles": "Power Sequencer Files", "logFiles": "Log Files"}, "tabs": {"dataReset": "Data Reset", "backupRestore": "Backup/Restore", "masterBackupServer": "Master-Backup Server", "systemUpgrade": "System Upgrade"}, "operations": {"serverUpgrade": "Server Upgrade", "resetSpecificData": "Reset Specific Data", "factoryReset": "Factory Reset", "serverRestart": "<PERSON>", "deleteConfigFile": "Delete Config File", "restoreConfigFile": "Restore Config File", "backupServerSettings": "Backup Server Settings"}, "messages": {"resetDataSuccess": "Reset specific data successfully", "factoryResetSuccess": "Factory reset successful, redirecting to login page", "uploadUpgradePackageSuccess": "Upload server upgrade package successfully, requesting server upgrade", "uploadUpgradePackageFailed": "Upload server upgrade package failed", "backupSuccess": "Server backup data successfully", "removeBackupFileSuccess": "Remove backup file successfully", "uploadConfigFileSuccess": "Upload config file successfully", "uploadConfigFileFailed": "Upload config file failed", "restoreDataSuccess": "Server restore data successfully", "setBackupServerSuccess": "Set master-backup server successfully", "pleaseSelectConfigFile": "Please select config file to upload", "pleaseSelectUpgradePackage": "Please select upgrade package to upload", "invalidBackupFileFormat": "File name format incorrect, please upload tar.gz format backup package with filename containing {identifier}", "invalidUpgradeFileFormat": "File name format incorrect, please upload tar.gz format firmware package with filename starting with {prefix}"}, "serverSyncStatus": {"masterConnectedToBackup": "Connected to backup server", "masterConnectingToBackup": "Connecting to backup server", "masterSyncDisabled": "Master-backup server function not enabled", "masterNoBackupSpecified": "No backup server specified", "masterBackupOffline": "Backup server offline", "masterConnectFailedWaitAuth": "Connect to backup server failed (waiting for authorization)", "masterConnectFailedAuth": "Connect to backup server failed (authorization failed)", "masterConnectFailedVersion": "Connect to backup server failed (program mismatch)", "masterConnectFailedNetwork": "Connect to backup server failed (network error)", "backupConnectedToMaster": "Connected to master server", "backupNotConnectedToMaster": "Not connected to master server", "backupRefusedConnection": "Master server connection refused", "backupChangedToMaster": "Switched to master server mode"}, "backup": {"currentConfigCount": "Current server config file count"}, "actions": {"backup": "Backup", "upload": "Upload", "export": "Export", "restore": "Rest<PERSON>"}, "noConfigFilesInServer": "No configuration files in server"}, "media": {"createNewList": "Create New List", "enterNewListName": "Please enter new list name", "renameList": "Rename List", "deleteList": "Delete List", "aboutToDeleteList": "About to delete list", "addSong": "Add Song", "selectFromLibrary": "Select from Library", "uploadNewSong": "Upload New Song", "enterSongKeyword": "Please enter song keyword", "songList": "Song List", "selectedSongs": "Selected Songs", "selectSongsToUpload": "Please select songs to upload, supports mp3 and wav formats", "deleteSong": "Delete Song", "aboutToDeleteSelectedSongs": "About to delete selected", "songsConfirm": "songs, please confirm", "auditSong": "Audit Song", "aboutToAuditSelected": "About to audit selected", "downloadSong": "Download Song", "aboutToDownloadSelected": "About to download selected", "downloadSelectedSongs": "Download Selected Songs", "downloadTip": "Tip: Recommend downloading no more than 5 songs at once", "uploadSong": "Upload Song", "listManagement": "List Management", "libraryManagement": "Library Management", "search": "Search", "enableSongSorting": "Enable Song Sorting", "dragOrSelectToSort": "Drag or select zones to sort", "sortByName": "Sort by Song Name", "sortByDuration": "Sort by <PERSON> Duration", "moveUpSelected": "Move Up Selected Songs", "moveDownSelected": "Move Down Selected Songs", "cancelSortChanges": "Cancel Sort Changes", "confirmSortChanges": "Confirm Sort Changes", "noSongsInSelectedList": "Selected list has no songs added", "noListSelected": "No list selected", "normal": "Normal", "invalid": "Invalid", "audited": "Audited", "pendingAudit": "Pending <PERSON>t", "preview": "Preview", "delete": "Delete", "aboutToDeleteSong": "About to delete song", "storageInfo": "Current account total storage: {total}MB, used: {used}MB, remaining: {remaining}MB", "music": "Music", "noLocalSongFiles": "Local song files do not exist, please upload", "songTypes": {"tts": "TTS", "music": "Music"}, "messages": {"createListSuccess": "Create list successfully", "renameListSuccess": "Rename list successfully", "deleteListSuccess": "Delete list successfully", "uploadSongSuccess": "Upload song successfully", "addSongToListSuccess": "Add song to list successfully", "deleteSongFromLibrarySuccess": "Delete specified song from library successfully", "deleteSongFromListSuccess": "Delete specified song from list successfully", "auditSongSuccess": "Song audit successfully", "sortSongSuccess": "Sort specified list songs successfully"}, "errors": {"pleaseSelectSongs": "Please select songs to upload", "maxSongsLimit": "Maximum 50 songs allowed per upload", "totalSizeLimit": "Single upload song total size exceeds 500MB", "invalidFileFormat": "Please upload song files with mp3 or wav extension", "uploadFailed": "Upload failed", "invalidInput": "Invalid input, please re-enter", "listNameExists": "List name already exists, please re-enter", "cannotModifyList": "This list cannot be modified"}, "validation": {"listNameRequired": "Tip: List name cannot be empty", "listNameLength": "Tip: Input exceeds character limit", "nameRequired": "Tip: Username cannot be empty", "nameLength": "Tip: Input exceeds character limit"}, "tips": {"auditedSongsOnly": "Tip: This page only shows audited songs in library that are not yet added to this list", "existingSongsOnly": "Tip: This page only shows existing songs in library that are not yet added to this list"}, "tabs": {"selectFromLibrary": "Select from Library", "uploadNewSong": "Upload New Song", "listManagement": "List Management", "songManagement": "Library Management", "radioManagement": "Radio Management"}, "radio": {"radioManagement": "Radio Management", "radioGroups": "Radio Groups", "radioList": "Radio List", "radioName": "Radio Name", "radioUrl": "Radio URL", "radioGroup": "Radio Group", "createTime": "Create Time", "listen": "Listen", "noRadiosInGroup": "No radios in this group", "selectGroup": "Please select a group", "loadingRadios": "Loading radios...", "loadingGroups": "Loading groups...", "search": "Search Radio Groups", "noGroupSelected": "Please select a radio group first", "fetchGroupError": "Failed to fetch radio groups", "fetchDetailError": "Failed to fetch radio details"}, "upload": {"selectSongsLabel": "Please select songs to upload, supports mp3 and wav formats"}, "actions": {"uploadSong": "Upload Song", "auditSong": "Audit Song", "downloadSong": "Download Song"}, "table": {"serialNumber": "Serial Number", "name": "Name", "type": "Type", "duration": "Duration", "bitrate": "Bitrate (Kbps)", "size": "Size", "user": "User", "auditStatus": "Audit Status", "status": "Status", "actions": "Actions"}}, "index": {"serverDisconnectedReconnecting": "Server disconnected, reconnecting, please wait", "serverRestarting": "Server restarting, please wait", "serverConnectionError": "Server connection error, returning to login page", "serverReconnectSuccess": "Server reconnect successfully", "reloginReasons": {"permissionChanged": "User permission changed", "userRemoved": "User removed", "loginElsewhere": "User logged in elsewhere", "systemRestart": "System restart", "accountAbnormal": "Account abnormal"}, "reloginMessage": "{reason}, returning to login page", "reconnectAttempt": "Server connection error, attempting to reconnect, current retry count: {count}"}, "common": {"confirm": "Confirm", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "close": "Close", "loading": "Loading...", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "machineCode": "Machine Code", "enterRegistrationCode": "Please enter registration code", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "search": "Search", "pleaseConfirm": "Please confirm", "upload": "Upload", "none": "None", "status": "Status", "number": "Number", "name": "Name", "actions": "Actions", "user": "User", "unknown": "Unknown", "enabled": "Enabled", "disabled": "Disabled", "confirmDelete": "Confirm Delete", "deleteSuccess": "Delete Success"}, "monitor": {"monitorSettings": "Monitor Settings", "ipAddress": "IP Address", "cameraName": "Camera Name", "rtspAddress": "RTSP Address", "username": "Username", "password": "Password", "batchSettings": "<PERSON><PERSON> Settings", "deleteMonitorDevice": "Delete Monitor Device", "aboutToDeleteMonitorDevice": "About to delete monitor device", "videoMonitorSwitch": "Video Monitor Switch", "aboutToToggleMonitor": "About to {action} video monitoring function,", "enable": "Enable", "disable": "Disable", "monitorSwitchNotice": "Note: After changing video monitoring function switch, please restart server!", "addCustomMonitorDevice": "Add Custom Monitor Device", "editMonitor": "Edit Monitor", "batchEdit": "<PERSON>ch Edit", "search": "Search", "noMonitorDevicesFound": "No monitor devices found", "edit": "Edit", "delete": "Delete", "headers": {"serialNumber": "Serial Number", "addMethod": "Add Method", "cameraName": "Camera Name", "ipAddress": "IP Address", "mac": "MAC", "username": "Username", "status": "Status", "actions": "Actions"}, "status": {"offline": "Offline", "loggedIn": "Logged In", "loginFailed": "Login Failed", "unknown": "Unknown"}, "addMethod": {"auto": "Auto", "manual": "Manual"}, "actions": {"batchSet": "Batch Set", "enable": "Enable", "disable": "Disable"}, "messages": {"aboutTo": "About to", "videoMonitoringFunction": "video monitoring function", "restartWarning": "Note: After changing video monitoring function switch, please restart server!", "noMonitorDevices": "No monitor devices found", "setMonitorInfoSuccess": "Set monitor device info successfully", "addCustomMonitorSuccess": "Add custom monitor device successfully", "deleteMonitorSuccess": "Delete monitor device successfully", "monitorSwitchSuccess": "{action} video monitoring, will take effect after system restart", "requiredFieldsEmpty": "Required fields cannot be empty, please retry", "rtspAddressError": "RTSP address error, please re-enter"}, "table": {"serialNumber": "Serial Number", "joinMethod": "Join Method", "cameraName": "Camera Name", "ipAddress": "IP Address", "mac": "MAC", "username": "Username", "status": "Status", "actions": "Actions"}, "errors": {"requiredFieldsEmpty": "Required fields cannot be empty, please retry", "invalidRTSPAddress": "RTSP address error, please re-enter"}}, "partitionSystem": {"dialogs": {"editDeviceName": "<PERSON> <PERSON>ce Name", "deviceUpgrade": "Device Upgrade", "modifyDeviceIP": "Modify Device IP", "intercomSettings": "Intercom Settings", "triggerSettings": "<PERSON><PERSON>s", "editNetworkMode": "Edit Device Network Mode", "sipSettings": "SIP Settings", "informationPublish": "Information Publishing", "advancedVolumeSettings": "Advanced Volume Settings", "taskManagement": "Task Management", "newEditTask": "New/Edit Task", "taskPartitionSelection": "Task Partition Selection", "partitionSelection": "Partition Selection", "groupSelection": "Group Selection", "soundEffectSettings": "Sound Effect Settings", "monitorBinding": "Monitor Binding", "alarmSoundSelection": "Alarm Sound Selection", "triggerSongSelection": "<PERSON>gger Song Selection", "monitorSpeaker": "Monitor Speaker", "fireManagement": "Fire Management", "powerManagement": "Power Management", "restartDevice": "<PERSON><PERSON>", "resetDeviceData": "Reset Device Data", "bluetoothSettings": "Bluetooth Settings", "deleteDevice": "Delete Device", "deleteTask": "Delete Task", "noiseAdaptiveSettings": "Noise Adaptive Terminal Settings", "keyManagement": "Key Management", "audioCollectorSettings": "Audio Collector Settings", "audioCollectorPartitionSelection": "Audio Collector Settings - Partition Selection", "audioMixerSettings": "Audio Mixer Settings", "audioMixerPartitionSelection": "Audio Mixer Settings - Partition Selection", "phoneGatewaySettings": "Phone Gateway Settings", "phoneGatewayPartitionSelection": "Phone Gateway Settings - Partition Selection", "ampControllerStatus": "Amplifier Controller Status"}, "labels": {"enterNewName": "Please enter new name", "deviceName": "Device Name", "currentFirmwareVersion": "Current Firmware Version", "selectServerFirmware": "Select Server Firmware", "upgradeCurrentDeviceOnly": "Upgrade current device only (when off, upgrade all same type devices)", "serverFirmware": "Server Firmware", "uploadNewFirmware": "Upload New Firmware Locally", "autoGetIpAddress": "Auto Get IP Address", "ipAddress": "IP Address", "subnetMask": "Subnet Mask", "defaultGateway": "Default Gateway", "key1": "Key 1", "key2": "Key 2", "micInputVolumeLevel": "Microphone Input Volume Level", "intercomOutputVolumeLevel": "Intercom Output Volume Level", "setAllSameTypeDevices": "Set All Same Type Devices", "enableNoiseAdaptive": "Enable Noise Adaptive", "noiseDetectorChannels": "Noise Detector Channels", "inactive": "Inactive", "averageNoiseLevel": "Average Noise Level", "noiseVolumeSettings": "Noise/Volume Settings", "segment": "Segment", "noiseLevel": "Noise Level", "volumeLevel": "Volume Level", "bindPartitions": "Bind Partitions", "availablePartitions": "Available Partitions", "addPartition": "Add Partition", "removePartition": "Remove Partition", "fixedValue": "Fixed Value", "triggerSwitch": "<PERSON><PERSON>", "enable": "Enable", "triggerMode": "Trigger Mode", "levelTrigger": "Level Trigger", "shortCircuitTrigger": "Short Circuit Trigger", "triggerSong": "<PERSON><PERSON>", "triggerVolume": "Trigger Volume", "networkMode": "Network Mode", "mainServerIp": "Main Server IP Address", "mainServerPort": "Main Server Port", "backupServerIp": "Backup Server IP Address", "backupServerPort": "Backup Server Port", "sipSwitch": "SIP Switch", "callVolume": "Call Volume", "transportProtocol": "Transport Protocol", "serverIpAddress": "Server IP Address", "serverPort": "Server Port", "sipAccount": "SIP Account", "sipPassword": "SIP Password", "enterDisplayContent": "Please enter display content", "selectEffect": "Please select effect", "moveSpeed": "Move Speed (Range: 1-64; smaller value = faster speed)", "stayTime": "Stay Time (Range: 1-255; unit: seconds)", "displaySwitch": "Display Switch", "globalSubVolume": "Global Sub Volume", "localVolume": "Local Volume", "taskName": "Task Name", "selectedPartitions": "Selected Partitions", "selectedGroups": "Selected Groups", "songPlayback": "Song Playback", "audioCollection": "Audio Collection", "playlist": "Playlist", "playMode": "Play Mode", "playlistSongs": "Playlist Songs", "doubleClickToAdd": "Double click to add", "selectedSongs": "Selected Songs", "doubleClickToDelete": "Double click to delete", "partitionList": "Partition List", "groupList": "Group List", "currentSoundEffect": "Current Sound Effect", "selectSoundEffect": "Please select sound effect", "currentBinding": "Current Binding", "currentSelection": "Current Selection", "disabled": "Disabled", "channelName": "Channel Name", "triggered": "Triggered", "notTriggered": "Not Triggered", "currentTriggerPartitions": "Current Trigger Partitions", "controlMode": "Control Mode", "manualMode": "Manual Mode", "autoMode": "Auto Mode", "open": "Open", "restartAllSameTypeDevices": "Restart All Same Type Devices", "resetAllSameTypeDevicesData": "Reset All Same Type Devices Data", "bluetoothName": "Bluetooth Name", "bluetoothEncryption": "Bluetooth Encryption", "noPasswordRequired": "No Password Required", "passwordRequired": "Password Required", "bluetoothPassword": "Bluetooth Password", "alarmSound": "Alarm Sound", "triggerPartitionCount": "Trigger Partition Count", "triggerStatus": "Trigger Status", "switch": "Switch", "playVolume": "Play Volume", "playSource": "Play Source", "featuresAndOperations": "Features and Operations", "currentAlarmSound": "Current Alarm Sound", "autoTrigger": "Auto Trigger", "partitionVolume": "Partition Volume", "selectedPartitionCount": "Selected Partition Count", "mixingSwitch": "Mixing Switch", "triggerType": "Trigger Type", "functionSwitch": "Function Switch", "amplifierControllerStatus": "Amplifier Controller Status", "masterAmplifier": "Master Amplifier", "backupAmplifier": "Backup Amplifier", "backupAmplifierStatus": "Backup Amplifier Status", "statusSimulationControl": "Status Simulation Control", "dragOrSelectPartitionSort": "Drag to Sort"}, "buttons": {"upgrade": "Upgrade", "cancel": "Cancel", "confirm": "Confirm", "close": "Close", "resetChanges": "Reset Changes", "confirmChanges": "Confirm Changes", "edit": "Edit", "delete": "Delete", "createTask": "Create Task", "editTask": "Edit Task", "deleteTask": "Delete Task", "selectPartitions": "Select Partitions", "selectGroups": "Select Groups", "addAll": "Add All", "setCurrentChannel": "Set Current Channel", "setAllChannels": "Set All Channels", "allOn": "All On", "allOff": "All Off", "saveSort": "Save Sort", "bluetoothSettings": "Bluetooth Settings", "intercomSettings": "Intercom Settings", "sipSettings": "SIP Settings", "informationPublish": "Information Publishing", "monitorSpeaker": "Monitor Speaker", "deviceName": "Device Name", "deviceVolume": "Device Volume", "deviceUpgrade": "Device Upgrade", "ipSettings": "IP Settings", "networkMode": "Network Mode", "soundEffectSettings": "Sound Effect Settings", "triggerSettings": "<PERSON><PERSON>s", "monitorBinding": "Monitor Binding", "fireManagement": "Fire Management", "powerManagement": "Power Management", "audioCollectorSettings": "Audio Collector Settings", "parameterSettings": "Parameter Settings", "gatewaySettings": "Gateway Settings", "ampSettings": "Amplifier Settings", "taskManagement": "Task Management", "keyManagement": "Key Management", "restartDevice": "<PERSON><PERSON>", "resetDevice": "Reset Device", "sortByDeviceName": "Sort by <PERSON><PERSON> Name", "sortByDeviceIP": "Sort by Device IP", "partitionSelection": "Partition Selection"}, "steps": {"deviceSelection": "1. <PERSON><PERSON>", "audioSourceSelection": "2. Audio Source Selection"}, "placeholders": {"enterTaskName": "Please enter task name", "enterPartitionKeyword": "Please enter partition keyword", "enterGroupKeyword": "Please enter group keyword", "enterChannelName": "Please enter channel name"}, "tips": {"volumeCalculation": "Tip: Device output volume equals main volume multiplied by sub volume", "noAudioCollectorFound": "Tip: No audio collector found, please check device.", "playlistEmpty": "Tip: Playlist is empty, cannot select songs, please add playlist in control center", "noMonitorBound": "Currently no monitor bound, please select monitor device for binding", "noAlarmSoundSelected": "Currently no alarm sound selected, please select a program source as alarm sound", "noTriggerSongSelected": "Currently no trigger song selected, please select a program source as trigger song", "monitorSpeakerWarning": "Tip: Only one partition can be set as monitor speaker, if not set, monitoring function will", "noFireCollectorChannelInfo": "No current fire collector channel info found", "noPowerChannelInfo": "No current power channel info found", "aboutToRestartDevice": "About to restart device", "pleaseConfirm": "Please confirm", "aboutToResetDevice": "About to reset device", "andRestoreFactorySettings": "and restore factory settings, please confirm", "aboutToDeleteDevice": "About to delete device"}, "warnings": {"networkModeWarning": "Warning: About to modify device network mode, may cause serious consequences like device unable to come online, non-debugging personnel please do not operate.", "factoryResetWarning": "Warning: Devi<PERSON> is about to restore factory settings, may cause serious consequences like device unable to come online, non-debugging personnel please do not operate."}, "messages": {"deviceNameEmpty": "Device name cannot be empty", "deviceNameExists": "Device name already exists", "deviceNameTooLong": "<PERSON><PERSON> name too long, please re-enter", "selectFirmwareForUpgrade": "Please select firmware for upgrade", "selectFirmwareForUpload": "Please select firmware for upload", "firmwareNameFormatError": "File name format incorrect, current device only supports tar.gz format firmware with file name starting with {prefix}", "ipAddressCannotBeEmpty": "IP address/subnet mask/default gateway cannot be empty, please re-enter", "soundEffectNotChanged": "Sound effect not changed, please reselect", "selectFireCollectorChannel": "Please select a fire collector channel for operation", "channelNameCannotBeEmpty": "Channel name cannot be empty, please re-enter", "channelAlarmSoundNotSet": "Current channel alarm sound not set, please reset", "channelNotBoundToPartition": "Current channel not bound to partition, please reset", "canOnlySetOneMonitorSpeaker": "Can only set one partition as monitor speaker, please retry", "monitorSpeakerSameAsCurrent": "Monitor speaker same as current, please reselect", "monitorDeviceNotSelected": "Monitor device not selected, please retry", "canOnlyDeleteOfflineDevices": "Can only delete offline devices, please retry", "taskNameEmptyOrInvalid": "Task name empty or invalid, please re-enter", "taskNeedsAtLeastOnePartitionOrGroup": "Task needs at least one partition or group, please reset", "taskNeedsPlayMode": "Task needs play mode setting, please reset", "noTaskSongsSelected": "No task songs selected, please reselect", "noAudioCollectorSelected": "No audio collector selected, please reselect", "selectTaskForDelete": "Please select a task for delete operation", "noFirmwareAvailable": "No firmware available for upgrade on server", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "textCannotBeEmpty": "Text cannot be empty", "bluetoothNameFormat": "Bluetooth name should be alphanumeric characters within 32 characters", "bluetoothPasswordFormat": "Bluetooth password must be exactly 4 digits", "phoneNumberFormat": "Phone number format error", "nameConflictOrInvalid": "Name conflict or invalid, please retry", "intercomSettingsSuccess": "Set intercom terminal parameters successfully", "triggerSettingsSuccess": "Set trigger parameters successfully", "audioCollectorSettingsSuccess": "Set audio collector parameters successfully", "parameterSettingsSuccess": "Set parameters successfully", "canOnlyDeleteOfflineDevice": "Can only delete offline devices, please retry", "taskNeedsPartitionOrGroup": "Task needs at least one partition or group, please reset", "textLengthExceedsLimit": "Text length cannot exceed {maxBytes} bytes", "channelNameExceedsLimit": "Channel name cannot exceed {maxBytes} bytes"}, "status": {"normal": "Normal", "fault": "<PERSON><PERSON>", "idle": "Idle", "unknown": "Unknown", "registrationFailed": "Registration Failed", "registrationSuccess": "Registration Success", "registering": "Registering", "registrationTimeout": "Registration Timeout", "accountPasswordError": "Account/Password Error", "inCall": "In Call"}, "effects": {"auto": "Auto", "pageFlip": "Page Flip", "continuousLeftMove": "Continuous Left Move", "leftMoveWithStay": "Left Move (with Stay)", "continuousDownMove": "Continuous Down Move", "downMoveWithStay": "Down Move (with Stay)", "blink": "Blink", "continuousUpMove": "Continuous Up Move", "upMoveWithStay": "Up Move (with Stay)", "snow": "Snow", "autoWithDescription": "Auto (static when screen space is sufficient, otherwise continuous left move)", "pageFlipWithDescription": "Page Flip (static when screen space is sufficient, otherwise page display)"}, "triggers": {"levelTrigger": "Level Trigger", "shortCircuitTrigger": "Short Circuit Trigger"}, "tooltips": {"audioCollection": "Audio Collection: ", "selectedSongs": "Selected Songs", "estimatedPlayDuration": "Estimated Play Duration", "songDurationEach": "Each song plus 1s", "selectedAudioCollector": "Selected Audio Collector: "}, "confirmations": {"aboutToDeleteTask": "About to delete task", "pleaseConfirm": "Please confirm", "aboutToDeleteDevice": "About to delete device", "deleteTask": "Delete Task", "keyManagement": "Key Management", "audioCollectorSettings": "Audio Collector Settings", "parameterSettings": "Parameter Settings", "partitionSelection": "Partition Selection"}, "deviceTypes": {"smartPagerA": "Smart Pager A", "smartPagerB": "Smart Pager B", "smartPagerC": "Smart Pager C", "decoderTerminalA": "Decoder Terminal A", "decoderTerminalB": "Decoder Terminal B", "decoderTerminalC": "Decoder Terminal C", "decoderTerminalD": "Decoder Terminal D", "decoderTerminalE": "Decoder Terminal E", "decoderTerminalF": "Decoder Terminal F", "decoderTerminalG": "Decoder Terminal G", "fireCollectorA": "Fire Collector A", "fireCollectorB": "Fire Collector B", "fireCollectorC": "Fire Collector C", "fireCollectorF": "Fire Collector F", "audioCollectorA": "Audio Collector A", "audioCollectorB": "Audio Collector B", "audioCollectorC": "Audio Collector C", "audioCollectorF": "Audio Collector F", "powerSequencerA": "Power Sequencer A", "powerSequencerB": "Power Sequencer B", "powerSequencerC": "Power Sequencer C", "powerSequencerF": "Power Sequencer F", "audioRelay": "Audio Relay", "audioRelayC": "Audio Relay C", "remoteController": "Remote Controller", "remoteControllerC": "Remote Controller C", "remoteControllerF": "Remote Controller F", "phoneGateway": "Phone Gateway", "ampController": "Amplifier Master-Backup Controller", "noiseAdaptiveTerminal": "Noise Adaptive Terminal", "audioProcessor": "Audio Coprocessor"}, "priorities": {"default": "Default (lower than scheduled audio source)", "high": "High Priority (higher than scheduled audio source)"}, "ampStatus": {"workingBackup": "Working (backup main amplifier {id})"}, "keyActions": {"none": "None", "pauseResume": "Pause/Resume", "stop": "Stop", "previous": "Previous", "next": "Next", "volumeUp": "Volume Up", "volumeDown": "Volume Down"}, "triggerModes": {"levelTrigger": "Level Trigger", "shortCircuitTrigger": "Short Circuit Trigger"}, "soundEffects": {"off": "Off", "pop": "Pop", "dance": "Dance", "rock": "Rock", "classical": "Classical", "vocal": "Vocal", "soft": "Soft", "custom": "Custom"}, "playModes": {"sequential": "Sequential Play", "loop": "Loop Play", "random": "Random Play"}, "radioLabels": {"off": "Off", "on": "On", "mixedTrigger": "Mixed Trigger"}, "inputLabels": {"key": "Key", "audioSourcePriority": "Audio Source Priority", "triggerChannel": "Trigger Channel", "mixingPriority": "Mixing Priority (higher value = higher priority)", "musicSignalFadeLevel": "Music Signal Fade Level (effective when MIC signal exists)", "phoneWhitelist": "Phone Whitelist (separate multiple numbers with commas)", "followDeviceVolume": "Follow Device Volume"}, "transferLabels": {"partitionList": "Partition List", "selectedPartitions": "Selected Partitions", "enterPartitionKeyword": "Enter partition keyword"}, "switchLabels": {"enableDeviceSort": "Enable Device Sort"}, "buttonTitles": {"moveUpSelectedPartition": "Move Up Selected Partition", "moveDownSelectedPartition": "Move Down Selected Partition", "cancelSortChanges": "Cancel Sort Changes", "confirmSortChanges": "Confirm Sort Changes"}, "tableMessages": {"decoderPartitionNotExist": "Decoder partition devices do not exist", "noCurrentTypeDeviceFound": "No current type device found"}, "iconTitles": {"bluetoothPlay": "Bluetooth Play", "voiceIntercom": "Voice Intercom", "deleteDevice": "Delete Device"}, "successMessages": {"setParametersSuccess": "Set parameters successfully", "deleteRemoteControllerTaskSuccess": "Delete remote controller task successfully", "addRemoteControllerTaskSuccess": "Add remote controller task successfully", "editRemoteControllerTaskSuccess": "Edit remote controller task successfully", "setRemoteControllerKeySuccess": "Set remote controller key successfully", "deviceAdvancedVolumeSetSuccess": "Device advanced volume setting successful", "modifyDeviceNetworkModeSuccess": "Modify device network mode successfully", "setSipAccountInfoSuccess": "Set SIP account information successfully", "setInformationPublishParametersSuccess": "Set information publishing parameters successfully", "setPartitionBluetoothParametersSuccess": "Set partition bluetooth parameters successfully", "modifyDeviceInfoSuccess": "Modify device information successfully", "upgradeFirmwareSuccess": "Upgrade firmware successfully", "modifyDeviceIpSuccess": "Modify device IP successfully", "uploadNewFirmwareSuccess": "Upload new firmware successfully", "startUpgrade": "Start upgrade", "currentFirmwareIsLatest": "Current firmware is already the latest version", "upgradeComplete": "Upgrade complete", "upgradeCompleteStartingDevice": "Upgrade complete, starting device", "restartDeviceSuccess": "Restart device successfully", "resetDeviceDataSuccess": "Reset device data successfully", "setSoundEffectSuccess": "Set sound effect successfully", "monitorBindingSuccess": "Monitor binding successful", "partitionDeviceCustomSortSuccess": "Partition device custom sort successful, will return to login page", "deleteDeviceSuccess": "Delete device successfully"}, "errorMessages": {"upgradeFirmwareFailed": "Upgrade firmware failed", "uploadNewFirmwareFailed": "Upload new firmware failed", "upgradeFailed": "Upgrade failed", "connectServerTimeout": "Connect server timeout", "currentDeviceNotSelected": "Current device not selected, please retry", "cannotModifyOfflineDevice": "Cannot modify offline device, please select online device", "currentFirmwareServerExists": "Current firmware already exists on server, please retry"}, "headerLabels": {"serialNumber": "Serial Number", "deviceName": "Device Name", "deviceType": "Device Type", "deviceIp": "Device IP", "mac": "MAC", "networkMode": "Network Mode", "boundCamera": "Bound Camera", "signalQuality": "Signal Quality", "iccid": "ICCID", "firmwareVersion": "Firmware Version", "runningStatus": "Running Status"}, "userLabels": {"andOthers": " and so on ", "users": " users"}, "signalQuality": {"veryPoor": "Very Poor", "poor": "Poor", "average": "Average", "good": "Good", "excellent": "Excellent", "unknown": "Unknown"}, "songInfo": {"duration": "Duration", "selectedSongs": "Selected Songs", "estimatedPlayDuration": "Estimated Play Duration"}, "validation": {"invalidVolumeArrayLength": "Invalid volume settings array length, should be 8 segments", "invalidVolumeRange": "Invalid volume value for segment {segment} ({noiseLevel}dB), should be between 1-100", "volumeNotIncreasing": "Volume value {currentVolume}% for segment {currentSegment} ({currentNoise}dB) cannot be lower than segment {previousSegment} ({previousNoise}dB) volume value {previousVolume}%"}, "statusLabels": {"idle": "Idle", "normal": "Normal", "fault": "<PERSON><PERSON>", "unknown": "Unknown"}}, "utils": {"playModes": {"single": "Single Play", "singleLoop": "Single Loop", "sequential": "Sequential Play", "loop": "Loop Play", "random": "Random Play"}, "channelFormat": "(CH{channel})", "channelsFormat": "({channels})", "andMoreSongs": " and so on {count} songs", "andMoreDevices": " and so on {count} devices", "andMoreZones": " and so on {count} zones", "andMoreGroups": " and so on {count} groups"}}