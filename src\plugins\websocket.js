import Vue from 'vue'
import store from '../store'
import { getSumOfIntArray } from './utils'
import i18n from '../i18n'

/**
 * 存储客户信息的对象
 * 支持扩展，需要增加相应字段
 * 使用find()方法通过customerId和functionId确定customer，从而获取颜色和logo信息
 * @param customerId 客户ID
 * @param functionId 客户功能ID
 * @param color web默认颜色
 * @param logoFileName logo相应位置，需放入/src/assets目录下
 * @param isShowLogo 布尔值，是否显示logo
 * @param loginBackgroundImg 登录界面背景图，为null或''代表不需要显示背景图，需放入/src/assets目录下
 * @param titleName title名称，为null或''代表使用默认：IP广播系统
 * @param isShowListView 布尔值，是否在主控制界面中显示列表视图
 * @param drawerSidebarImg 导航栏背景图，如果为空或未指定则使用默认sidebar1.jpg，需放入/src/assets目录下
 * @param logoMaxWidth 导航栏logo最大宽度，默认为140px，自动按照该宽度缩放比例
 * @param logoOffsetClass 导航栏logo样式class
 */
const customerInformation = [
  {customerId: '0', functionId: 'A0', color: '#4CAF50', logoFileName: '', isShowLogo: false, loginBackgroundImg: '', isShowListView: true}, // C0A0代表常规绿色版本
  {customerId: '0', functionId: 'A1', color: '#4CAF50', logoFileName: '', isShowLogo: false, loginBackgroundImg: 'background_mhi.jpg', titleName: '物联网云服务平台', isShowListView: true}, // C0A1代表明辉智能（用于网页备案展示）
  {customerId: '0', functionId: 'B0', color: '#4CAF50', logoFileName: '', isShowLogo: false, loginBackgroundImg: '', isShowListView: true}, // C0B0代表常规绿色版本，默认英文，且屏蔽声卡采集
  {customerId: '1', functionId: 'A0', color: '#ea5119', logoFileName: 'logo_aisp.png', isShowLogo: true, loginBackgroundImg: 'background_aisp.jpg',
    drawerSidebarImg: 'sidebar_aisp.jpg', logoMaxWidth: '100px', logoOffsetClass: 'ml-1', isShowListView: true}, // C1A0代表安思柏（橙色）
  {customerId: '1', functionId: 'A1', color: '#4CAF50', logoFileName: 'logo_cyzg.png', isShowLogo: true, loginBackgroundImg: '', isShowListView: true}, // C1A1代表安思柏（创远之歌）
  {customerId: '1', functionId: 'A2', color: '#4CAF50', logoFileName: 'logo_hiptre.png', isShowLogo: true, loginBackgroundImg: '', isShowListView: true}, // C1A2代表安思柏（海特）
  {customerId: '1', functionId: 'A3', color: '#4CAF50', logoFileName: 'logo_ytzx.png', isShowLogo: true, loginBackgroundImg: '', logoOffsetClass: 'ml-3', isShowListView: true}, // C1A3代表安思柏（云泰智信）
  {customerId: '1', functionId: 'A4', color: '#4CAF50', logoFileName: 'logo_svs.png', isShowLogo: true, loginBackgroundImg: '', logoMaxWidth: '100px', logoOffsetClass: 'ml-2', isShowListView: true}, // C1A3代表安思柏（SVS）
  {customerId: '1', functionId: 'A5', color: '#1E90FF', logoFileName: 'logo_vtron.png', isShowLogo: true, loginBackgroundImg: '', logoMaxWidth: '140px', logoOffsetClass: 'ml-1', isShowListView: true}, // C1A3代表安思柏（SVS）
  {customerId: '1', functionId: 'A6', color: '#4CAF50', logoFileName: 'logo_seingrdiu.png', isShowLogo: true, loginBackgroundImg: '', logoMaxWidth: '160px', logoOffsetClass: 'ml-2', isShowListView: true}, // C1A3代表安思柏（seingrdiu）
  {customerId: '1', functionId: 'A7', color: '#4CAF50', logoFileName: 'logo_haiwei.png', isShowLogo: true, loginBackgroundImg: '',
    drawerSidebarImg: 'sidebar_haiwei.jpg', logoMaxWidth: '160px', logoOffsetClass: 'ml-1', isShowListView: true}, // C1A7代表安思柏（haiwei）
  {customerId: '2', functionId: 'A0', color: '#4CAF50', logoFileName: 'logo_lax.png', isShowLogo: true, loginBackgroundImg: '', isShowListView: true}, // C2A0代表易会智能（LAX）
  {customerId: '2', functionId: 'A1', color: '#4CAF50', logoFileName: 'logo_gldt.png', isShowLogo: true, loginBackgroundImg: '', isShowListView: true}, // C2A1代表易会智能（GLDT）
  {customerId: '2', functionId: 'A2', color: '#4CAF50', logoFileName: 'logo_centre.png', isShowLogo: true, loginBackgroundImg: '', isShowListView: true}, // C2A2代表易会智能（CENTRE）
  {customerId: '2', functionId: 'A3', color: '#4CAF50', logoFileName: 'logo_sywy.png', isShowLogo: true, loginBackgroundImg: '', logoOffsetClass: 'ml-2', isShowListView: true}, // C2A3代表易会智能（视音微云）
  {customerId: '2', functionId: 'A4', color: '#4CAF50', logoFileName: 'logo_lotfun.png', isShowLogo: true, loginBackgroundImg: '', isShowListView: true}, // C2A4代表易会智能（乐坊）
  {customerId: '2', functionId: 'A5', color: '#4CAF50', logoFileName: 'logo_thzn.png', isShowLogo: true, loginBackgroundImg: '', isShowListView: true}, // C2A5代表易会智能（腾华智能）
  {customerId: '2', functionId: 'A6', color: '#4CAF50', logoFileName: 'logo_junnan.png', isShowLogo: true, loginBackgroundImg: '', isShowListView: true}, // C2A6代表易会智能（君南科技）
  {customerId: '2', functionId: 'A7', color: '#F0831E', logoFileName: 'logo_scgj.png', isShowLogo: true, loginBackgroundImg: 'background_scgj.jpg',
    drawerSidebarImg: 'sidebar_scgj.jpg', logoMaxWidth: '120px', logoOffsetClass: 'ml-1', isShowListView: true}, // C2A7代表易会智能（松川国际-特美声旗下）
  {customerId: '2', functionId: 'A8', color: '#109A8D', logoFileName: 'logo_fensav.png', isShowLogo: true, loginBackgroundImg: '',logoMaxWidth: '150px', logoOffsetClass: '', isShowListView: true}, // C2A8代表泛思
  {customerId: '2', functionId: 'A9', color: '#4CAF50', logoFileName: 'logo_huiming.png', isShowLogo: true, loginBackgroundImg: '', isShowListView: true}, // C2A9代表慧明
  {customerId: '2', functionId: 'B0', color: '#4CAF50', logoFileName: 'logo_dahua.png', isShowLogo: true, loginBackgroundImg: '',
    drawerSidebarImg: 'sidebar_dahua.jpg', logoMaxWidth: '145px', logoOffsetClass: '', isShowListView: true}, // C2B0代表大华
  {customerId: '2', functionId: 'B2', color: '#4CAF50', logoFileName: 'logo_jiuwu.png', isShowLogo: true, loginBackgroundImg: '', logoMaxWidth: '120px', isShowListView: true}, // C2B2代表九五
  {customerId: '2', functionId: 'B3', color: '#4CAF50', logoFileName: 'logo_pulai.png', isShowLogo: true, loginBackgroundImg: '', isShowListView: true}, // C2B3代表谱涞
  {customerId: '2', functionId: 'B4', color: '#4CAF50', logoFileName: 'logo_funinc.png', isShowLogo: true, loginBackgroundImg: '', logoMaxWidth: '125px', isShowListView: true}, // C2B4代表华云思创
  {customerId: '2', functionId: 'B5', color: '#4CAF50', logoFileName: 'logo_meahanrt.png', isShowLogo: true, loginBackgroundImg: '', logoMaxWidth: '150px', isShowListView: true}, // C2B4代表曼哈特
  {customerId: '2', functionId: 'B6', color: '#4CAF50', logoFileName: 'logo_sywy.png', isShowLogo: true, loginBackgroundImg: '', logoOffsetClass: 'ml-2', titleName: '云广播系统', isShowListView: true}, // C2B6代表易会智能（视音微云）应急广播项目
  {customerId: '2', functionId: 'B7', color: '#5683D6', logoFileName: 'logo_rehart.png', isShowLogo: true, loginBackgroundImg: '', logoOffsetClass: 'ml-0', isShowListView: true}, // C2B8 迪声
  {customerId: '3', functionId: 'A0', color: '#ba51d0', logoFileName: 'logo_jusbe.png', isShowLogo: true,
    loginBackgroundImg: 'background_jusbe.jpg', titleName: '智慧云广播系统', isShowListView: true}, // C3A0代表佳比（紫色）
  {customerId: '3', functionId: 'A1', color: '#00CAE3', logoFileName: '', isShowLogo: false,
    loginBackgroundImg: 'background_jusbe.jpg', titleName: 'AI智能互联系统', isShowListView: true}, // C3A1代表佳比子品牌（青色）
  {customerId: '3', functionId: 'A2', color: '#E91E63', logoFileName: '', isShowLogo: false,
    loginBackgroundImg: 'background_jusbe.jpg', titleName: 'IOT数字物联系统', isShowListView: true}, // C3A2代表佳比子品牌（红色）
  {customerId: '4', functionId: 'A0', color: '#4CAF50', logoFileName: '', isShowLogo: false,
    loginBackgroundImg: '', titleName: '云广播系统', isShowListView: true}, // C4A0代表龙之音（云广播系统）
  {customerId: '4', functionId: 'B0', color: '#4CAF50', logoFileName: '', isShowLogo: false,
    loginBackgroundImg: '', titleName: '云广播系统', isShowListView: true}, // C4B0代表龙之音（云广播系统）
  {customerId: '4', functionId: 'A2', color: '#4CAF50', logoFileName: '', isShowLogo: false,
    loginBackgroundImg: '', titleName: '村村通广播系统', isShowListView: true}, // C4A2代表龙之音（村村通广播系统）
  {customerId: '5', functionId: 'A0', color: '#3498DB', logoFileName: '', isShowLogo: false,
    loginBackgroundImg: '', titleName: 'IP广播系统', isShowListView: true}, // C5A0代表伟声
  {customerId: '5', functionId: 'A1', color: '#3498DB', logoFileName: '', isShowLogo: false,
    loginBackgroundImg: '', titleName: 'IP广播系统', isShowListView: true}, // C5A0代表伟声-子用户不允许进入媒体管理
  {customerId: '6', functionId: 'A0', color: '#E91E63', logoFileName: 'logo_dlzn.png', isShowLogo: true,
    loginBackgroundImg: '', titleName: 'IOT数字物联系统', logoOffsetClass: 'mb-8', isShowListView: true}, // C6A0代表鼎龙
  {customerId: '8', functionId: 'A0', color: '#4CAF50', logoFileName: '', isShowLogo: false, loginBackgroundImg: '', titleName: '云互联智控系统', logoOffsetClass: 'mb-8',isShowListView: true}, // C8A0代表艾普
]

const cId = process.env.VUE_APP_CUSTOMER_ID // 从配置文件.env中读取customerId的值(也可以放在其他文件,方便更改即可）
const fId = process.env.VUE_APP_FUNCTION_CID // 从配置文件.env中读取functionId的值(也可以放在其他文件,方便更改即可）
const customer = customerInformation.find(customer => customer.customerId === cId && customer.functionId === fId)
export var primaryColor = (customer && customer.color) || '#4CAF50'                 // 默认颜色为绿色
export var logoFileName = (customer && customer.logoFileName) || ''                 // 默认logoFileName为空，即不显示
export var isShowLogo = (customer && customer.isShowLogo) || false                  // 默认不显示logo
export var isShowListView = (customer && customer.isShowListView) || false          // 默认不显示视图列表
export var loginBackgroundImg = (customer && customer.loginBackgroundImg) || ''     // 默认不设置登录页面背景图片
export var drawerSidebarImg = (customer && customer.drawerSidebarImg) || ''         // 默认导航栏背景图片
export var logoMaxWidth = (customer && customer.logoMaxWidth) || '140px'            // 默认logo大小
export var logoOffsetClass = (customer && customer.logoOffsetClass) || ''           // 默认logo样式
export var titleName = (customer && customer.titleName) || 'IP广播系统'               // 默认网页选项卡标题名称
export var customerVersion = 'C' + cId + fId           //example:C0A0

export function getCustomerTitle() {
  if(titleName === 'IP广播系统') {
    return i18n.t('customer.title.ipBroadcastSystem')
  }
  else if(titleName === '云广播系统') {
    return i18n.t('customer.title.cloudBroadcastSystem')
  }
  else if(titleName === '村村通广播系统') {
    return i18n.t('customer.title.villageVillageBroadcastSystem')
  }
  else if(titleName === 'AI智能互联系统') {
    return i18n.t('customer.title.aiSmartLinkSystem')
  }
  else if(titleName === 'IOT数字物联系统') {
    return i18n.t('customer.title.iotDigitalThingsLinkSystem')
  }
  else if(titleName === '物联网云服务平台') {
    return i18n.t('customer.title.iotCloudPlatform')
  }
  else if(titleName === '云互联智控系统') {
    return i18n.t('customer.title.cloudInterconnectControlSystem')
  }
  else if(titleName === '智慧云广播系统') {
    return i18n.t('customer.title.smartCloudBroadcastSystem')
  }
  else {
    return titleName
  }
}


export function init (needReLogin) {
  store.commit('WEBSOCKET_INIT', needReLogin)
}

export function close () {
  store.commit('WEBSOCKET_CLOSE')
}

// 获取所有分区的设备名称
export function getAllZonesName () {
  if (store.state.eventList.length < 1) {
    return
  }
  const zonesName = []
  store.state.eventList.forEach((zone) => {
    zonesName.push(zone.name)
  })
  return zonesName
}

// 通过分区macs获取分区信息
export function getZonesByZoneMacs (macs) {
  const zoneInfos = []
  if (macs === null || macs === undefined || macs.length === 0) {
    return zoneInfos
  }
  const zones = store.state.eventList
  for (let i = 0; i < macs.length; i++) {
    for (let j = 0; j < zones.length; j++) {
      if (zones[j].mac === macs[i]) {
        zoneInfos.push(zones[j])
        break
      }
    }
  }
  return zoneInfos
}

// 通过分区macs获取分区信息
export function getZoneByZoneMac (mac) {
  if (mac === null || mac === undefined || mac === '') {
    return {}
  }
  const zones = store.state.eventList.filter(zone => zone.mac === mac)
  return zones.length > 0 ? zones[0] : {}
}

// 通过分区macs获取分区名称
export function getZoneNamesByZoneMacs (macs) {
  const zoneNames = []
  if (macs.length !== 0) {
    const zones = getZonesByZoneMacs(macs)
    zones.forEach((zone) => {
      zoneNames.push(zone.name)
    })
  }
  return zoneNames
}

// 通过分区mac查询其是否在线
export function checkZoneOnlineByZoneMac (mac) {
  if (mac === null || mac === '') {
    return false
  }
  const zones = store.state.eventList
  let result = false
  for (let i = 0; i < zones.length; i++) {
    if (zones[i].mac === mac) {
      result = zones[i].source !== -1
      break
    }
  }
  return result
}

// 通过分组id获取分组信息
export function getGroupInfoByGroupId (ids) {
  const groupInfos = []
  if (ids === null || ids === undefined || ids.length === 0) {
    return groupInfos
  }
  const groups = store.state.groupList
  for (let i = 0; i < ids.length; i++) {
    for (let j = 0; j < groups.length; j++) {
      if (groups[j].group_id === ids[i]) {
        groupInfos.push(groups[j])
        break
      }
    }
  }
  return groupInfos
}
// 通过分组id获取分组名称
export function getGroupNameByGroupId (ids) {
  const groupNames = []
  if (ids.length !== 0) {
    const groups = getGroupInfoByGroupId(ids)
    groups.forEach((group) => {
      groupNames.push(group.group_name)
    })
  }
  return groupNames
}
// 通过分组id获取分区mac信息
export function getZoneMacsByGroupId (ids) {
  const zoneMacs = []
  if (ids === null || ids === undefined || ids.length === 0) {
    return zoneMacs
  }
  const groups = getGroupInfoByGroupId(ids)
  groups.forEach((group) => {
    group.zones.forEach((mac) => {
      zoneMacs.push(mac)
    })
  })
  return zoneMacs
}
// 通过列表id获取列表
export function getListByListId (id) {
  let list = null
  const lists = store.state.playList
  if (id === null || id === undefined || lists === null || lists === undefined) {
    return list
  }
  for (let i = 0; i < lists.length; i++) {
    if (lists[i].list_id === id) {
      list = lists[i]
      break
    }
  }
  return list
}

// 根据标准协议 生成uuid
export function getUuid () {
  var s = []
  var hexDigits = '0123456789abcdef'
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-'

  var uuid = s.join('')
  return uuid
}

/**
 * 上传固件
 * @param uuid
 * 1. 请求上传固件到服务器
 */
export function uploadFirmware (file) {
  // console.log('准备上传固件')
  if (!file) {
    return false
  }
  try {
    uploadFirmwareByCgi(file)
  } catch (e) {
    return false
  }
  return true
}

/**
 * 上传服务器升级包
 * @param file
 * @returns {boolean}
 */
export function uploadServePackage (file) {
  // console.log('准备上传升级包' + file.name)
  if (!file) {
    return false
  }
  try {
    uploadServerPackageByCgi(file)
  } catch (e) {
    return false
  }
  return true
}

/**
 * 上传系统备份
 * @param file
 * @returns {boolean}
 */
export function uploadServerData (file) {
  // console.log('准备上传配置文件' + file.name)
  try {
    uploadServerDataByCgi(file)
  } catch (e) {
    return false
  }
  return true
}

/**
 * 上传歌曲
 * @param files
 * 1. 请求上传歌曲到服务器，等待服务器回复上传路径
 * 2. 通过post将文件上传到路径
 * 3. 上传完成后发送请求
 */
export function uploadSong (files) {
  //preUploadSong()
  return postUploadSong(files)
}

// 1.请求上传歌曲到服务器
export function preUploadSong () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'request_upload_song_to_server',
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 2. 通过post将文件上传
export function postUploadSong (files) {
  // console.log('准备上传' + files)
  if (!files || files.length <= 0) {
    return false
  }
  try {
    uploadOneSongByCgi(files)
  } catch (e) {
    return false
  }
  return true
}

/**
 * 添加歌曲至列表（for 媒体管理）
 * @param songNames
 * @param listId
 */
export function addSongToListForMedia(isNewUpload, songNames, listId) {
  if (isNewUpload) {
    if(!songNames || songNames.length <= 0) {
      return
    }
  }

  const sendData = {
    uuid: store.state.uuid,
    command: 'add_song_to_list',
    list_id: listId,
    song_count: songNames.length,
    song_names: isNewUpload?getSongNames(songNames):songNames,
  }
  if (isNewUpload) {
    sendData.temp_song_names = isNewUpload?getSongNames(songNames):songNames
  }
    // console.log('addSongToListForMedia' + JSON.stringify(sendData))
    store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 从列表删除歌曲（for 媒体管理）
 * @param songNames
 * @param listId
 * @param isRemoveLocal
 */
export function removeSongFromListForMedia(songNames, listId, isRemoveLocal) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'remove_song_from_list',
    list_id: listId,
    song_count: songNames.length,
    song_names: songNames,
    remove_local: isRemoveLocal,
  }
  // console.log('removeSongFromListForMedia' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}


/**
 * 审核曲库歌曲（for 媒体管理）
 * @param songNames
 * @param listId
 * @param isRemoveLocal
 */
export function auditSongForMedia(songNames) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'audit_local_song',
    song_count: songNames.length,
    song_names: songNames,
  }
  // console.log('auditSongForMedia' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

// 3. 上传完成后发送请求
export function addSongToList (files, listId) {
  if (!files || files.length <= 0) {
    return
  }
  const sendData = {
    uuid: store.state.uuid,
    command: 'add_song_to_list',
    list_id: listId,
    song_count: files.length,
    song_names: getSongNames(files),
  }
  setTimeout(() => {
    store.commit('WEBSOCKET_SEND', sendData)
  }, 500)
}

// 设置分组分区
export function setGroupZone (groupUuid, commandUuid, macs) {
  const data = {
    command: 'set_group_zone',
    uuid: store.state.uuid,
    command_uuid: commandUuid,
    group_id: groupUuid,
    page_count: 1,
    page: 1,
    zone_count: macs.length,
    zone_macs: macs,
  }
  store.commit('WEBSOCKET_SEND', data)
}

function getSongNames (files) {
  const names = []
  files.forEach((file) => {
    names.push(file.name)
  })
  return names
}

// 获取上传文件的总大小
export function getAllFileSize (files) {
  let totalSize = 0
  files.forEach((file) => {
    totalSize += file.size
  })
  return totalSize
}

// 通过调用服务的歌曲cgi上传
function uploadOneSongByCgi (files) {
  // var FileController = './cgi-bin/Addsong.cgi'
  // for (let i = 0; i < files.length; i++) {
  //   form.append('files', files[i], files[i].name)
  // }
  // 获取上传歌曲的总大小
  const totalSize = getAllFileSize(files)
  // console.log('上传歌曲的总大小为 : ' + totalSize)
  let uploadedCount=0
  for (let i = 0; i < files.length; i++) {
    let uploadSucceed=false
    const form = new FormData()
    form.append('files', files[i])
    const xhr = new XMLHttpRequest()
    xhr.open('post', Vue.prototype.$cgiAddSongAddress, true)
    xhr.onload = function () {
      if (xhr.status == 200) {
        uploadedCount = uploadedCount+1
        if(uploadedCount >= files.length) {
          store.commit('updateUploadSongRealProgress', 100)
        }
      }
    }
    // xhr.onerror = function (e) { console.log('上传失败') }
    xhr.upload.onprogress = function (event) {
      const sizeRate = Number((event.total / totalSize).toFixed(2))
      if (event.lengthComputable) {
        const completedPercent = Number((event.loaded / event.total * 100).toFixed(2))
        store.commit('updateUploadSongArrayProgress', { completedPercent, i, sizeRate })
      }
    }
    xhr.send(form)
  }
}

// 通过调用服务的固件cgi上传，暂时只开放单个文件的上传
function uploadFirmwareByCgi (file) {
  const form = new FormData()
  form.append('file', file)
  const xhr = new XMLHttpRequest()
  xhr.open('post', Vue.prototype.$cgiUpdateDeviceAddress, true)
  xhr.onload = () => {
    if ((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304) {
      // console.log('上传固件成功')
      store.commit('updateUploadFirmwareResult', true)
    }
  }
  xhr.onerror = () => {
    // console.log('上传失败')
    store.commit('updateUploadFirmwareResult', false)
  }
  xhr.upload.onprogress = function (event) {
    if (event.lengthComputable) {
      const completedPercent = (event.loaded / event.total * 100).toFixed(1)
      store.commit('updateUploadFirmwareProgress', completedPercent)
      // console.log('上传进度' + completedPercent)
    }
  }
  xhr.send(form)
}

// 通过调用服务的cgi进行服务器升级包上传
function uploadServerPackageByCgi (file) {
  // var FileController = './cgi-bin/UpgradeSystem.cgi'
  const form = new FormData()
  form.append('file', file)
  // console.log(form)
  const xhr = new XMLHttpRequest()
  xhr.open('post', Vue.prototype.$cgiUpgradeSystemAddress, true)
  xhr.onload = () => {
    if ((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304) {
      // console.log('上传升级包成功')
      store.commit('updateUploadServePackageResult', true)
    }
  }
  xhr.onerror = () => {
    // console.log('上传失败')
    store.commit('updateUploadServePackageResult', false)
  }
  xhr.upload.onprogress = function (event) {
    if (event.lengthComputable) {
      const completedPercent = (event.loaded / event.total * 100).toFixed(1)
      store.commit('updateUploadServePackageProgress', completedPercent)
    }
  }
  xhr.send(form)
}

// 通过调用服务的cgi进行系统配置文件上传
function uploadServerDataByCgi (file) {
  // var FileController = './cgi-bin/UpgradeSystem.cgi'
  // console.log('准备上传系统备份')
  const form = new FormData()
  form.append('file', file)
  // console.log(form)
  const xhr = new XMLHttpRequest()
  xhr.open('post', Vue.prototype.$cgiBackupFileUpdateAddress, true)
  xhr.onload = () => {
    if ((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304) {
      // console.log('上传配置文件成功')
      store.commit('updateUploadBackupResult', true)
    }
  }
  xhr.onerror = () => {
    // console.log('上传配置文件失败')
    store.commit('updateUploadBackupResult', false)
  }
  xhr.upload.onprogress = function (event) {
    if (event.lengthComputable) {
      const completedPercent = (event.loaded / event.total * 100).toFixed(1)
      store.commit('updateUploadBackupProcess', completedPercent)
    }
  }
  xhr.send(form)
}

// 发送请求升级服务器
export function upgradeServer (bagName) {
  const sendData = {
    command: 'upgrade_server',
    upgradebag_name: bagName,
    uuid: store.state.uuid,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 管理员获取所有分区/设备信息
export function getAllDeviceInfo () {
  const sendData = {
    command: 'get_device_info',
    page: 1,
    uuid: store.state.uuid,
    device_type: 0, // 代表分区设备
    all_zone: 1,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}
// 获取分组信息
export function getGroupList () {
  const sendData = {
    command: 'get_group_info',
    uuid: store.state.uuid,
    group_id: '0', // 分组uuid，若ID为”0”，则查询所有分组信息
  }
  store.commit('WEBSOCKET_SEND', sendData)
}
// 获取列表信息
export function getPlayList () {
  const sendData = {
    command: 'get_playlist_info',
    uuid: store.state.uuid,
    playlist_id: '0', // 若ID为”0”，则查询所有列表信息
  }
  store.commit('WEBSOCKET_SEND', sendData)
}
// 获取音频采集器信息
export function getAudioCollectors () {
  const sendData = {
    command: 'get_device_info',
    page: 1,
    uuid: store.state.uuid,
    device_type: 3, // 代表音频采集器
  }
  store.commit('WEBSOCKET_SEND', sendData)
}
// 获取消防采集器信息
export function getFireCollectors () {
  const sendData = {
    command: 'get_device_info',
    page: 1,
    uuid: store.state.uuid,
    device_type: 4, // 代表消防采集器
  }
  store.commit('WEBSOCKET_SEND', sendData)
}
// 获取寻呼台信息
export function getPagingCenter () {
  const sendData = {
    command: 'get_device_info',
    page: 1,
    uuid: store.state.uuid,
    device_type: 1, // 代表寻呼台
  }
  store.commit('WEBSOCKET_SEND', sendData)
}
// 获取网络时序器信息
export function getSequencePower () {
  const sendData = {
    command: 'get_device_info',
    page: 1,
    uuid: store.state.uuid,
    device_type: 5, // 代表网络时序器
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 获取音频协处理器信息
export function getAudioMixers () {
  const sendData = {
    command: 'get_device_info',
    page: 1,
    uuid: store.state.uuid,
    device_type: 6, // 代表音频协处理器
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 获取远程遥控器信息
export function getRemoteControlers () {
  const sendData = {
    command: 'get_device_info',
    page: 1,
    uuid: store.state.uuid,
    device_type: 7, // 代表远程遥控器
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 获取电话网关信息
export function getPhoneGateways () {
  const sendData = {
    command: 'get_device_info',
    page: 1,
    uuid: store.state.uuid,
    device_type: 8, // 代表电话网关
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 获取功放控制器信息
export function getAmpControlers () {
  const sendData = {
    command: 'get_device_info',
    page: 1,
    uuid: store.state.uuid,
    device_type: 9, // 代表功放控制器
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 获取噪声自适应终端信息
export function getNoiseDetector () {
  const sendData = {
    command: 'get_device_info',
    page: 1,
    uuid: store.state.uuid,
    device_type: 10, // 代表噪声自适应终端
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取服务器上传路径下的所有固件
 */
export function getServerUploadFirmwares () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_server_upload_firmwares',
    page: 1,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取定时点信息
 */
export function getServerTimer () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_timer_info',
    time_scheme_id: 0,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取/设置系统日期时间
 */
export function setSystemDateTime (isSet, time) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_system_date_time',
    set: isSet ? 1 : 0, // 0表示查询，1表示设置时间
  }
  if (isSet) {
    sendData.system_date_time = time
  }
  // console.log('获取最新时间set_system_date_time')
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取/设置系统网路设置
 */
export function setSystemNetwork (isSet, ip, subnet, gateway, dns) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_system_network',
    set: isSet ? 1 : 0, // 0表示查询，1表示设置时间
  }
  if (isSet) {
    sendData.ip_address = ip
    sendData.subnet_mask = subnet
    sendData.gateway = gateway
    sendData.dns_server = dns
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取/设置系统存储设置
 */
export function getSystemStorage () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_system_storage',
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取账户信息
 */
export function getUserInfo (username) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_user_info',
    dest_account: username,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取用户的分区信息
 */
export function getUserZone (username) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_user_zone',
    dest_account: username,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 请求升级设备
 */
export function upgradeFirmwares (firmwareName, onlyUpdateDevice, mac, deviceModel) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'upgrade_device',
    upgrade_type: 1, // 本地升级
    firmware_name: firmwareName,
    upgrade_device: onlyUpdateDevice ? 1 : 2, // 1为当前单个设备升级， 2为同类型设备升级
    device_model: deviceModel,
  }
  if (onlyUpdateDevice) {
    sendData.device_mac = mac
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 请求获取播放模式
 */
export function getPlayMode () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'play_mode',
    set: 0,
    selected_id: store.state.selectIdTime,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 请求获取播放模式
 */
export function setPlayMode (playMode) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'play_mode',
    set: 1,
    play_mode: playMode,
    selected_id: store.state.selectIdTime,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 服务器重启
 */
export function restart () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'reboot_server',
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 恢复出厂设置
 */
export function resetDefault () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'factory_reset',
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 重置服务器数据
 * @type: 传入的是int数组类型
 */
export function resetData (type) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'reset_server_data',
    file_types: getSumOfIntArray(type),
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 定时相关
/**
 * 设置当前定时方案
 */
export function setCurrentTimeSchema (id) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_current_time_scheme',
    time_scheme_id: id,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 启用/禁用定时点
 */
export function setTimePointStatus (schemaId, timerId, timerName, valid) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'time_point_valid',
    time_scheme_id: schemaId,
    time_point: {
      id: timerId,
      name: timerName,
    },
    isvalid: valid,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 添加定时点过程
 * 1. 添加定时点信息
 * 2. 设置定时点分区
 * 3. 设置定时点分组
 * 4. 设置定时点歌曲
 */

/**
 * 添加定时点信息
 * @param schemeId 定时方案id
 * @param commandUuid 标识uuid
 * @param timePoint 定时点信息
 * @param zoneMacs 分区macs
 * @param groupIds 分组ids
 * @param songPaths 歌曲路径string数组
 * @param sequencePowerInfos 电源时序器信息
 * @param audioCollectorInfo 音频采集器信息
 */
export function addTimerInfo (schemeId, commandUuid, timePoint, zoneMacs, groupIds, songPaths, sequencePowerInfos, audioCollectorInfo) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'add_time_point',
    time_scheme_id: schemeId,
    command_uuid: commandUuid,
    time_point: timePoint,
  }
  store.commit('WEBSOCKET_SEND', sendData)
  if (timePoint.device_type === 1) { // 如果设备类型是电源时序器
    setTimeSequencePower(schemeId, commandUuid, timePoint.id, timePoint.name, sequencePowerInfos)
  }
  else {  // 设备类型是解码终端
    setTimerSections(schemeId, commandUuid, timePoint.id, timePoint.name, zoneMacs)
    setTimerGroups(schemeId, commandUuid, timePoint.id, timePoint.name, groupIds)
    if(sequencePowerInfos != null) {
      setTimeSequencePower(schemeId, commandUuid, timePoint.id, timePoint.name, sequencePowerInfos)
    }
    if (timePoint.source_type === 0) {
      setTimerSongs(schemeId, commandUuid, timePoint.id, timePoint.name, songPaths)
    } else if (timePoint.source_type === 1) {
      setTimerAudioCollector(schemeId, commandUuid, timePoint.id, timePoint.name, audioCollectorInfo)
    }
  }
}

/**
 * 编辑定时点信息
 * @param schemeId 定时方案id
 * @param commandUuid 标识uuid
 * @param timePoint 定时点信息
 * @param zoneMacs 分区macs
 * @param groupIds 分组ids
 * @param songPaths 歌曲路径string数组
 * @param sequencePowerInfos 电源时序器信息
 * @param audioCollectorInfo 音频采集器信息
 */
export function editTimerInfo (schemeId, commandUuid, timePoint, zoneMacs, groupIds, songPaths, sequencePowerInfos, audioCollectorInfo) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'edit_time_point',
    time_scheme_id: schemeId,
    command_uuid: commandUuid,
    time_point: timePoint,
  }
  // console.log('准备编辑定时点' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
  if (timePoint.device_type == 1) { // 如果设备类型是电源时序器
    setTimeSequencePower(schemeId, commandUuid, timePoint.id, timePoint.name, sequencePowerInfos)
  }
  else {  // 设备类型是解码终端
    setTimerSections(schemeId, commandUuid, timePoint.id, timePoint.name, zoneMacs)
    setTimerGroups(schemeId, commandUuid, timePoint.id, timePoint.name, groupIds)
    if(sequencePowerInfos != null) {
      setTimeSequencePower(schemeId, commandUuid, timePoint.id, timePoint.name, sequencePowerInfos)
    }
    if (timePoint.source_type === 0) {
      setTimerSongs(schemeId, commandUuid, timePoint.id, timePoint.name, songPaths)
    } else if (timePoint.source_type === 1) {
      setTimerAudioCollector(schemeId, commandUuid, timePoint.id, timePoint.name, audioCollectorInfo)
    }
  }
}

/**
 * 设置定时分区
 * @param schemeId
 * @param commandUuid
 * @param id
 * @param name
 * @param zones
 */
export function setTimerSections (schemeId, commandUuid, id, name, zones) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_time_zone',
    time_scheme_id: schemeId,
    command_uuid: commandUuid,
    time_point: {
      id: id,
      name: name,
    },
    page: 1,
    page_count: 1,
    zone_count: zones.length, // 此页包含的分区数量,最多20个分区
    zone_macs: zones, // String 数组, 分区MAC地址数组
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 设置定时点分组
 * @param schemeId 定时方案id
 * @param commandUuid 标识uuid
 * @param id
 * @param name
 * @param groups
 */
export function setTimerGroups (schemeId, commandUuid, id, name, groups) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_time_group',
    time_scheme_id: schemeId,
    command_uuid: commandUuid,
    time_point: {
      id: id,
      name: name,
    },
    page: 1,
    page_count: 1,
    group_count: groups.length, // 此页包含的分组数量，最多20个分组
    group_ids: groups, // String 数组, 分组id数组
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 设置定时点歌曲
 * @param schemeId 定时方案id
 * @param commandUuid 标识uuid
 * @param id
 * @param name
 * @param songs
 */
export function setTimerSongs (schemeId, commandUuid, id, name, songs) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_time_song',
    time_scheme_id: schemeId,
    command_uuid: commandUuid,
    time_point: {
      id: id,
      name: name,
    },
    page: 1,
    page_count: 1,
    song_count: songs.length, // 此页包含的歌曲数量，最多20个歌曲歌曲路径名称
    song_pathnames: songs, // String 数组
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 设置定时点音频采集器
 * @param schemeId 定时方案id
 * @param commandUuid 标识uuid
 * @param id
 * @param name
 * @param audioCollectorInfo // 格式举例： device_mac|channel  2C:21:C8:B8:66:1C|1
 */
export function setTimerAudioCollector (schemeId, commandUuid, id, name, audioCollectorInfo) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_time_audio_collector',
    time_scheme_id: schemeId,
    command_uuid: commandUuid,
    time_point: {
      id: id,
      name: name,
    },
    page: 1,
    page_count: 1,
    audio_collector: {
      device_mac: audioCollectorInfo.split('|')[0],
      channel: Number(audioCollectorInfo.split('|')[1])
    }
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 设置定时点电源时序器
 * @param schemeId 定时方案id
 * @param commandUuid 标识uuid
 * @param id
 * @param name
 * @param sequencePowerInfos
 */
export function setTimeSequencePower (schemeId, commandUuid, id, name, sequencePowerInfos) {
  // if (sequencePowerInfos == null || sequencePowerInfos.length === 0) {
  //   return
  // }
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_time_sequence_power',
    time_scheme_id: schemeId,
    command_uuid: commandUuid,
    time_point: {
      id: id,
      name: name,
    },
    page: 1,
    page_count: 1,
    sequence_power_count: sequencePowerInfos.length,
    sequence_power_infos: sequencePowerInfos
  };
  // console.log(JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 移除定时点
 * @param schemeId 定时方案id
 * @param timerId 定时点id
 * @param timerName 定时点名称
 */
export function removeTimer (schemeId, timerId, timerName) {
  const timePoint = {
    id: timerId,
    name: timerName,
  }
  const sendData = {
    uuid: store.state.uuid,
    command: 'remove_time_point',
    time_scheme_id: schemeId,
    time_point: timePoint,
  }
  // console.log('发送移除定时点' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 复制定时点
 * @param schemeId 定时方案id
 * @param timerId 定时点id
 * @param timerName 定时点名称
 */
export function copyTimer (schemeId, timerId, timerName) {
  const timePoint = {
    id: timerId,
    name: timerName,
  }
  const sendData = {
    uuid: store.state.uuid,
    command: 'copy_time_point',
    time_scheme_id: schemeId,
    time_point: timePoint,
  }
  // console.log('发送复制定时点' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 定时点排序
 * @param schemeId 定时方案id
 */
 export function sortTimer (schemeId) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'sort_time_point',
    time_scheme_id: schemeId,
    sort_item: 4, // 固定为按开始时间排序，1:定时点名称 2：状态  3：播放周期  4：开始时间
    ascending_order: true,  //true: 升序  false：降序

  }
  // console.log('发送复制定时点' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

// 添加用户
export function addUser (commandUuid, username, password, zoneCount, authority) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'add_user',
    command_uuid: commandUuid,
    account: username,
    password: password,
    user_authority: {
      account: username,
      zone_count: zoneCount,
      authority: authority,
    },
  }
  // console.log(JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

export function addUserForMultiAccount (commandUuid, account, username, password, parentAccount, zoneCount, authority) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'add_user',
    command_uuid: commandUuid,
    account: account,
    user_name: username,
    parent_account: parentAccount,
    password: password,
    user_authority: {
      account: account,
      zone_count: zoneCount,
      authority: authority,
    },
  }
  console.log(JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

// 编辑用户
export function editUser (commandUuid, username, password, zoneCount, authority) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'edit_user',
    command_uuid: commandUuid,
    dest_account: username,
    dest_password: password,
    user_authority: {
      account: username,
      zone_count: zoneCount,
      authority: authority,
    },
  }
  // console.log(JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

export function editUserForMultiAccount (commandUuid, account, username, password, parentAccount, zoneCount, authority) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'edit_user',
    command_uuid: commandUuid,
    dest_account: account,
    dest_password: password,
    user_name: username,
    parent_account: parentAccount,
    user_authority: {
      account: account,
      zone_count: zoneCount,
      authority: authority,
    },
  }
  // console.log(JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

// 设置用户分区
export function setUserZone (commandUuid, username, password, zoneCount, zoneMacs) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_user_zone',
    command_uuid: commandUuid,
    dest_account: username,
    page_count: 1,
    page: 1,
    zone_count: zoneCount,
    zones_mac: zoneMacs,
  }
  // console.log(JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

// 移除用户
export function removeUser (username) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'remove_user',
    account: username,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 查询/设置分区EQ音效（仅针对解码设备）
export function deviceEqMode (isSet, mac, deviceModel, eqType, eqMode, soundEffect) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'device_eq_mode',
    set: isSet ? 1 : 0, // 0表示查询，1表示设置时间
    device_mac: mac,
    device_model: deviceModel,
    eq_type: eqType,
    eq_mode: eqMode,
  }
  if (soundEffect != null) {
    sendData.gain1 = soundEffect.gain1
    sendData.gain2 = soundEffect.gain2
    sendData.gain3 = soundEffect.gain3
    sendData.gain4 = soundEffect.gain4
    sendData.gain5 = soundEffect.gain5
    sendData.gain6 = soundEffect.gain6
    sendData.gain7 = soundEffect.gain7
    sendData.gain8 = soundEffect.gain8
    sendData.gain9 = soundEffect.gain9
    sendData.gain10 = soundEffect.gain10
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 获取电源时序器参数
export function getSequencePowerInfo () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_sequence_power_info',
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 设置电源时序器参数
export function setSequencePowerDevice (mac, mode, channelCount, channelInfos) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_sequence_power_device',
    device_mac: mac
  }
  if (mode != null) {
    sendData.mode = mode
  }
  if (channelCount != null && channelInfos != null) {
    sendData.channel_cnt = channelCount
    sendData.channel_infos = channelInfos
  }
  // console.log('发送json请求： ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

// 获取消防采集器详细参数
export function getFireCollectorInfo () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_fire_collector_info',
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 设置消防采集器
export function setFireDevice (commandUuid, mac, channelCount) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_fire_device',
    command_uuid: commandUuid,
    device_mac: mac,
    channel_count: channelCount,
  }
  // console.log('发送json请求： ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 设置消防通道（需要和setFireDevice方法一起使用）,构建fireChannel对象
 * fireChannel: {
 *   channel_id: 1, // 通道id
 *   name: aa, // 通道名称
 *   trigger: 0, // 触发模式:0 电平触发  1 短路触发
 *   sound: /x/xx, // 报警音效路径
 *   zone_macs: [], // 消防通道绑定的分区MAC
 * }
 */
export function setFireChannel (commandUuid, mac, fireChannel, allChannel) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_fire_channel',
    command_uuid: commandUuid,
    device_mac: mac,
    all_channel: allChannel ? 1 : 0, // 1:设置所有通道   0:设置单个通道
    fire_channel: fireChannel,
  }
  // console.log('发送json请求： ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 查询/设置监听音箱
 */
export function setAudioMonitorSpeaker (isSet, mac) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_audio_monitor_speaker',
    set: isSet ? 1 : 0, // 0表示查询，1表示设置
  }
  if (mac !== null) {
    sendData.device_mac = mac
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 开启/关闭节目源监听
 */
export function audioMonitorSource (isOper, selectId) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'audio_monitor_source',
    oper: isOper ? 1 : 0, // 0表示关闭，1表示开启
    selected_id: selectId, // 选中分区的标识，需传入
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 文字转语音
 */
export function textToSpeech (volume, pitch, speed, voiceName, fileName, text, rdn) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'text_to_speech',
    online: 2, // 在线/离线：（暂固定为离线）,1 为在线，2为离线
    volume: volume,
    pitch: pitch,
    speed: speed,
    voice_name: voiceName, // 发音人 离线模式下男声:xiaofeng, 女声:xiaoyan
    file_name: fileName, // 合成文件名称(不需扩展名)
    text: text, // 合成文本: 在线：500字以下 离线：1000字以下
    rdn: rdn, // 音频数字发音方式: 0：自动，不确定时按值发音, 1：按照值发音, 2：按照串发音, 3：自动，不确定时按照串发音
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 查询/设置蓝牙
 */
export function setBluetooth (isSet, mac, model, btName, btEncryption, btPin) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_bluetooth',
    device_mac: mac,
    device_model: model,
    set: isSet ? 1 : 0, // 0表示查询，1表示设置
    bluetooth_name: btName, // 蓝牙名称（最长32个字符）（不支持中文）
    bluetooth_encryption: btEncryption, // 蓝牙加密方式( 0 无需密码  1 需要密码)
    bluetooth_pin: btPin, // 蓝牙密码（固定4字符）一般蓝牙默认密码为0000
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 导出运行日志
 */
export function exportRunLogs (logType, startDate, endDate, deviceName, deviceMac, fileName) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'export_run_log',
    log_type: logType,
    start_date: startDate,
    end_date: endDate,
    device_name: deviceName,
    device_mac: deviceMac,
    file_name: fileName,
  }
  // console.log('export_run_log ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取监控设备信息
 */
export function getMonitorInfo () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_monitor_info',
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取今日定时点信息（今日任务）
 */
export function getTodayTimerInfo () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_today_timer_info',
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 设置监控设备信息
 */
export function setMonitorInfo (count, monitorDevice) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_monitor_info',
    monitor_count: count,
  }
  if (!Array.isArray(monitorDevice)) {
    sendData.monitors = [monitorDevice]
  } else {
    sendData.monitors = monitorDevice
  }
  // console.log('设置监控信息' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}


/**
 * 添加自定义监控设备
 */
export function addCustomMonitor (name, url) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'add_custom_monitor',
    name: name,
    url: url
  }
  // console.log('设置监控信息' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}


/**
 * 删除监控设备
 */
export function delMonitorInfo (count, monitorDevice) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'delete_monitor',
    monitor_count: count,
  }
  if (!Array.isArray(monitorDevice)) {
    sendData.monitors = [monitorDevice]
  } else {
    sendData.monitors = monitorDevice
  }
  // console.log('设置监控信息' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 开启/关闭监控RTSP
 * @param isOpen 1开启，2关闭
 */
export function monitorRtsp (isOpen, mac) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'monitor_rtsp',
    set: isOpen,
    monitor_mac: mac,
  }
  // console.log('发送rtsp请求' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 系统注册
 * @param isOpen 1开启，2关闭
 */
export function systemRegister (code) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'server_register',
    serial_code: code,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 扩展81-120的音频采集
 * @returns {Map<string, string>}
 */
export function getDevicePlayStatusMap() {
  let devicePlayStatusMap = new Map([
    ['-1', i18n.t('deviceStatus.offline')],
    ['0', i18n.t('deviceStatus.idle')],
    ['1', i18n.t('deviceStatus.localPlay')],
    ['2', i18n.t('deviceStatus.networkOnDemand')],
    ['3', i18n.t('deviceStatus.scheduled')],
    ['4', i18n.t('deviceStatus.audioMixing')],
    ['5', i18n.t('deviceStatus.intercom')],
    ['6', i18n.t('deviceStatus.monitorEventTrigger')],
    ['7', i18n.t('deviceStatus.monitoring')],
    ['8', i18n.t('deviceStatus.fireAlarm')],
    ['9', i18n.t('deviceStatus.networkPaging')],
    ['10', i18n.t('deviceStatus.hundredVolt')],
    ['11', i18n.t('deviceStatus.sipCall')],
    ['13', i18n.t('deviceStatus.api')],
    ['14', i18n.t('deviceStatus.netRadio')],
    ['32', i18n.t('deviceStatus.phoneGateway')]
  ])
  for (let i = 81; i < 121; i++) {
    devicePlayStatusMap.set(i + '', i18n.t('deviceStatus.audioCollection'))
  }
  return devicePlayStatusMap
}

/**
 * 获取不同分区播放状态的图标
 */
export function getIconOfZoneStatus() {
  let iconOfZoneStatusMap = new Map([
    ['-1', 'mdi-cloud-off-outline'], // 离线
    ['0', 'mdi-coffee'], // 空闲
    ['1', 'mdi-music-clef-treble'], // 本地播放
    ['2', 'mdi-music-clef-treble'], // 网络点播
    ['3', 'mdi-timer-outline'], // 定时
    ['4', 'mdi-cellphone-cog'], // 音频混音
    ['5', 'mdi-phone'], // 对讲
    ['6', 'mdi-music-clef-treble'], // 监控事件触发
    ['7', 'mdi-music-clef-treble'], // 监听
    ['8', 'mdi-alarm-light'], // 消防告警
    ['9', 'mdi-cast-audio'], // 网络寻呼
    ['10', 'mdi-flash'], // 100V
    ['13', 'mdi-music-clef-treble'], // API TTS/Music
    ['14', 'mdi-radio'], // 网络电台
    ['32', 'mdi-phone'] // 电话网关
  ])
  for (let i = 81; i < 121; i++) {
    iconOfZoneStatusMap.set(i + '', 'mdi-video-input-component') // 音频采集
  }
  return iconOfZoneStatusMap
}

/**
 * 获取服务器配置文件名称
 */
export function getBackupFileNames () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_backup_file_names',
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 服务器备份数据
 * @param type 1. 配置文件备份 2. 音乐文件备份
 */
export function backupServerData (type) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'backup_server_data',
    type: type, // 暂固定为1
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 查询/设置自动通话录音开关
 * @param set 1为设置，0为查询
 * @param enableCallRecord 是否启用通话录音
 */
export function callRecordSwitch (set, enableCallRecord) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'call_record_switch',
    set: set
  }
  if (set === 1) {
    sendData.enable_call_record = enableCallRecord
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 查询录音日志
 * @param startDate 开始日期
 * @param endDate 结束日期
 */
export function queryCallLog (startDate, endDate, callerMac) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'query_call_log',
    start_date: startDate,
    end_date: endDate
  }
  if(callerMac) {
    sendData.caller_mac = callerMac
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 删除通话日志
 * @param callId 通话ID
 */
export function deleteCallLog (callId) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'delete_call_log',
    call_id: callId
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 移除配置文件
 * @param backupName
 */
export function removeBackupFile (backupName) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'remove_backup_file',
    backup_name: backupName
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 服务器上传数据
 * @param backupName
 */
export function uploadServerDataFromLocal (backupName) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'upload_server_data',
    backup_name: backupName
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 服务器还原数据
 * @param backupName
 */
export function restoreServerData (backupName) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'restore_server_data',
    backup_name: backupName
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 登录
 * @param username
 * @param password
 */
export function authSuccess (username, password) {
  const sendData = {
    command: 'user_login',
    account: username,
    password: password,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 复制定时方案
 * @param timeSchemaId 定时方案序号
 */
export function copyTimeSchema (timeSchemaId) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'copy_time_scheme',
    time_scheme_id: timeSchemaId
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 分区设备自定义排序
 * @param macs 包括zone_count个分区的mac地址
 */
export function sortDeviceCustom (macs, deviceType) {
  if (macs == null || macs.length === 0) {
    return
  }
  const sendData = {
    uuid: store.state.uuid,
    command: 'sort_device_custom',
    device_type: deviceType,
    zone_count: macs.length,
    zones_mac: macs,
  };
  // console.log('test ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 歌曲自定义排序
 */
export function sortSongList (listId, songMd5s) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'sort_song_list',
    list_id: listId,
    song_count: songMd5s.length,
    songs_md5: songMd5s,
  };
  // console.log('test ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 删除设备
 * @param deviceMac 设备MAC
 * @param deviceModel 设备类型
 */
export function deleteDevice (deviceMac, deviceModel) {
  if (deviceMac == null || deviceModel == null) {
    return
  }
  const sendData = {
    uuid: store.state.uuid,
    command: 'delete_device',
    device_mac: deviceMac,
    device_model: deviceModel,
  };
  // console.log('test ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取手动任务信息
 */
export function getManualTaskInfo () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_manual_task_info'
  };
  // console.log('get_manual_task_info ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 获取服务器本地歌曲信息
 */
export function getLocalSongListInfo () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_local_songlist_info'
  };
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 设置任务播放模式
 * @param playIds 播放id数组
 * @param playMode 1：单曲播放  2：单曲循环  3：顺序播放 4：循环播放  5：随机播放
 * @param isAllTask 0.设置部分手动任务 1. 设置账户所有手动任务
 */
export function setTaskPlayMode (playIds, playMode, isAllTask) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_task_play_mode',
    all_task: isAllTask ? 1 : 0,
    play_mode: playMode
  };
  if (!isAllTask) {
    sendData.task_cnt = playIds.length
    sendData.play_ids = playIds
  }
  // console.log('set_task_play_mode ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 控制任务播放状态
 * @param playIds 播放id数组
 * @param playStatus 播放状态:1 播放  2暂停   4停止
 */
export function setTaskPlayStatus (playIds, playStatus) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_task_play_status',
    task_cnt: playIds.length,
    play_ids: playIds,
    play_status: playStatus
  };
  // console.log('set_task_play_status ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 查询/设置手动任务播放进度
 * @param playIds 播放id数组
 * @param playTime 当前播放时间
 */
export function setTaskProgress (isSet, playId, playTime) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_task_playback_progress',
    set: isSet?1:0,
    play_id: playId,
    cur_play_time: playTime
  };
  // console.log('set_task_play_status ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 控制任务播放上一曲/下一曲
 * @param playIds 播放id数组
 * @param action 动作:1 上一曲  2下一曲
 */
export function setTaskPlayPreNext (playIds, action) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_task_play_pre_next',
    task_cnt: playIds.length,
    play_ids: playIds,
    action: action
  };
  // console.log('set_task_play_pre_next ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 创建新列表
 * @param listId 列表id
 * @param listName 列表名称
 */
export function addSongList (listId, listName) {
  const sendData = {
    command: 'add_song_list',
    uuid: store.state.uuid,
    list_id: listId,
    list_name: listName,
  };
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 重命名列表
 * @param listId 列表id
 * @param listName 列表名称
 */
export function renameSongList (listId, listName) {
  const sendData = {
    command: 'rename_song_list',
    uuid: store.state.uuid,
    list_id: listId,
    list_name: listName,
  };
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 删除列表
 * @param listId 列表id
 */
export function removeSongList (listId) {
  const sendData = {
    command: 'remove_song_list',
    uuid: store.state.uuid,
    list_id: listId,
  };
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 单次恢复/取消定时点
 * @param timeSchemeId 定时方案id
 * @param timerId 定时点id
 * @param timerName 定时点name
 * @param isCancel boolean, true代表取消，false代表恢复
 */
export function singleCancelTimePoint (timeSchemeId, timerId, timerName, isCancel) {
  const timePoint = {
    id: timerId,
    name: timerName,
  }
  const sendData = {
    command: 'single_cancel_time_point',
    uuid: store.state.uuid,
    time_scheme_id: timeSchemeId,
    time_point: timePoint,
    isCancel: isCancel
  };
  // console.log('singleCancelTimePoint ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

export function queryDeviceNetworkMode (deviceMac, deviceModel) {
  const sendData = {
    command: 'device_network_mode',
    uuid: store.state.uuid,
    all_model_device: 1,
    device_mac: deviceMac,
    device_model: deviceModel,
    set: 0
  };
  // console.log('queryDeviceNetworkMode ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

export function queryDeviceSipInfo (deviceMac) {
  const sendData = {
    command: 'set_sip_info',
    uuid: store.state.uuid,
    device_mac: deviceMac,
    set: 0
  };
  // console.log('queryDeviceNetworkMode ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

export function setDeviceSipInfo (deviceMac, sip_enable, sip_server_protocol, sip_output_vol, sip_account, sip_password, sip_server_ip, sip_server_port) {
  const sendData = {
    command: 'set_sip_info',
    uuid: store.state.uuid,
    device_mac: deviceMac,
    set: 1,
    sip_enable: sip_enable,
    sip_server_protocol: sip_server_protocol,
    sip_output_vol: sip_output_vol,
    sip_account: sip_account,
    sip_password: sip_password,
    sip_server_ip: sip_server_ip,
    sip_server_port: sip_server_port
  };
  // console.log('setDeviceSipInfo ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

export function queryDeviceInformationPubInfo (deviceMac) {
  const sendData = {
    command: 'set_information_publish',
    uuid: store.state.uuid,
    device_mac: deviceMac,
    set: 0
  };
  // console.log('queryDeviceNetworkMode ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}
export function setDeviceInformationPubInfo (deviceMac, enable_display, text, effects, speed, stay_time) {
  const sendData = {
    command: 'set_information_publish',
    uuid: store.state.uuid,
    device_mac: deviceMac,
    set: 1,
    enable_display: enable_display,
    text: btoa(text),
    effects: effects,
    speed: speed,
    stay_time: stay_time
  };
  // console.log('setDeviceSipInfo ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData)
}

export function queryTrigger(deviceMac, deviceModel) {
  setTrigger(false, deviceMac, deviceModel,false, false, false, null, null)
}

export function setTrigger (isSet, deviceMac, deviceModel, isAllModelDevice, isTrigger, isShortCircuit, triggerSongPathName, triggerVolume) {
  const sendData = {
    command: 'set_trigger',
    uuid: store.state.uuid,
    device_mac: deviceMac,
    device_model: deviceModel,
    set: isSet ? 1 : 0, // 0:查询    1:设置
    all_model_device: isAllModelDevice ? 2 : 1 // 是否设置所有同类型设备（查询时固定为单个设备） 1：单个设备 2：同一类型设备
  };
  if (isSet) {
    sendData.trigger_switch = isTrigger ? 1 : 0 // 触发开关：0-关闭  1-开启
    sendData.trigger_mode = isShortCircuit ? 1 : 0 // 触发模式：0 电平触发  1 短路触发
    sendData.trigger_song_path_name = triggerSongPathName // 触发歌曲
    sendData.trigger_volume = triggerVolume // 触发音量, int
  }
  // console.log('set_trigger ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

// 获取音频采集器详细参数
export function getAudioCollectorInfo () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_audio_collector_info',
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

export function queryAudioCollectorParm(deviceMac) {
  store.commit('updateQueryAudioCollectorParmResult', null)
  setAudioCollectorParm(false, deviceMac, null, null, null, null)
}

export function setAudioCollectorParm (isSet, deviceMac, channels, priority, triggerSwitch, triggerChannelId, triggerZoneVolume, triggerZoneMacs) {
  const sendData = {
    command: 'set_audio_collector_parm',
    uuid: store.state.uuid,
    device_mac: deviceMac,
    set: isSet ? 1 : 0, // 0:查询    1:设置
  };
  if (isSet) {
    sendData.channels = channels
    sendData.priority = priority                    // 音源优先级：1-默认，低于定时音源 2-高优先级，高于定时音源
    sendData.trigger_switch = triggerSwitch ? 1 : 0 // 触发开关：0-关闭  1-开启
    sendData.trigger_channel_id = triggerChannelId // 触发通道id（1~4），数值越大，优先级越高
    sendData.trigger_zone_volume = triggerZoneVolume // 触发音量
    sendData.trigger_zone_macs = triggerZoneMacs    // 触发分区
  }
  // console.log('set_audio_mixer_parm ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

export function queryAudioMixerParm(deviceMac) {
  store.commit('updateQueryAudioMixerParmResult', null)
  setAudioMixerParm(false, deviceMac, null, null, null, null, null, null)
}

export function queryPhoneGatewayParm(deviceMac) {
  store.commit('updateQueryPhoneGatewayParmResult', null)
  setPhoneGatewayParm(false, deviceMac, null, null, null, null, null, null)
}

export function queryAmpControlerParm(deviceMac) {
  store.commit('updateQueryAmpControlerParmResult', null)
  setAmpControlerParm(false, deviceMac)
}

export function queryNoiseDetectorParm(deviceMac) {
  store.commit('updateQueryNoiseDetectorParmResult', null)
  setNoiseDetectorParm(false, deviceMac, null, null, null)
}

export function setNoiseDetectorParm (isSet, deviceMac, enable_control, segment_volume, zone_macs) {
  const sendData = {
    command: 'noise_adaptive_config',
    uuid: store.state.uuid,
    device_mac: deviceMac,
    set: isSet ? 1 : 0, // 0:查询    1:设置
  };
  if (isSet) {
    sendData.enable_control = enable_control ? 1 : 0 // 0-关闭  1-开启
    sendData.segment_volume = segment_volume // 分段音量
    sendData.zone_macs = zone_macs    // 关联分区
  }
  // console.log('set_audio_mixer_parm ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}


export function setAudioMixerParm (isSet, deviceMac, master_switch, priority, trigger_type, volume_fade_level, zone_volume, zone_macs) {
  const sendData = {
    command: 'set_audio_mixer_parm',
    uuid: store.state.uuid,
    device_mac: deviceMac,
    set: isSet ? 1 : 0, // 0:查询    1:设置
  };
  if (isSet) {
    sendData.master_switch = master_switch ? 1 : 0 // 混音主开关：0-关闭  1-开启
    sendData.priority = priority // 混音优先级（1~9），数值越大，优先级越高
    sendData.trigger_type = trigger_type // 触发类型（1~3），1-混合触发 2-MIC 3-AUX
    sendData.volume_fade_level = volume_fade_level  // 存在MIC信号时，其他信号的淡化级别（0~9)
    sendData.zone_volume = zone_volume // 触发分区音量
    sendData.zone_macs = zone_macs    // 触发分区
  }
  // console.log('set_audio_mixer_parm ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}


export function setPhoneGatewayParm (isSet, deviceMac, master_switch, zone_volume, tel_whitelist, zone_macs) {
  const sendData = {
    command: 'set_phone_gateway_parm',
    uuid: store.state.uuid,
    device_mac: deviceMac,
    set: isSet ? 1 : 0, // 0:查询    1:设置
  };
  if (isSet) {
    sendData.master_switch = master_switch ? 1 : 0 // 电话网关主开关：0-关闭  1-开启
    sendData.zone_volume = zone_volume // 触发分区音量
    sendData.tel_whitelist = tel_whitelist  //电话白名单
    sendData.zone_macs = zone_macs    // 触发分区
  }
  // console.log('setPhoneGatewayParm ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

export function setAmpControlerParm (isSet, deviceMac) {
  const sendData = {
    command: 'amp_controler_config',
    uuid: store.state.uuid,
    device_mac: deviceMac,
    set: isSet ? 1 : 0, // 0:查询    1:设置
  };
  store.commit('WEBSOCKET_SEND', sendData);
}

export function setDeviceNetworkMode (isSetAllNetwork, deviceMac, deviceModel, networkMode, serverIp, serverPort, backupServerIp, backcpServerPort) {
  const sendData = {
    command: 'device_network_mode',
    uuid: store.state.uuid,
    all_model_device: isSetAllNetwork, // 1 单个设备 2 同类型设备
    device_model: deviceModel,
    set: 1,
    network_mode: networkMode // 1 UDP; 2 TCP
  };
  if (isSetAllNetwork === 1) {
    sendData.device_mac = deviceMac
  }
  if (networkMode === 2) {
    sendData.server_ip = serverIp;
    sendData.server_port = serverPort;
    sendData.backup_server_ip = backupServerIp;
    sendData.backup_server_port = backcpServerPort;
  }
  // console.log('device_network_mode ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

export function setSubVolume (isSetAllDevice, deviceMac, deviceModel, subVolume, auxVolume) {
  const sendData = {
    command: 'set_sub_volume',
    uuid: store.state.uuid,
    all_model_device: isSetAllDevice, // 1 单个设备 2 同类型设备
    device_model: deviceModel,
    set: 1,
    device_mac: deviceMac,
    sub_volume: subVolume,
    aux_volume: auxVolume
  };
  // console.log('set_sub_volume ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

export function querySubVolume (deviceMac) {
  const sendData = {
    command: 'set_sub_volume',
    uuid: store.state.uuid,
    set: 0,
    device_mac: deviceMac,
  };
  // console.log('set_sub_volume ==== query: ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

// 启用/停用视频监控
export function switchMonitor (isEnabled) {
  const sendData = {
    command: 'monitor_switch',
    uuid: store.state.uuid,
    set: 1,
    enable_monitor: isEnabled,
  };
  // console.log('monitor_switch ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

// 启用/停用考试模式
export function switchExaminationMode (isEnabled) {
  const sendData = {
    command: 'examination_mode_switch',
    uuid: store.state.uuid,
    set: 1,
    examination_mode: isEnabled,
  };
  // console.log('monitor_switch ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

/**
 * 获取远程遥控器参数
 */
export function getRemoteControllerInfo () {
  const sendData = {
    command: 'get_remote_controler_info',
    uuid: store.state.uuid,
  };
  // console.log('get_remote_controler_info ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

/**
 * 查询/设置主备服务器
 */
export function setBackupServerInfo (isSet, enable_server_sync, dest_server_ip) {
  const sendData = {
    command: 'set_backup_server',
    uuid: store.state.uuid,
    set: isSet ? 1 : 0, // 0:查询    1:设置
  };
  if (isSet) {
    sendData.enable_server_sync = enable_server_sync // 主备功能开关
    sendData.dest_server_ip = dest_server_ip //备用服务器IP
  }
  // console.log('setBackupServerInfo ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}


/**
 * 添加/编辑/删除远程遥控器任务
 * @param event 1 添加 2 编辑 3 删除
 * @param mac 设备mac
 * @param task remote_controler_task对象。如果是删除，task对象只需要传入task_id
 *
 * 示例如下
 * {
  "task_id":1,
  "name":"任务1",
  "source_type":0,
  "play_mode":3,
  "volume":30,
  "zone_macs": [
    "2C:21:43:71:56:39",
    "2C:21:43:71:56:38"
  ],
  "group_ids": [
    "83acfabb-07ef-46d6-8154-cb7e19ccd72d",
    "83acfabb-07ef-46d6-8154-cb7e19ccd73d",
  ],
  "song_pathnames": [
    "/Data/Program/Other/周传雄-黄昏.mp3"",
    "/Data/Program/Other/周杰伦-七里香.mp3"
  ],
  "audio_collector": {
    "device_mac": "2C:21:43:72:4A:4A",
    "channel":1
  }
}
 */
export function setRemoteControllerTask (event, mac, task) {
  const sendData = {
    command: 'set_remote_controler_task',
    uuid: store.state.uuid,
    event: event,
    device_mac: mac,
    task: task // 外层组装remote_controler_task对象
  };
  // console.log('set_remote_controler_task ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}

/**
 * 设置远程遥控器按键
 */
export function setRemoteControllerKey (mac, keyInfos) {
  const sendData = {
    command: 'set_remote_controler_key',
    uuid: store.state.uuid,
    device_mac: mac,
    keys: keyInfos // 外层组装remote_controler_key对象
  };
  // console.log('set_remote_controler_key ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}


/**
 * 启动/停止手动告警
 */
 export function startManualAlarm (status) {
  const sendData = {
    command: 'start_manual_alarm',
    uuid: store.state.uuid,
    set: status // 0代表自动 1代表开启 2代表关闭
  };
  store.commit('WEBSOCKET_SEND', sendData);
}

/**
 * 设置存储空间
 */
export function setUserStorageCapacity (destAccount, storageCapacity) {
  const sendData = {
    command: 'set_user_storage_capacity',
    uuid: store.state.uuid,
    dest_account: destAccount,
    storage_capacity: parseInt(storageCapacity), // 单位MB，范围50MB-1TB
  };
  store.commit('WEBSOCKET_SEND', sendData);
}

/**
 * 设置用户权限
 */
export function setUserAuthority (destAccount, authority) {
  const sendData = {
    command: 'set_user_authority',
    uuid: store.state.uuid,
    dest_account: destAccount,
    authority: parseInt(authority),
  };
  store.commit('WEBSOCKET_SEND', sendData);
}

/**
 * 查询/设置对讲终端基本参数
 */
export function setIntercomBasic (mac, isSet, isAllModelDevice, deviceModel, key1Mac, key2Mac, micVol, farOutputVolume) {
  const sendData = {
    command: 'set_intercom_basic',
    uuid: store.state.uuid,
    device_mac: mac,
    set: isSet ? 1 : 0, // 0查询 1设置
    all_model_device: isAllModelDevice ? 2 : 1, // 1单个设备 2 同一类型
    device_model: deviceModel,
    key1_mac: key1Mac, // 按键1对应的mac
    key2_mac: key2Mac, // 按键2对应的mac
    mic_vol:  micVol,
    far_out_vol:  farOutputVolume,
  }
  // console.log('set_intercom_basic ==== ' + JSON.stringify(sendData))
  store.commit('WEBSOCKET_SEND', sendData);
}


/**
 * 请求网页寻呼
 */
export function requesetWebPaging (isStart, vol, transmissionMode) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'request_paging',
    event: isStart? 1 : 0,
    paging_src: 1,
    transmission_mode: transmissionMode,
    volume: vol,
    selected_id: store.state.selectIdTime,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

export function setZoneVolume (vol) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'set_volume',
    volume: vol,
    selected_id: store.state.selectIdTime,
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

export function cloud_control_switch (isSet, cloudSwitch) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'cloud_control_switch',
    set: isSet ? 1 : 0, // 0查询 1设置
  }
  if(isSet) {
    sendData.cloud_control = cloudSwitch
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

export function cloud_control_register (serial_code) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'cloud_control_authorization',
    serial_code: serial_code
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

// 电台管理相关函数

/**
 * 获取电台分组列表
 */
export function getRadioGroupList () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_radio_group_list'
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 根据分组ID获取电台列表
 * @param {number} groupId 分组ID
 */
export function getRadioListByGroupId (groupId) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'get_radio_list_by_group_id',
    groupId: Number(groupId)
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 播放电台
 * @param {string} radioId 电台ID
 */
export function playRadioSource (radioId) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'play_radio',
    radio_id: radioId,
    selected_id: store.state.selectIdTime
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 停止电台播放
 */
export function stopRadioSource () {
  const sendData = {
    uuid: store.state.uuid,
    command: 'stop_radio',
    selected_id: store.state.selectIdTime
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 添加电台信息
 * @param {Object} radioInfo 电台信息对象
 */
export function addRadioInfo (radioInfo) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'add_radio',
    radio_info: radioInfo
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 编辑电台信息
 * @param {Object} radioInfo 电台信息对象
 */
export function editRadioInfo (radioInfo) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'edit_radio',
    radio_info: radioInfo
  }
  store.commit('WEBSOCKET_SEND', sendData)
}

/**
 * 删除电台信息
 * @param {string} radioId 电台ID
 */
export function deleteRadioInfo (radioId) {
  const sendData = {
    uuid: store.state.uuid,
    command: 'delete_radio',
    radio_id: radioId
  }
  store.commit('WEBSOCKET_SEND', sendData)
}
