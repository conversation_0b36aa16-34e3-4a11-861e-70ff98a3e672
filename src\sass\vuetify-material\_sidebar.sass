.v-application .v-navigation-drawer .v-navigation-drawer__content .v-list-item .v-list-item__content .v-list-item__title.display-2
  font-size: 18px !important
  margin-top: 12px
  margin-bottom: 12px

.v-application .v-navigation-drawer .v-navigation-drawer__content .v-list .v-list-group .v-list-group__header .v-list-item__content .v-list-item__title
  font-size: 14px
  font-weight: 300

.v-application--is-ltr .v-list-item__avatar:first-child
  margin-right: 11px

.v-application .v-navigation-drawer .v-navigation-drawer__content .v-list-item__icon.v-list-group__header__append-icon .v-icon
  font-size: 19px

.v-application--is-ltr #core-navigation-drawer div.v-list-item__icon--text,
.v-application--is-ltr #core-navigation-drawer div.v-list-item__icon:first-child
  margin-left: 5px !important
  margin-right: 18px
  opacity: .8

.v-application--is-ltr .v-list-item__action:last-of-type:not(:only-child),
.v-application--is-ltr .v-list-item__avatar:last-of-type:not(:only-child),
.v-application--is-ltr .v-list-item__icon:last-of-type:not(:only-child)
  margin-right: 2px

.v-list--nav.v-list--dense .v-list-item:not(:last-child):not(:only-child),
.v-list--nav .v-list-item--dense:not(:last-child):not(:only-child),
.v-list--rounded.v-list--dense .v-list-item:not(:last-child):not(:only-child),
.v-list--rounded .v-list-item--dense:not(:last-child):not(:only-child)
  margin-bottom: 3px

.v-list-item .v-list-item__title, .v-list-item .v-list-item__subtitle
    line-height: 1.2
    font-weight: 300
    font-size: 14px

.v-list-group__items .v-list-item
    font-size: 13px
    margin-bottom: 0px !important
    .v-list-item__title
      font-size: 13px
    .v-list-item__icon
      margin-top: 14px

.v-list-group__items .v-list-group--sub-group .v-list-group__header .v-list-item__icon--text
    margin-top: 15px !important


.v-list-item__icon
  margin: 12px 0

.theme--dark.v-list-item--active:hover::before, .theme--dark.v-list-item--active::before
    opacity: 0

.v-navigation-drawer
  .v-list-item__content
    transition: all 0.3s linear 0s

.v-list--nav
  padding-left: 15px
  padding-right: 15px

.theme--dark.v-navigation-drawer .v-divider
  background-color: rgba(181, 181, 181, 0.2)
  border-color: rgba(181, 181, 181, 0.1)
  width: calc(100% - 30px)
  margin-left: 15px

.input-class .v-input__control
  height: 20px
