<template>
  <div>
    <!--通用successSnackBar-->
    <v-snackbar
      v-model="commonSuccessSnackBar"
      color="primary"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ successMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="primary" fab small class="ml-6" v-bind="attrs" @click="commonSuccessSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--通用errorSnackBar-->
    <v-snackbar
      v-model="commonErrorSnackBar"
      color="error"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="error" fab small class="ml-6" v-bind="attrs" @click="commonErrorSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <v-dialog v-model="registrationDialog" max-width="500px" transition>
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('system.registration.title') }}</span>
        </v-card-title>

        <v-card-text>
          <v-text-field
            :value="getRegisterStatusName(true)"
            :label="$t('system.registration.status')"
            class="pt-6"
            hide-details
            required
            disabled
            style="font-weight: 600"
          />
          <v-text-field
            :value="machineCode"
            :label="$t('system.registration.machineCode')"
            class="pt-6"
            hide-details
            required
            disabled
            style="font-weight: 600"
          />
          <v-text-field
            v-model="registrationCode"
            :append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
            :type="showPassword ? 'text' : 'password'"
            :placeholder="$t('system.registration.codePlaceholder')"
            @click:append="showPassword = !showPassword"
            :label="$t('system.registration.code')"
            class="pt-6"
            hide-details
            required
            style="font-weight: 500"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="registrationDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="systemRegister"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-container
      id="system"
      fluid
      tag="section"
    >
      <v-row dense>
        <!--选项卡切换-->
        <v-col cols="12">
          <v-tabs
            v-model="tab"
            icons-and-text
            style="background-color: transparent"
            grow
          >
            <v-tabs-slider />

            <v-tab
              v-for="i in tabList"
              :key="i.id"
              :href="`#tab-${i.id}`"
              class="text-lg-h4"
              @click="setCurrentTab(i)"
            >
              {{ i.name }}
              <v-icon size="30">{{ i.icon }}</v-icon>
            </v-tab>
          </v-tabs>
        </v-col>
      </v-row>
      <v-tabs-items v-model="tab">
        <!-- 系统信息总控制-->
        <v-tab-item :value="'tab-' + 1">
          <v-row dense>
            <v-col
              cols="12"
              sm="12"
            >
              <v-card :height="getSystemInfoTabHeight()">
                <v-toolbar
                  color="primary"
                  dark
                >
                  <v-toolbar-title class="text-xl-h3">{{ $t('system.info.title') }}</v-toolbar-title>
                </v-toolbar>

                <v-simple-table>
                  <template v-slot:default>
                    <tbody>
                      <tr v-for="(item, index) in systemData" :key="item.name">
                        <td>{{ item.name }}</td>
                        <td v-show="index !== 2 && index !== 6">{{ item.data }}</td>
                        <td v-show="index === 2">
                          <v-chip :color="getColorOfSystemRegistration(type)" @click="clickRegisterStatus(type)" dark>{{ item.data }}</v-chip>
                          <v-chip
                            v-show="type === 1 || type === 7"
                            @click="registrationDialog = true"
                            color="primary"
                            class="ml-3"
                            dark
                          >
                            <v-icon left>mdi-account-plus</v-icon>
                            {{ $t('system.registration.register') }}
                          </v-chip>
                        </td>
                         <td v-show="index === 6">
                          <v-chip
                            v-show="true"
                            color="white"
                          >
                            <v-switch
                              v-model="$store.state.isEnableExaminationMode"
                              :label="$t('system.info.enable')"
                              :title="$t('system.info.examModeTooltip')"
                              color="primary"
                              @change="changeExaminationModeSwitch"
                            />
                          </v-chip>
                        </td>
                      </tr>
                    </tbody>
                  </template>
                </v-simple-table>
              </v-card>
            </v-col>
          </v-row>
          <v-row>
            <v-col
              cols="6"
            >
              <v-card height="320px">
                <v-toolbar
                  color="primary"
                  dark
                >
                  <v-toolbar-title class="text-xl-h3">{{ $t('system.network.title') }}</v-toolbar-title>
                </v-toolbar>
                <v-simple-table>
                  <template v-slot:default>
                    <tbody>
                      <tr v-for="item in networkData" :key="item.name">
                        <td>{{ item.name }}</td>
                        <td>{{ item.data }}</td>
                      </tr>
                    </tbody>
                  </template>
                </v-simple-table>
              </v-card>
            </v-col>
            <v-col
              cols="6"
            >
              <v-card height="320px">
                <v-toolbar
                  color="primary"
                  dark
                >
                  <v-toolbar-title class="text-xl-h3">{{ $t('system.storage.title') }}</v-toolbar-title>
                </v-toolbar>
                <v-simple-table>
                  <template v-slot:default>
                    <tbody>
                      <tr v-for="item in storageData" :key="item.name">
                        <td>{{ item.name }}</td>
                        <td>{{ item.data }}</td>
                      </tr>
                    </tbody>
                  </template>
                </v-simple-table>
              </v-card>
            </v-col>
          </v-row>
        </v-tab-item>
        <!--时间设置总控制-->
        <v-tab-item v-if="tabList.length > 1" :value="'tab-' + 2">
          <v-card height="600px">
            <v-toolbar
              color="primary"
              dark
            >
              <v-toolbar-title class="text-xl-h3">{{ $t('system.time.title') }}</v-toolbar-title>
            </v-toolbar>
            <v-card-text class="black--text text-h3 mt-5">
              {{ $t('system.time.instruction') }}
              <v-btn
                color="primary"
                class="ml-5"
                @click="updateSystemTime"
              >
                {{ $t('system.time.set') }}
              </v-btn>
            </v-card-text>
            <v-row class="mt-n5">
              <v-col
                cols="2"
                class="ml-2"
              >
                <v-date-picker scrollable first-day-of-week="1" locale="zh-cn" v-model="date" full-width/>
              </v-col>
              <v-col cols="3">
                <v-time-picker
                  v-model="time"
                  use-seconds
                  flat
                  format="24hr"
                  scrollable
                />
              </v-col>
            </v-row>
          </v-card>
        </v-tab-item>
        <!--网络设置总控制-->
        <v-tab-item v-if="tabList.length > 2" :value="'tab-' + 3">
          <v-card height="400px">
            <v-toolbar
              color="primary"
              dark
            >
              <v-toolbar-title class="text-xl-h3">{{ $t('system.network.settings') }}</v-toolbar-title>
            </v-toolbar>
            <v-card-text class="black--text text-h3 mt-5">
              {{ $t('system.network.configInstruction') }}
              <v-btn
                color="primary"
                class="ml-5"
                @click="updateNetwork"
              >
                {{ $t('system.network.set') }}
              </v-btn>
            </v-card-text>
            <v-card-text class="mt-n3">
              <v-text-field
                v-model="newIpAddress"
                :label="$t('system.network.ipAddress')"
                hide-details
              />
              <v-text-field
                v-model="subnetMark"
                :label="$t('system.network.subnetMask')"
                class="pt-6"
                hide-details
              />
              <v-text-field
                v-model="gateway"
                :label="$t('system.network.gateway')"
                class="pt-6"
                hide-details
              />
              <v-text-field
                v-model="dnsServer"
                :label="$t('system.network.dnsServer')"
                class="pt-6"
                hide-details
              />
            </v-card-text>
          </v-card>
        </v-tab-item>
      </v-tabs-items>
    </v-container>
  </div>
</template>

<script>
import {mapGetters, mapState} from 'vuex'
  import { customerVersion } from '@/plugins/websocket'
  export default {
    name: 'System',
    data: () => ({
      tab: null,
      date: null,
      time: null,
      newIpAddress: null,
      subnetMark: null,
      gateway: null,
      dnsServer: null,
      currentTab: '',
      systemData: [],
      networkData: [],
      storageData: [],
      successMessages: '',
      commonSuccessSnackBar: false,
      errorMessages: '',
      commonErrorSnackBar: false,
      snackbarTimeout: 1500,
      registrationDialog: false, // 系统注册对话框
      registrationCode: null, //注册码
      showPassword: false,
    }),
    computed: {
      ...mapState(['timestamp', 'systemBootTime', 'systemNetwork', 'systemStorage', 'ip', 'mac', 'type', 'version',
        'setSystemDateTimeResult', 'setSystemNetworkResult', 'machineCode', 'serverRegisterResult', 'expiration_date',
        'errorId', 'errorWsMessage', 'commandName']),
      ...mapGetters(['isWindowsServer', 'isAdmin']),
      tabList() {
        const tabList = [{ id: 1, name: this.$t('system.info.title'), icon: 'mdi-information' }];
        if (!this.isWindowsServer && this.isAdmin) {
          tabList.push(
            { id: 2, name: this.$t('system.time.title'), icon: 'mdi-alarm' },
            { id: 3, name: this.$t('system.network.settings'), icon: 'mdi-network' }
          )
        }
        return tabList
      },
    },
    watch: {
      // 监听语言变化
      '$i18n.locale'() {
        this.initializeTranslations()
        this.prepareSystemData()
        this.prepareNetworkData()
        this.prepareStorageData()
      },
      // 统一错误处理
      errorId () {
        if (this.$route.fullPath !== '/dashboard/system') {
          return;
        }
        if (this.$store.state.errorId !== null) {
          this.errorMessages = this.$store.state.errorWsMessage
          this.commonErrorSnackBar = true
        }
      },
      currentTab: function () {
        if (this.currentTab === this.$t('system.time.title')) {
          this.date = new Date(new Date(this.timestamp).getFullYear(), new Date(this.timestamp).getMonth(), new Date(this.timestamp).getDate() + 1).toISOString().substr(0, 10)
          this.time = new Date(this.timestamp).getHours() + ':' + new Date(this.timestamp).getMinutes() + ':' + new Date(this.timestamp).getSeconds()
        } else if (this.currentTab === this.$t('system.network.settings')) {
          this.newIpAddress = this.systemNetwork.ipAddress
          this.subnetMark = this.systemNetwork.subnetMask
          this.gateway = this.systemNetwork.gateway
          this.dnsServer = this.systemNetwork.dnsServer
        }
      },
      timestamp: function () {
        if (this.timestamp !== null) {
          this.systemData[3].data = this.timestamp
          this.systemData[5].data = this.getRunningTime(this.systemBootTime, this.timestamp)
          // this.date = new Date(new Date(this.timestamp).getFullYear(), new Date(this.timestamp).getMonth(), new Date(this.timestamp).getDate() + 1).toISOString().substr(0, 10)
          // this.time = new Date(this.timestamp).getHours() + ':' + new Date(this.timestamp).getMinutes() + ':' + new Date(this.timestamp).getSeconds()
        }
      },
      setSystemDateTimeResult: function () {
        if (this.$store.state.setSystemDateTimeResult === 0) {
          this.successMessages = this.$t('system.time.success')
          this.commonSuccessSnackBar = true
          this.$store.commit('updateSetSystemDateTimeResult', null)
        }
      },
      setSystemNetworkResult: function () {
        if (this.$store.state.setSystemNetworkResult === 0) {
          this.successMessages = this.$t('system.network.configSuccess')
          this.commonSuccessSnackBar = true
          // 延时跳转到新的网页地址 todo check是否修改为vueRouter
          this.$store.commit('updateLogoutBySelf', true)
          setTimeout(() => {
            window.location.href = 'http://' + this.$store.state.systemNetwork.ipAddress + ':' + this.$cgiPort
          }, 5000)
          this.$store.commit('updateSetSystemNetworkResult', null)
        }
      },
      systemNetwork: function () {
        this.prepareNetworkData()
      },
      systemStorage: function () {
        this.prepareStorageData()
      },
      serverRegisterResult () {
        if (this.$route.fullPath !== '/dashboard/system') {
          return;
        }
        if (this.serverRegisterResult == null) {
          return
        }
        if (this.serverRegisterResult === 0) {
          this.successMessages = this.$t('system.registration.success')
          this.commonSuccessSnackBar = true
          this.registrationDialog = false
          this.$store.commit('updateLogoutBySelf', true)
          // this.systemData[2].data = '已注册'
          // // 刷新系统注册状态
          // this.$store.commit('updateSystemRegistrationType', 2)
          // 回退到登录界面
          setTimeout(() => {
            this.$router.push('/')
          }, 1000)
        } else {
          this.errorMessages = this.$t('system.registration.failed')
          this.commonErrorSnackBar = true
        }
        this.$store.commit('updateServerRegisterResult')
      },
    },
    mounted () {
      // 初始化翻译文本
      this.initializeTranslations()
      this.RefreshSystemData()
      this.prepareSystemData()
      this.prepareNetworkData()
      this.prepareStorageData()
    },
    methods: {
      initializeTranslations() {
        // 初始化当前选项卡
        this.currentTab = this.$t('system.info.title')

        // 初始化系统数据
        this.systemData = [
          { name: this.$t('system.info.version'), data: '' },
          { name: this.$t('system.info.webAddress'), data: '' },
          { name: this.$t('system.info.authorization'), data: '' },
          { name: this.$t('system.info.currentTime'), data: '' },
          { name: this.$t('system.info.startTime'), data: '' },
          { name: this.$t('system.info.runTime'), data: '' },
        ]

        // 初始化网络数据
        this.networkData = [
          { name: this.$t('system.network.macAddress'), data: '' },
          { name: this.$t('system.network.ipAddress'), data: '' },
          { name: this.$t('system.network.subnetMask'), data: '' },
          { name: this.$t('system.network.gateway'), data: '' },
          { name: this.$t('system.network.dnsServer'), data: '' },
        ]

        // 初始化存储数据
        this.storageData = [
          { name: this.$t('system.storage.totalSpace'), data: '' },
          { name: this.$t('system.storage.availableSpace'), data: '' },
          { name: this.$t('system.storage.totalMemory'), data: '' },
          { name: this.$t('system.storage.availableMemory'), data: '' },
        ]

        // 初始化消息
        this.successMessages = this.$t('common.success')
        this.errorMessages = this.$t('common.error')
      },
      RefreshSystemData: function () {
        if(this.isAdmin && (customerVersion === 'C3A0')) {
          this.systemData.push({
            name: this.$t('system.info.examMode'),
            data: '',
          })
        }
      },
      getSystemInfoTabHeight: function () {
        if(this.isAdmin && (customerVersion === 'C3A0')) {
          return '400px'
        }
        else {
          return '350px'
        }
      },
      getRegisterStatusName: function (isShowExpirationTime) {
        let statusName='';
        if (this.type === 1) { // 未注册（需要显示注册按钮）
          statusName = this.$t('system.registration.unregistered')
        } else if (this.type === 2) { // 常规版已注册
          if(isShowExpirationTime && this.expiration_date != null) {
            statusName = this.$t('system.registration.registeredWithExpiry', { date: this.expiration_date })
          }
          else {
            statusName = this.$t('system.registration.registered')
          }
        } else if (this.type === 4) { // 3-保留 4-虚拟化版本（云服务器都属于这种）
          statusName = this.$t('system.registration.registered')
        }else if (this.type === 5) { // 加密狗
          statusName = this.$t('system.registration.dongleDetected')
        }else if (this.type === 6) { // 加密狗
          statusName = this.$t('system.registration.dongleExpired')
        }else if (this.type === 7) { // 加密狗
          statusName = this.$t('system.registration.codeExpired')
        }
        return statusName
      },
      prepareSystemData: function () {
        // 确保 systemData 数组已初始化
        if (this.systemData.length === 0) {
          return
        }
        // 从配置文件中读取web版本
        const webVersion = process.env.VUE_APP_WEB_VERSION || '1.0.0'
        this.systemData[0].data = this.version + ' / ' + webVersion + customerVersion
        this.systemData[1].data = 'http://' + this.ip + ':' + this.$cgiPort
        this.systemData[2].data = this.getRegisterStatusName(false)
        this.systemData[3].data = this.timestamp
        this.systemData[4].data = this.systemBootTime
        this.systemData[5].data = this.getRunningTime(this.systemBootTime, this.timestamp)
      },
      prepareNetworkData: function () {
        // 确保 networkData 数组已初始化
        if (this.networkData.length === 0 || this.systemNetwork === null) {
          return
        }
        this.networkData[0].data = this.mac.toUpperCase() // mac在user_login的应答中
        this.networkData[1].data = this.systemNetwork.ipAddress
        this.networkData[2].data = this.systemNetwork.subnetMask
        this.networkData[3].data = this.systemNetwork.gateway
        this.networkData[4].data = this.systemNetwork.dnsServer
      },
      prepareStorageData: function () {
        // 确保 storageData 数组已初始化
        if (this.storageData.length === 0 || this.systemStorage === null) {
          return
        }
        this.storageData[0].data = (this.systemStorage.hardDiskTotal / 1024).toFixed(1) + ' GB'
        this.storageData[1].data = (this.systemStorage.hardDiskRemain / 1024).toFixed(1) + ' GB'
        this.storageData[2].data = this.systemStorage.memoryTotal + ' MB'
        this.storageData[3].data = this.systemStorage.momeoryRemain + ' MB'
      },
      // 更改当前选项卡
      setCurrentTab: function (tab) {
        this.currentTab = tab.name
      },
      // 计算运行时间
      getRunningTime: function (startTime, endTime) {
        const start = new Date(startTime)
        const end = new Date(endTime)
        let runTime = parseInt((end.getTime() - start.getTime()) / 1000)
        let year = Math.floor(runTime / 86400 / 365)
        runTime = runTime % (86400 * 365)
        let month = Math.floor(runTime / 86400 / 30)
        runTime = runTime % (86400 * 30)
        let day = Math.floor(runTime / 86400)
        runTime = runTime % 86400
        let hour = Math.floor(runTime / 3600)
        runTime = runTime % 3600
        let minute = Math.floor(runTime / 60)
        runTime = runTime % 60
        let second = runTime
        // 20210613 增加年和月的显示（当年或月大于1时显示）
        let relativeTime = day + this.$t('timeTips.days') + hour + this.$t('timeTips.hours') + minute + this.$t('timeTips.minutes') + second + this.$t('timeTips.seconds')
        if (year > 0) {
          return year + this.$t('timeTips.years') + month + this.$t('timeTips.months') + relativeTime;
        } else if (month > 0) {
          return month + this.$t('timeTips.months') + relativeTime
        } else {
          return relativeTime;
        }
      },
      // 更新系统时间
      updateSystemTime: function () {
        this.$ws.setSystemDateTime(true, this.date + ' ' + this.time)
      },
      // 更新网络设置
      updateNetwork: function () {
        // 校验格式
        if (this.newIpAddress === '' || this.subnetMark === '' || this.gateway === '' || this.dnsServer === '') {
          this.errorMessages = this.$t('system.network.fieldsRequired')
          this.commonErrorSnackBar = true
          return
        }
        if (!this.isIpValid(this.newIpAddress + '')) {
          this.errorMessages = this.$t('system.network.invalidIp')
          this.commonErrorSnackBar = true
          return
        }
        this.$ws.setSystemNetwork(true, this.newIpAddress, this.subnetMark, this.gateway, this.dnsServer)
      },
      // 校验是否为ip格式
      isIpValid: function (ipStr) {
        const re = /^(\d+)\.(\d+)\.(\d+)\.(\d+)$/g // 匹配IP地址的正则表达式
        if (re.test(ipStr)) {
          if (RegExp.$1 < 256 && RegExp.$2 < 256 && RegExp.$3 < 256 && RegExp.$4 < 256) return true
        }
        return false
      },
      // 设置不同注册状态状态的颜色显示
      getColorOfSystemRegistration (type) {
        if (type === 2 || type === 4 || type === 5) {
          return 'primary'
        }
        else {
          return 'grey'
        }

      },
      // 点击注册状态（当注册状态为已注册的时候，也可以点击，用于切换注册码限制时间）
      clickRegisterStatus (type) {
        if (type === 2) {
          this.registrationDialog = true
        }
      },
      // 注册
      systemRegister () {
        if (!this.registrationCode || this.registrationCode === '') {
          this.errorMessages = this.$t('system.registration.codeRequired')
          this.commonErrorSnackBar = true
          return
        }
        this.$ws.systemRegister(this.registrationCode);
      },
      changeExaminationModeSwitch (val) {
        //console.log("switch="+val)
        this.$ws.switchExaminationMode(val)
      }
    },
  }
</script>

<style scoped>
/deep/ .v-date-picker-title__year {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}
/deep/ .v-date-picker-title__date {
  font-size: 42px;
  text-align: left;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  padding-bottom: 8px;
  margin-bottom: -8px;
}
/*修改文本框label的字体大小*/
.v-radio >>> .v-label{
  font-size: 1.35em !important;
  font-weight: bold;
}
</style>
