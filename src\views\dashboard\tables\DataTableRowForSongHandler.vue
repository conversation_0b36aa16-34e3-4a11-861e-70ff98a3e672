<!-- v-data-table排序rowHandler -->
<template>
  <tr :class="itemClass" @click="onClick">
    <!--  默认表格内容align为text-center  -->
    <td v-for="(header, index) in headers" :key="index" class="text-center">
      <slot :item="item" :name="columnName(header)">
        <div>
            {{ getNonSlotValue(item, header) }}
        </div>
      </slot>
    </td>
  </tr>
</template>

<script>
import {mapState} from 'vuex'

export default {
  name: "DataTableRowForSongHandler",
  components: {},
  props: {
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    headers: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      itemClass: '',
    };
  },
  computed: {
    ...mapState(['songMd5SelectedForSort']),
  },
  methods: {
    columnName(header) {
      return `item.${header.value}`;
    },
    getNonSlotValue(item, header) {
      const val = item[header.value];

      if (val) {
        return val;
      }

      return "";
    },
    onClick() {
      if (this.songMd5SelectedForSort == null || this.songMd5SelectedForSort !== this.item.song_md5) {
        this.$store.commit('updateSongMd5SelectedForSort', this.item.song_md5);
        this.itemClass = 'selected-zone';
      } else {
        this.$store.commit('updateSongMd5SelectedForSort', null);
        this.itemClass = !this.item.alive ? 'offlineZone' : 'onlineZone';
      }
    }
  },
  watch: {
    songMd5SelectedForSort() {
      if (this.item.song_md5 === this.songMd5SelectedForSort) {
        return
      }
      this.itemClass = !this.item.alive ? 'offlineZone' : 'onlineZone';
    }
  },
  mounted() {
    this.itemClass = !this.item.alive ? 'offlineZone' : 'onlineZone';
  }
};
</script>

<style scoped>
.v-table tr:hover:not(.v-table__expanded__content) {
  background: red !important;
}
.selected-zone {
  background: #c2c9f3 !important;
}
</style>
