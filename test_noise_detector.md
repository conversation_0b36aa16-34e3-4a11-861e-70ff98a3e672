# 噪声自适应终端设置界面测试文档

## 功能概述

噪声自适应终端设置界面已完成开发，包含以下主要功能：

### 1. 启用/关闭开关
- ✅ 提供噪声自适应功能的启用/关闭开关
- ✅ 当关闭时，隐藏相关设置选项

### 2. 噪声检测器通道显示
- ✅ 支持最大8路噪声检测器通道
- ✅ 有效值范围：30dB~130dB
- ✅ 0值表示该通道无效
- ✅ 有效通道显示绿色，无效通道显示灰色
- ✅ 显示具体的噪声值

### 3. 平均噪声值显示
- ✅ 计算并显示所有有效噪声通道的平均值
- ✅ 只有当存在有效通道时才显示

### 4. 噪声/音量设置
- ✅ 8段式噪声/音量设置
- ✅ 固定噪声值：30dB, 40dB, 50dB, 60dB, 70dB, 80dB, 90dB, 100dB（只读显示）
- ✅ 可调音量值：从editNoiseDetectorVolumeArray数组获取（1-100%）
- ✅ 音量值验证：必须在1-100之间，且下一段不能比上一段低
- ✅ 简化的界面设计，固定噪声值用卡片样式突出显示

### 5. 分区绑定功能
- ✅ 可选择绑定的分区
- ✅ 使用穿梭框组件进行分区选择
- ✅ 显示已选择分区数量
- ✅ 优化对话框高度，列表铺满整个对话框

## 技术实现

### 组件结构
- 主对话框：`editNoiseDetectorDialog`
- 分区选择对话框：`noiseDetectorPartitionSelectDialog`
- 曲线绘制：Canvas 2D API

### 数据结构
```javascript
// 噪声自适应相关数据
editNoiseDetectorSwitch: false,           // 启用开关
editNoiseDetectorChannelArray: [],        // 通道噪声值数组
editNoiseDetectorVolumeArray: [],         // 音量设置数组（8个值：对应8段音量）
noiseDetectorPartitionSelectTransfer: [], // 选中的分区
```

### 关键方法
- `getChannelColor(index)` - 获取通道颜色
- `isChannelValid(index)` - 判断通道是否有效
- `getAverageNoiseLevel()` - 计算平均噪声值
- `getFixedNoiseValue(index)` - 获取固定的噪声值（30+index*10）
- `validateVolumeSettings()` - 验证音量设置的有效性
- `saveNoiseDetectorSettings()` - 保存设置（包含验证）

### WebSocket 通信
- 查询：`queryNoiseDetectorParm(deviceMac)`
- 设置：`setNoiseDetectorParm(isSet, deviceMac, enable_control, segment_volume, zone_macs)`

## 国际化支持

已添加以下翻译键：
- `noiseAdaptiveSettings` - 噪声自适应终端设置
- `enableNoiseAdaptive` - 启用噪声自适应
- `noiseDetectorChannels` - 噪声检测器通道
- `averageNoiseLevel` - 平均噪声值
- `noiseVolumeSettings` - 噪声/音量设置
- `segment` - 段
- `noiseLevel` - 噪声值
- `volumeLevel` - 音量值
- `bindPartitions` - 绑定分区
- `fixedValue` - 固定值
- `validation.*` - 验证相关的错误消息

支持语言：简体中文、英文、繁体中文

## 样式设计

- 响应式布局，适配不同屏幕尺寸
- 通道状态卡片带有悬停动画效果
- 简化的分段控制区域，固定噪声值只读显示
- 分段控制区域使用卡片布局，便于操作

## 测试建议

1. **功能测试**
   - 测试启用/关闭开关的响应
   - 验证通道有效性判断逻辑
   - 测试平均值计算的准确性
   - 验证固定噪声值的正确显示（30-100dB）

2. **界面测试**
   - 检查不同语言下的界面显示
   - 验证响应式布局在不同屏幕尺寸下的表现
   - 测试动画效果和用户交互

3. **数据测试**
   - 测试边界值（30dB、130dB）
   - 验证无效值（0）的处理
   - 测试分区选择功能

4. **集成测试**
   - 验证与后端WebSocket通信
   - 测试数据保存和恢复功能
   - 检查与其他组件的兼容性

## 已知限制

1. 噪声值固定为30-100dB（每10dB一段），不可调节
2. 分区选择使用Element UI的transfer组件，需要确保Element UI已正确引入
3. 初始化时会设置默认的音量值，实际使用时应从服务器获取

## 问题修复记录

### ✅ 错误消息显示修复
- **问题**: `this.$message.error()` 方法未定义，导致 `TypeError: Cannot read properties of undefined (reading 'error')`
- **原因**: 项目使用 Vuetify 的 `v-snackbar` 组件显示消息，而不是 Element UI 的 `$message`
- **解决方案**: 改用项目标准的错误显示方式：
  ```javascript
  // 修复前
  this.$message.error(validationResult.message)

  // 修复后
  this.errorMessages = validationResult.message
  this.commonErrorSnackBar = true
  ```

### ✅ Element UI 国际化配置
- **问题**: el-transfer 组件的"无数据"文本始终显示中文，无法国际化
- **原因**: Element UI 组件需要单独配置国际化，不会自动跟随 Vue i18n
- **解决方案**: 在 main.js 中配置 Element UI 国际化：
  ```javascript
  // 导入 Element UI 语言包
  import locale from 'element-ui/lib/locale'
  import langZh from 'element-ui/lib/locale/lang/zh-CN'
  import langEn from 'element-ui/lib/locale/lang/en'
  import langZhTW from 'element-ui/lib/locale/lang/zh-TW'

  // 创建全局语言切换方法
  Vue.prototype.$setElementLocale = function(langCode) {
    if (langCode === 'zh-TW') {
      locale.use(langZhTW)
    } else if (langCode === 'en') {
      locale.use(langEn)
    } else {
      locale.use(langZh)
    }
  }
  ```
- **效果**: el-transfer 组件的"无数据"文本现在会根据当前语言显示：
  - 简体中文: "无数据"
  - English: "No data"
  - 繁體中文: "無數據"

## 后续优化建议

1. 考虑添加噪声值的可调节功能（如果需要）
2. 添加音量值的批量设置功能
3. 添加数据验证和错误处理
4. 考虑添加预设音量模板
5. 优化移动端显示效果
