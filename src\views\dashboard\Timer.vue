<template>
  <div v-if="render">
    <!--snackBar-->
    <!--{{ $t('timer.comments.commonSuccessSnackBar') }}-->
    <v-snackbar
      v-model="commonSuccessSnackBar"
      color="primary"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ successMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="primary" fab small class="ml-6" v-bind="attrs" @click="commonSuccessSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--{{ $t('timer.comments.commonErrorSnackBar') }}-->
    <v-snackbar
      v-model="commonErrorSnackBar"
      color="error"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="error" fab small class="ml-6" v-bind="attrs" @click="commonErrorSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--{{ $t('timer.comments.dialogs') }}-->
    <v-dialog
      v-model="deleteSchemaDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('timer.deleteSchema') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('timer.aboutToDeleteSchema') }}</span>
          <span class="font-weight-black text-decoration-underline" style="margin-left: 10px; margin-right: 2px">
            {{ schemaSelected.length === 0 ? '' : schemaSelected[0].time_scheme_name }}
          </span>
          <span>,</span>
          <span style="margin-left: 5px">{{ $t('timer.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteSchemaDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="deleteSchema"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="setActiveSchemaDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('timer.setActiveSchema') }}
        </v-card-title>
        <v-card-text class="text-center">
          <p class="red--text text--darken-4">{{ $t('timer.onlyOneSchemaActive') }}</p>
          <span>{{ $t('timer.aboutToSetSchemaActive') }}</span>
          <span class="font-weight-black text-decoration-underline">{{ schemaSelected.length === 0 ? '' : schemaSelected[0].time_scheme_name }}</span>
          <span>,{{ $t('timer.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="setActiveSchemaDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="setActiveSchema"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="copySchemaDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('timer.copySchema') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('timer.aboutToCopySchema') }}</span>
          <span class="font-weight-black text-decoration-underline" style="margin-left: 10px; margin-right: 5px">
            {{ schemaSelected.length === 0 ? '' : schemaSelected[0].time_scheme_name }}
          </span>
          <span>,</span>
          <span style="margin-left: 5px">{{ $t('timer.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="copySchemaDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="copySchema"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="deleteTimerDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('timer.deleteTimer') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('timer.aboutToDeleteTimer') }}</span>
          <span class="font-weight-black text-decoration-underline">{{ timerSelected.length === 0 ? '' : timerSelected[0].name }}</span>
          <span>,{{ $t('timer.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteTimerDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="deleteTimer"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="deleteTimerActionInTableDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('timer.deleteTimer') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('timer.aboutToDeleteTimer') }}</span>
          <span class="font-weight-black text-decoration-underline">{{ deletedTimerActionInTable ? deletedTimerActionInTable.name : '' }}</span>
          <span>,{{ $t('timer.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteTimerActionInTableDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="deleteTimerActionInTable"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--{{ $t('timer.comments.copyTimerPoint') }}-->
    <v-dialog
      v-model="copyTimerDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('timer.copyTimer') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('timer.aboutToCopyTimer') }}</span>
          <span class="font-weight-black text-decoration-underline" style="margin-left: 10px; margin-right: 5px">
            {{ timerSelected.length === 0 ? '' : timerSelected[0].name }}
          </span>
          <span>,</span>
          <span style="margin-left: 5px">{{ $t('timer.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="copyTimerDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="copyTimer"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--{{ $t('timer.comments.timerPointSorting') }}-->
    <v-dialog
      v-model="sortTimerDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('timer.sortTimer') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('timer.aboutToSortTimer') }}</span>
          <span>,</span>
          <span style="margin-left: 5px">{{ $t('timer.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="sortTimerDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="sortTimer"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- {{ $t('timer.comments.createEditTimerDialog') }} -->
    <v-dialog
      v-model="timerDialog"
      max-width="85%"
      transition
      persistent
    >
      <v-card>
        <v-row
          justify="center"
          align="center"
          dense
          style="background-color: white"
        >
          <v-col
            cols="12"
          >
            <v-card
              max-height="100px"
            >
              <v-toolbar
                color="primary"
                dark
              >
                <v-app-bar-nav-icon class="mt-1"/>

                <v-toolbar-title v-show="isCreateTimer">{{ $t('timer.createTimer') }}</v-toolbar-title>
                <v-toolbar-title v-show="!isCreateTimer">{{ $t('timer.editTimer') }}</v-toolbar-title>
              </v-toolbar>
            </v-card>
          </v-col>
          <v-col cols="12">
            <v-card height="80px">
              <v-row :align="timerDeviceType === 0 ? undefined : 'center'" :justify="timerDeviceType === 0 ? undefined : 'center'">
                <v-col cols="12" lg="3" class="mx-5">
                  <v-text-field
                    v-model="timerName"
                    :label="$t('timer.timerName')"
                    :placeholder="$t('timer.timerNamePlaceholder')"
                    hide-details
                  />
                </v-col>
                <v-col cols="12" lg="2">
                  <v-switch v-model="timerValid" :label="$t('timer.effective')" />
                </v-col>
                <v-col cols="12" lg="2" v-show="timerDeviceType === 0">
                  <v-switch v-model="isVolFollowDevice" :label="$t('timer.volumeFollowDevice')" />
                </v-col>
                <v-col v-show="timerDeviceType === 0 && !isVolFollowDevice" cols="12" lg="3">
                  <v-slider
                    class="mt-5"
                    :value="timerVolume"
                    prepend-icon="mdi-volume-high"
                    thumb-label="always"
                    thumb-size="28"
                    @change.passive="v => timerVolume = v"
                  />
                </v-col>
              </v-row>
            </v-card>
          </v-col>
          <v-col
            cols="12"
            lg="2"
          >
            <v-card :height="get_vcard_height">
              <v-toolbar
                color="primary"
                dark
              >
                <v-toolbar-title>{{ $t('timer.dateSelection') }}</v-toolbar-title>
              </v-toolbar>
              <!--<v-card-title style="padding-top: 5px;">{{ $t('timer.steps.dateSelection') }}</v-card-title>-->
              <div class="ml-4">
                <v-switch
                  v-model="daySwitch"
                  :label="$t('timer.weeklyLoop')"
                />
                <div v-if="daySwitch" class="ml-3">
                  <v-checkbox v-for="n in 7" :key="n" v-model="daySelected" :label="getDay(n)" :value="n" class="ma-0 pa-0 pt-2 input-class" />
                </div>
                <v-switch
                  v-model="dateSwitch"
                  :label="$t('timer.specifyDate')"
                />
                <div v-if="dateSwitch">
                  <v-menu
                    ref="menu"
                    v-model="menu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="date"
                        :label="$t('timer.startDate')"
                        prepend-icon="mdi-calendar-month-outline"
                        readonly
                        v-bind="attrs"
                        hide-details
                        v-on="on"
                      />
                    </template>
                    <v-date-picker v-model="date" scrollable :min="getMinDate" :max="getMaxDate" @input="menu = false" first-day-of-week="1" locale="zh-cn" />
                  </v-menu>
                  <v-menu
                    ref="menu2"
                    v-model="menu2"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="date2"
                        :label="$t('timer.endDate')"
                        prepend-icon="mdi-calendar-month-outline"
                        readonly
                        v-bind="attrs"
                        hide-details
                        v-on="on"
                      />
                    </template>
                    <v-date-picker v-model="date2" scrollable :min="getMinDate" :max="getMaxDate" @input="menu2 = false" first-day-of-week="1" locale="zh-cn" />
                  </v-menu>
                </div>
              </div>
            </v-card>
          </v-col>

          <v-col
            cols="12"
            lg="2"
          >
            <v-card :height="get_vcard_height">
              <v-toolbar
                color="primary"
                dark
              >
                <v-toolbar-title>{{ $t('timer.timeSelection') }}</v-toolbar-title>
              </v-toolbar>
              <!--<v-card-title style="padding-top: 5px">{{ $t('timer.steps.timeSelection') }}</v-card-title>-->
              <div class="ml-4 pt-4">
                <v-menu
                  ref="timeMenu1"
                  v-model="timeMenu1"
                  :close-on-content-click="false"
                  :close-on-click="false"
                  :nudge-right="40"
                  :return-value.sync="time1"
                  transition="scale-transition"
                  offset-y
                  max-width="360px"
                  min-width="360px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="time1"
                      :label="$t('timer.startTime')"
                      prepend-icon="mdi-alarm-plus"
                      readonly
                      v-bind="attrs"
                      hide-details
                      v-on="on"
                    />
                  </template>
                  <v-time-picker
                    v-if="timeMenu1"
                    v-model="time1"
                    full-width
                    use-seconds
                    flat
                    format="24hr"
                    scrollable
                  >
                    <v-container
                      class="fill-height text-center"
                      fluid>
                      <v-row>
                        <v-col cols="12">
                          <v-chip class="text-h4"
                                  text-color="black"
                                  large>
                            <v-icon left large class="ma-2">
                              mdi-keyboard-outline
                            </v-icon>
                            <span class="ma-2">{{ $t('timer.manualInput') }}</span>
                            <input type="time" step="1" v-model="time1" >
                          </v-chip>
                        </v-col>
                        <v-col cols="12" class="mt-n2">
                          <v-btn color="primary" text @click="timeMenu1 = false">{{ $t('common.cancel') }}</v-btn>
                          <v-btn color="primary" text @click="$refs.timeMenu1.save(time1)">{{ $t('common.confirm') }}</v-btn>
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-time-picker>
                </v-menu>
                <v-menu
                  ref="timeMenu2"
                  v-model="timeMenu2"
                  :close-on-content-click="false"
                  :close-on-click="false"
                  :nudge-right="40"
                  :return-value.sync="time2"
                  transition="scale-transition"
                  offset-y
                  max-width="360px"
                  min-width="360px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="time2"
                      :label="$t('timer.endTime')"
                      prepend-icon="mdi-alarm-off"
                      readonly
                      v-bind="attrs"
                      hide-details
                      v-on="on"
                    />
                  </template>
                  <v-time-picker
                    v-if="timeMenu2"
                    v-model="time2"
                    full-width
                    use-seconds
                    flat
                    format="24hr"
                    scrollable
                  >
                    <v-container
                      class="fill-height text-center"
                      fluid>
                      <v-row>
                        <v-col cols="12">
                          <v-chip class="text-h4"
                                  text-color="black"
                                  large>
                            <v-icon left large class="ma-2">
                              mdi-keyboard-outline
                            </v-icon>
                            <span class="ma-2">{{ $t('timer.manualInput') }}</span>
                            <input type="time" step="1" v-model="time2" >
                          </v-chip>
                        </v-col>
                        <v-col cols="12" class="mt-n2">
                          <v-btn color="primary" text @click="timeMenu2 = false">{{ $t('common.cancel') }}</v-btn>
                          <v-btn color="primary" text @click="$refs.timeMenu2.save(time2)">{{ $t('common.confirm') }}</v-btn>
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-time-picker>
                </v-menu>
              </div>
              <v-btn v-if="timerSourceType === 0 && timerDeviceType === 0" color="primary"
                     class="mt-6 ml-6"
                     @click="fixEndTimeOfSequencePlaying"
                     v-show="selectedPlayMode === 3">
                {{ $t('timer.fixEndTime') }}
              </v-btn>
            </v-card>
          </v-col>

          <v-col
            cols="12"
            lg="2"
          >
            <v-card :height="get_vcard_height">
              <v-toolbar
                color="primary"
                dark
              >
                <v-toolbar-title>{{ $t('timer.deviceSelection') }}</v-toolbar-title>
              </v-toolbar>
              <!--<v-card-title style="padding-top: 5px">{{ $t('timer.steps.deviceSelection') }}</v-card-title>-->
              <!--<v-checkbox v-model="selectAllPartition" :label="`Checkbox 1: ${selectAllPartition.toString()}`" />-->

              <v-radio-group
                v-model="timerDeviceType"
                hide-details
                v-if="checkIfShowSequencePower"
                class="ml-5"
              >
                <v-row>
                  <v-col cols="12">
                    <v-radio
                      :label="$t('timer.decoder')"
                      color="primary darken-1"
                      :value="Number(0)"
                    />
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12">
                    <v-radio
                      :label="$t('timer.powerSequencer')"
                      color="primary darken-1"
                      :value="Number(1)"
                    />
                  </v-col>
                </v-row>
              </v-radio-group>

              <!--{{ $t('timer.comments.addDividerLine') }}-->
              <v-divider v-if="checkIfShowSequencePower" class="mt-4"/>

              <div v-show="timerDeviceType === 0">
                <v-list>
                  <v-card-title class="text-left pt-2">{{ $t('timer.selectedZoneCount') }} {{ partitionSelectTransfer.length }}</v-card-title>
                  <v-btn color="primary" class="mt-4 ml-6" @click="partitionSelectDialog = true">{{ $t('timer.zoneSelection') }}</v-btn>
                </v-list>
                <!--<v-checkbox v-model="selectAllGroups" :label="`Checkbox 2: ${selectAllGroups.toString()}`" />-->
                <v-list>
                  <v-card-title class="text-left pt-2">{{ $t('timer.selectedGroupCount') }} {{ groupSelectTransfer.length }}</v-card-title>
                  <v-btn color="primary" class="mt-4 ml-6" @click="groupSelectDialog = true">{{ $t('timer.groupSelection') }}</v-btn>
                </v-list>
              </div>

              <div v-show="timerDeviceType === 1">
                <v-list>
                  <v-card-title class="text-left pt-2">{{ $t('timer.selectedDeviceCount') }} {{ selectedPowerNumber }}</v-card-title>
                  <v-card-title class="text-left pt-2">{{ $t('timer.selectedChannelCount') }} {{ selectedPowerChannelNumber }}</v-card-title>
                  <v-btn color="primary" class="mt-4 ml-6" @click="powerSelectDialog = true">{{ $t('timer.powerSequencerSelection') }}</v-btn>
                </v-list>
              </div>

            </v-card>
          </v-col>

          <v-col
            cols="12"
            lg="6"
            v-show="timerDeviceType === 0"
          >
            <v-card :height="get_vcard_height">
              <v-toolbar
                color="primary"
                dark
              >
                <v-toolbar-title>
                  <v-row style="margin: 0px">
                    <span>{{ $t('timer.sourceSelection') }}</span>
                    <v-radio-group
                      v-model="timerSourceType"
                      row
                      hide-details
                    >
                      <v-radio
                        :label="$t('timer.songPlay')"
                        color="primary darken-3"
                        :value="Number(0)"
                        class="ml-6"
                      />
                      <v-radio
                        v-if="checkIfShowAudioCollector"
                        :label="$t('timer.audioCollection')"
                        color="primary darken-3"
                        :value="Number(1)"
                      />
                    </v-radio-group>
                  </v-row>
                </v-toolbar-title>
              </v-toolbar>
              <!--{{ $t('timer.comments.audioSourceSelection') }}-->
              <div v-if="timerSourceType === 1">
                <div v-show="audioCollectors.length === 0">
                  <v-card-subtitle class="pt-8 text-lg-h3">
                    <span style="color: red">{{ $t('timer.noAudioCollector') }}</span>
                  </v-card-subtitle>
                </div>
                <div class="ml-4 mt-3" v-show="audioCollectors.length > 0">
                  <v-row>
                    <v-col col="12">
                      <!--<v-card-title class="text-left text-lg-h3 pl-4 pt-1" style="font-weight: 400">
                        {{ getTooltipForSelectedAudioCollector }}
                      </v-card-title>-->
                      <v-toolbar
                        color="primary"
                        dark
                        height="40px"
                      >
                        <v-toolbar-title
                          class="ml-n4 text-lg-h4"
                          v-html="getTooltipForSelectedAudioCollector"
                        >
                        </v-toolbar-title>
                      </v-toolbar>
                      <!--<v-card-text>-->
                      <v-text-field
                        v-model="audioCollectorSearch"
                        prepend-icon="mdi-magnify"
                        flat
                        hide-details
                        clearable
                        clear-icon="mdi-close-circle-outline"
                        class="ml-2"
                      />
                      <v-treeview
                        :items="audioCollectorSearchTreeData"
                        item-key="id"
                        item-text="name"
                        item-children="children"
                        selected-color="primary"
                        selection-type="leaf"
                        hoverable
                        activatable
                        dense
                        :open-all="audioCollectorSearchTreeData.length === 1"
                        :filter="treeViewFilter"
                        :search="audioCollectorSearch"
                        :active.sync="audioCollectorActiveIds"
                        :open.sync="audioCollectorOpenIds"
                        open-on-click
                        class="mt-2"
                      >
                        <!-- {{ $t('timer.comments.setLabelSize') }} -->
                        <template v-slot:label="{ item }">
                          <span :class="getAudioCollectorClass(item.id)" style="font-weight: 350">{{ item.name }}</span>
                        </template>
                      </v-treeview>
                      <!--</v-card-text>-->
                    </v-col>
                  </v-row>
                </div>
              </div>
              <!--{{ $t('timer.comments.songSelection') }}-->
              <div v-else-if="timerSourceType === 0">
                <div v-show="playList.length === 0">
                  <v-card-subtitle class="pt-8 text-lg-h3">
                    <span style="color: red">{{ $t('timer.noPlaylist') }}</span>
                  </v-card-subtitle>
                </div>
                <div class="ml-4 mt-3" v-show="playList.length > 0">
                  <v-row>
                    <v-col cols="12">
                      <v-toolbar
                        color="primary"
                        dark
                        height="40px"
                      >
                        <v-toolbar-title
                          class="ml-n4 text-lg-h4"
                          v-html="getTitleOfTimerSelectedSongs"
                        >
                        </v-toolbar-title>
                      </v-toolbar>
                    </v-col>
                    <v-col cols="6" class="mt-n4">
                      <v-select
                        v-model="selectedPlayList"
                        :items="playList"
                        filled
                        item-value="list_id"
                        item-text="list_name"
                        name="selectedPlayMode"
                        :label="$t('timer.playlist')"
                        class="section-high-class mr-3"
                      />
                    </v-col>
                    <v-col cols="6" class="mt-n4">
                      <v-select
                        v-model="selectedPlayMode"
                        :items="playModes"
                        filled
                        item-value="id"
                        item-text="name"
                        name="selectedPlayMode"
                        :label="$t('timer.playMode')"
                        class="section-high-class mr-3"
                      />
                    </v-col>
                    <v-col cols="6" class="mt-n11">
                      <v-card min-height="100px" width="96%">
                        <div class="pt-2">
                          <v-chip
                            class="text-h4 ml-1 mr-3"
                            color="primary"
                            text-color="white"
                          >
                            {{ $t('timer.listSongs') }}
                          </v-chip>
                          <v-btn
                            color="primary darken-1"
                            text
                            @click="addSingleSongs"
                            :disabled="singleReadySongIndex == null || singleReadySongIndex === 0"
                            max-width="100px"
                          >
                            {{ $t('timer.addSingle') }}
                          </v-btn>
                          <v-btn
                            color="primary darken-1"
                            text
                            @click="addAllLeftSongs"
                            max-width="100px"
                          >
                            {{ $t('timer.addAll') }}
                          </v-btn>
                        </div>
                        <v-list :max-height="get_vlist_max_height">
                          <draggable
                            tag="v-list-item-group"
                            :list="getSelectedPlayListSongs"
                            :group="{ name: 'song', pull: 'clone', put: false }"
                          >
                            <v-list-item
                              v-for="(song,i) in getSelectedPlayListSongs"
                              :key="i"
                              dense
                              @dblclick="dbClickLeftSongList(song)"
                              @change="changeReadySelect(i)"
                              :disabled="!song.alive"
                            >
                              <v-tooltip
                                top
                                open-on-hover
                                open-delay="1000"
                                nudge-left="10"
                              >
                                <template v-slot:activator="{ on, attrs }">
                                  <v-list-item-content v-bind="attrs" v-on="on">
                                    <v-list-item-title
                                      :style="songStyleInPlaylist(song.alive)"
                                      v-text="song.song_name"
                                    />
                                  </v-list-item-content>
                                </template>
                                <span v-html="getSongNameAndDurationForSongTooltip(song)"/>
                              </v-tooltip>
                            </v-list-item>
                          </draggable>
                        </v-list>
                      </v-card>
                    </v-col>
                    <v-col cols="6" class="mt-n11">
                      <v-card
                        min-height="100px"
                        class="mr-3"
                        width="96%"
                      >
                        <div class="pt-2">
                          <v-chip
                            class="text-h4 ml-1 mr-2"
                            color="primary"
                            text-color="white"
                          >
                            {{ $t('timer.selectedSongs') }}
                          </v-chip>
                          <v-btn
                            color="primary darken-1"
                            text
                            :disabled="singleSelectedSongIndex == null"
                            @click="removeSingleSongs"
                            max-width="60px"
                            class="pl-1 mr-2"
                          >
                            {{ $t('timer.deleteSingle') }}
                          </v-btn>
                          <v-btn
                            fab
                            color="primary"
                            @click="songMoveUp"
                            :disabled="singleSelectedSongIndex == null || singleSelectedSongIndex === 0"
                            small
                          >
                            <v-icon size="25">mdi-arrow-up-bold</v-icon>
                          </v-btn>
                          <v-btn
                            fab
                            color="primary"
                            @click="songMoveDown"
                            :disabled="singleSelectedSongIndex == null || singleSelectedSongIndex >= selectedSongs.length - 1"
                            small
                          >
                            <v-icon size="25">mdi-arrow-down-bold</v-icon>
                          </v-btn>
                          <v-btn
                            fab
                            color="primary"
                            @click="removeSelectedSongs"
                            :disabled="selectedSongs.length === 0"
                            small
                            class="ml-n0"
                          >
                            <v-icon size="25">mdi-delete</v-icon>
                          </v-btn>
                        </div>
                        <v-list :max-height="get_vlist_max_height">
                          <draggable
                            :list="selectedSongs"
                            :group="{ name: 'song'}"
                          >
                            <v-list-item-group :value="singleSelectedSongIndex">
                              <v-list-item
                                v-for="(song,i) in selectedSongs"
                                :key="i"
                                dense
                                :input-value="singleSelectedSongIndex === i"
                                @dblclick="dbClickRightSongList(i)"
                                @change="changeSelected(i)"
                              >
                                <v-tooltip
                                  top
                                  open-on-hover
                                  open-delay="1000"
                                  nudge-left="10"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-list-item-content v-bind="attrs" v-on="on">
                                      <v-list-item-title
                                        :style="songStyleInPlaylist(song.alive)"
                                        v-text="song.song_name"
                                      />
                                    </v-list-item-content>
                                  </template>
                                  <span v-html="getSongNameAndDurationForSongTooltip(song)"/>
                                </v-tooltip>
                              </v-list-item>
                            </v-list-item-group>
                          </draggable>
                        </v-list>
                      </v-card>
                    </v-col>
                  </v-row>
                </div>
              </div>
            </v-card>
          </v-col>

          <v-col
            cols="12"
          >
            <v-card height="60px" align="center" class="pt-3">
              <v-btn color="primary" @click="timerDialog = false">{{ $t('common.cancel') }}</v-btn>
              <v-btn color="primary" @click="createOrEditTimer(isCreateTimer)">{{ $t('common.confirm') }}</v-btn>
            </v-card>
          </v-col>
        </v-row>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="createSchemaDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('timer.createSchema') }}</span>
        </v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col
                cols="12"
              >
                <v-form v-model="schemaNameValid">
                  <v-text-field
                    v-model="newSchemaName"
                    :label="$t('timer.schemaNamePlaceholder')"
                    required
                    :rules="newSchemaNameRules"
                  />
                </v-form>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="createSchemaDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            :loading="createSchemaLoading"
            :disabled="createSchemaLoading"
            @click="createSchema"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="editSchemaDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('timer.editSchema') }}</span>
        </v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col
                cols="12"
              >
                <v-form v-model="schemaNameValid">
                  <v-text-field
                    v-model="newSchemaName"
                    :label="$t('timer.schemaNamePlaceholder')"
                    required
                    :rules="newSchemaNameRules"
                  />
                </v-form>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="editSchemaDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            :loading="editSchemaLoading"
            :disabled="editSchemaLoading"
            @click="editSchema"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--树形电源选择对话框-->
    <v-dialog
      v-model="powerSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
          dark
          flat
        >
          <v-toolbar-title>{{ $t('timer.powerSequencerSelectionTitle') }}</v-toolbar-title>
        </v-toolbar>
        <v-row>
          <v-col col="12">
            <!--<v-card-text>-->
              <v-text-field
                v-model="powerSelectSearch"
                flat
                light
                hide-details
                clearable
                clear-icon="mdi-close-circle-outline"
                class="pt-0 mt-0"
                append-icon="mdi-magnify"
              />
              <v-treeview
                v-model="powerTreeSelection"
                :items="powerTreeData"
                item-key="id"
                item-text="name"
                item-children="children"
                selected-color="primary"
                selection-type="leaf"
                selectable
                return-object
                hoverable
                activatable
                dense
                :open-all="sequencePowerInfo.length === 1"
                :filter="treeViewFilter"
                :search="powerSelectSearch"
                :active.sync="activeTreeNodes"
              ></v-treeview>
            <!--</v-card-text>-->
          </v-col>
        </v-row>

        <v-divider></v-divider>

        <v-card-actions>
          <v-btn
            class="white--text"
            color="primary darken-1"
            depressed
            @click="powerTreeSelection = restorePowerTreeSelection"
          >
            {{ $t('timer.reset') }}
            <v-icon right>
              mdi-backup-restore
            </v-icon>
          </v-btn>

          <v-spacer></v-spacer>

          <v-btn
            class="white--text"
            color="primary darken-1"
            depressed
            @click="powerSelectDialog = false"
          >
            {{ $t('common.confirm') }}
            <v-icon right>
              mdi-content-save
            </v-icon>
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!--分区选择对话框-->
    <v-dialog
      v-model="partitionSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('timer.zoneSelectionTitle') }}</span>
        </v-card-title>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="partitionSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('timer.zoneKeywordPlaceholder')"
            :data="myZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('timer.zoneList'), $t('timer.selectedZones')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="partitionSelectDialog = false"
          >
            {{ $t('timer.close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- 分组选择对话框-->
    <v-dialog
      v-model="groupSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('timer.groupSelectionTitle') }}</span>
        </v-card-title>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="groupSelectTransfer"
            filterable
            :filter-placeholder="$t('timer.groupKeywordPlaceholder')"
            :data="groupList"
            :props="{
              key: 'group_id',
              label: 'group_name'
            }"
            :titles="[$t('timer.groupList'), $t('timer.selectedGroups')]"
          />
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="groupSelectDialog = false"
          >
            {{ $t('timer.close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--########################-->
    <!--主显示页面-->
    <v-container
      id="Timers"
      fluid
      style="margin-top: 2px"
      v-if="isAdmin"
    >
      <v-card class="mb-2">
        <v-card-title>
          <!--按钮栏-->
          <span class="text-lg-h3">{{ $t('timer.timerScheme') }}</span>
          <span style="padding-left: 30px" />
          <v-btn
            color="primary"
            @click="preCreateSchema"
            :disabled="!isAdmin"
          >
            {{ $t('timer.createTimerScheme') }}
          </v-btn>
          <v-btn
            v-show="schemaSelected.length > 0"
            color="primary"
            @click="preEditSchema"
            :disabled="!isAdmin"
          >
            {{ $t('timer.editTimerScheme') }}
          </v-btn>
          <v-btn
            v-show="schemaSelected.length > 0"
            color="primary"
            @click="preDeleteSchema"
            :disabled="!isAdmin"
          >
            {{ $t('timer.deleteTimerScheme') }}
          </v-btn>
          <v-btn
            v-show="schemaSelected.length > 0"
            color="primary"
            @click="preCopySchema"
            :disabled="!isAdmin"
          >
            {{ $t('timer.copyTimerScheme') }}
          </v-btn>
          <v-btn
            v-show="schemaSelected.length > 0 && schemaSelected[0].time_scheme_id !== currentSchemaId"
            color="primary"
            @click="preSetActiveSchema"
            :disabled="!isAdmin"
          >
            {{ $t('timer.setCurrentSchemeActive') }}
          </v-btn>
          <v-spacer />
          <v-text-field
            v-model="schemaSearch"
            append-icon="mdi-magnify"
            :label="$t('timer.search')"
            single-line
            hide-details
            full-width
            style="padding-bottom: 35px"
          />
        </v-card-title>
        <v-data-table
          v-model="schemaSelected"
          :headers="listHeaders"
          :items="getEnhancedSchemaInfo"
          :search="schemaSearch"
          sort-by.sync="['time_scheme_id']"
          item-key="time_scheme_id"
          class="elevation-1"
          single-select
          :loading="getEnhancedSchemaInfo.length === 0"
          :loading-text="$t('timer.noTimerSchemeSet')"
          style="margin-top: -10px"
          :items-per-page="5"
          @click:row="rowSchemaClick"
          :footer-props="{
            itemsPerPageOptions: [5,10,20,50],
            showFirstLastPage: true,
            showCurrentPage: true,
            firstIcon: 'mdi-arrow-collapse-left',
            lastIcon: 'mdi-arrow-collapse-right',
            prevIcon: 'mdi-minus',
            nextIcon: 'mdi-plus'
          }"
        >
          <template v-slot:item.valid="{ item }">
            <v-chip :color="getColorOfSchemaStatus(item.valid)" dark>{{ getSchemaValidStatus(item.valid) }}</v-chip>
          </template>
        </v-data-table>
      </v-card>
    </v-container>
    <v-container
      id="Timers"
      fluid
      :style="isAdmin ? '' : 'margin-top: 2px'"
      :class="isAdmin ? 'mt-6' : ''"
    >
      <v-card :class="isAdmin ? '' : 'mb-2'">
        <v-card-title>
          <span class="text-lg-h3">{{ $t('timer.timerPointSettings') }}</span>
          <span style="padding-left: 30px" />
          <v-btn
            color="primary"
            @click="preCreateTimer"
          >
            {{ $t('timer.createTimerPoint') }}
          </v-btn>
          <v-btn
            v-show="timerSelected.length > 0"
            color="primary"
            @click="preEditTimer"
          >
            {{ $t('timer.editTimerPoint') }}
          </v-btn>
          <v-btn
            v-show="timerSelected.length > 0"
            color="primary"
            @click="preDeleteTimer"
          >
            {{ $t('timer.deleteTimerPoint') }}
          </v-btn>
          <v-btn
            v-show="timerSelected.length > 0 && timerSelected[0].vaild === 'false'"
            color="primary"
            @click="setTimePointValid(true)"
          >
            {{ $t('timer.enableTimerPoint') }}
          </v-btn>
          <v-btn
            v-show="timerSelected.length > 0 && timerSelected[0].vaild === 'true'"
            color="primary"
            @click="setTimePointValid(false)"
          >
            {{ $t('timer.disableTimerPoint') }}
          </v-btn>
          <v-btn
            v-show="timerSelected.length > 0"
            color="primary"
            @click="preCopyTimer"
          >
            {{ $t('timer.copyTimerPoint') }}
          </v-btn>
          <v-btn
            v-show="isAdmin && getTimerListsBySchema.length > 1 && timerSelected.length === 0"
            color="primary"
            @click="preSortTimer"
          >
            {{ $t('timer.sortTimerPoints') }}
          </v-btn>
          <v-spacer />
          <v-text-field
            v-model="timerSearch"
            append-icon="mdi-magnify"
            :label="$t('timer.search')"
            single-line
            hide-details
            full-width
            style="padding-bottom: 35px"
          />
        </v-card-title>
        <v-data-table
          v-model="timerSelected"
          :headers="headers"
          :items="getTimerListsBySchema"
          :search="timerSearch"
          sort-by.sync="['id']"
          item-key="id"
          class="elevation-1"
          single-select
          :loading="getTimerListsBySchema.length === 0"
          :loading-text="getLoadingTextForNoTimerData"
          style="margin-top: -10px"
          :items-per-page="isAdmin ? 5 : 10"
          @click:row="rowTimerClick"
          :footer-props="{
            itemsPerPageOptions: [5,10,50,100],
            showFirstLastPage: true,
            showCurrentPage: true,
            firstIcon: 'mdi-arrow-collapse-left',
            lastIcon: 'mdi-arrow-collapse-right',
            prevIcon: 'mdi-minus',
            nextIcon: 'mdi-plus'
          }"
        >
          <!--<template slot="no-data">
            <div>
              {{ $t('timer.noTimerInCurrentSchema') }}
            </div>>
          </template>-->
          <template v-slot:item.id="{ item }">
            <span>{{ getTimerListsBySchema.map(function(x) {return x.id; }).indexOf(item.id) + 1 }}</span>
          </template>
          <template v-slot:item.time_mode="{ item }">
            <span>{{ getTimeMode(item.time_mode) }}</span>
          </template>
          <template v-slot:item.weekday="{ item }">
            <span>{{ getTimerLoop(item) }}</span>
          </template>
          <template v-slot:item.play_mode="{ item }">
            <span>{{ $util.getPlayMode(item.play_mode) }}</span>
          </template>
          <template v-slot:item.songs="{ item }">
            <v-tooltip bottom open-on-hover open-delay="500" v-if="(item.device_type === null || item.device_type === 0) && (item.source_type == null || item.source_type === 0)">
              <template v-slot:activator="{ attr, on }">
                <span v-bind="attr" v-on="on">{{ $util.getTimerSongs(item.songs) }}</span>
              </template>
              <span>{{ $util.getSongNames(item.songs) }}</span>
            </v-tooltip>
            <span v-show="(item.device_type === null || item.device_type === 0) && item.source_type === 1">
              {{ $util.getAudioCollectorDescription($t('deviceTypes.audioCollector') + ':',
              item.audio_collector == null ? null : item.audio_collector.device_mac,
              item.audio_collector == null ? null : item.audio_collector.channel) }}
            </span>
            <v-tooltip bottom open-on-hover open-delay="500" v-if="item.device_type === 1">
              <template v-slot:activator="{ attr, on }">
                <span v-bind="attr" v-on="on">{{ $util.getTimerSequencePowerNames($t('deviceTypes.powerSequencer') + ':', item.sequence_powers) }}</span>
              </template>
              <span>{{ $util.getSequencePowerNames($t('deviceTypes.powerSequencer') + ':', item.sequence_powers) }}</span>
            </v-tooltip>
          </template>
          <template v-slot:item.sections="{ item }">
            <v-tooltip bottom open-on-hover open-delay="500">
              <template v-slot:activator="{ attr, on }">
                <span v-bind="attr" v-on="on">{{ $util.getTimerSections(item.sections) }}</span>
              </template>
              <span>{{ $util.getTimerSectionsNames(item.sections) }}</span>
            </v-tooltip>
          </template>
          <template v-slot:item.groups="{ item }">
            <v-tooltip bottom open-on-hover open-delay="500">
              <template v-slot:activator="{ attr, on }">
                <span v-bind="attr" v-on="on">{{ $util.getTimerGroups(item.groups) }}</span>
              </template>
              <span>{{ $util.getTimerGroupsName(item.groups) }}</span>
            </v-tooltip>
          </template>
          <!--20211030 定时点生效修改为switch模式-->
          <!--@click.native.stop: 当点击滑块操作时,不修改表格行的选中状态-->
          <template v-slot:item.vaild="{ item }">
            <v-row justify="center">
              <v-switch
                :key="timerValidRefreshKey"
                :input-value="getValidStatus(item.vaild)"
                :label="$t('timer.effective')"
                color="primary"
                @change="setTimePointValidForColumn(item.id, item.name, item.vaild)"
                @click.native.stop
              />
            </v-row>
          </template>
          <!--操作列-->
          <template v-slot:item.actions="{ item }">
            <v-icon
              large
              :title="$t('timer.edit')"
              @click.stop.prevent="preEditTimerActionInTable(item)"
            >
              mdi-pencil
            </v-icon>
            <v-icon
              large
              :title="$t('timer.delete')"
              @click.stop.prevent="preDeleteTimerActionInTable(item)"
            >
              mdi-delete
            </v-icon>
          </template>
        </v-data-table>
      </v-card>
    </v-container>
  </div>
</template>

<style scoped>
  /deep/ tr.v-data-table__selected {
    background: #c2c9f3 !important;
  }
  /deep/ .v-input__slot {
    margin-bottom: 0;
  }
  .section-high-class >>> .v-select__selection {
    border-top-width: 3px;
    margin-top: 6px;
  }

  /deep/ .v-dialog {
    overflow-x: hidden;
    overflow-y: auto;
  }

  /deep/ .v-card {
    border-radius: 6px !important;
    margin-top: 0px !important;
    margin-bottom: 0px !important;
  }

  /deep/ .v-list {
    overflow-y: auto
  }
  /deep/ .theme--dark.v-label {
    color: white;
  }
</style>

<style>

</style>

<script>

import {mapGetters, mapState} from 'vuex'
import {stringToCharArray} from '@/plugins/utils'
import draggable from 'vuedraggable'
import {addTimes} from "../../plugins/utils";
import {customerVersion} from '@/plugins/websocket'

export default {
  name: 'Timer',
  components: {draggable},

  data: () => ({
    winHeight : window.innerHeight,
    render: true,
    timerValidRefreshKey: 0,
    successMessages: '',
    commonSuccessSnackBar: false,
    errorMessages: '',
    commonErrorSnackBar: false,
    createSchemaDialog: false,
    editSchemaDialog: false,
    deleteSchemaDialog: false,
    deleteTimerDialog: false,
    deleteTimerActionInTableDialog: false,
    setActiveSchemaDialog: false,
    copySchemaDialog: false, // 复制定时方案对话框
    copyTimerDialog: false, // 复制定时点对话框
    sortTimerDialog: false, // 定时点排序
    newSchemaName: '',
    // todo 取消loading
    createSchemaLoading: false,
    editSchemaLoading: false,
    deleteSchemaLoading: false,
    addTimePointLoading: false,
    partitionSelectTransfer: [], // 已选择的分区
    partitionSelectDialog: false,
    groupSelectTransfer: [], // 已选择的分组
    groupSelectDialog: false,
    selectPartitions: [],
    selectAllPartition: false,
    selectAllGroups: false,
    time1: null, // 定时点开始时间
    time2: null, // 定时点结束时间
    date: null, // 定时点开始日期
    date2: null, // 定时点结束日期
    menu: false,
    menu2: false,
    timeMenu1: false,
    timeMenu2: false,
    timerValid: true,
    timerDialog: false,
    isCreateTimer: true, // 用于标记定时点操作的状态，true为增加定时点,false为编辑定时点,用于切换定时点界面的显示
    snackbarTimeout: 1500,
    schemaSearch: null,
    timerSearch: null,
    timerVolume: 50,
    timerName: null,
    daySwitch: false,
    // 选择的日期循环，全选则为[1,2,3,4,5,6,7]
    daySelected: [],
    dateSwitch: false,
    schemaSelected: [], // 定时方案已选中的
    timerSelected: [], // 定时点已选中的
    // 选中列表的id, list_id字段
    selectedPlayMode: null,
    selectedPlayList: null,
    selectedSongs: [],
    singleSelectedSongIndex: null,
    singleReadySongIndex: null,
    // rules
    schemaNameValid: false,
    isVolFollowDevice: false, // 定时点音量是是否跟随设备音量，默认为false
    isVolFollowDevice: [],
    deletedTimerActionInTable: null,
    editTimerActionInTable: null, // 通过右侧列表中的编辑图标选中的定时点
    // 树形视图
    powerSelectDialog: false,
    powerTreeSelection: [],
    restorePowerTreeSelection: [], // 存储回显的电源时序器选择
    powerSelectSearch: null,
    selectedPowerNumber: 0,
    selectedPowerChannelNumber: 0,
    activeTreeNodes: [],
    lastActiveTreeNode: null,
    // 音频采集器相关
    timerSourceType: 0, // 定时点音源选择： 0表示歌曲播放，1表示音频采集
    timerDeviceType: 0, // 设备类型选择： 0表示解码终端 1表示电源时序器
    audioCollectorSearch: null,
    audioCollectorActiveIds: [], // 通过代码逻辑设置为单选,取该数组中的第一个元素即为当前选中的通道
    audioCollectorOpenIds: [],
    lastActiveOpenId: null,
    // 添加数据属性来存储动态翻译内容
    dynamicHeaders: [],
    dynamicListHeaders: [],
    dynamicPlayModes: [],
    dynamicNewSchemaNameRules: [],
  }),
    computed: {
      ...mapState(['errorDuration','timerList', 'timers', 'playList', 'addSchemaResult', 'editSchemaResult',
        'deleteSchemaResult', 'addTimePointResult', 'editTimerResult', 'deleteTimerResult',
        'currentSchemaId', 'setActiveSchemaResult', 'errorId', 'errorWsMessage', 'setTimePointValidResult',
        'apiName', 'copySchemaResult', 'user', 'commandName', 'copyTimerResult', 'sortTimerResult', 'sequencePowerInfo']),
      ...mapGetters(['isAdmin', 'isTimerAuthorizedUser', 'audioCollectors', 'isAudioCollectorAuthorizedUser']),
      getTooltipForSelectedAudioCollector() {
        if (this.audioCollectorActiveIds.length === 0) {
          return this.$t('timer.messages.noAudioCollectorSelected')
        }
        const mac = this.audioCollectorActiveIds[0].split("|")[0]
        const channel = this.audioCollectorActiveIds[0].split("|")[1]
        return this.$util.getAudioCollectorDescription(this.$t('timer.audioCollector.selected'), mac, channel)
      },
      audioCollectorSearchTreeData() {
        if (this.audioCollectors.length === 0) {
          return []
        }
        const data = []
        this.audioCollectors.forEach(collector => {
          const children = []
          const channelSizes = 4
          for (let i = 1; i <= channelSizes; i++) {
            children.push({
              // 举例： 2C:21:C8:B8:66:1C|1
              id: collector.mac + '|' + i,
              name: 'CH' + i,
            })
          }
          data.push({
            id: collector.mac,
            name: collector.name,
            children: children
          })
        })
        return data
      },
      /**
       * 组装电源选择所需的数据
       * mandatory fields:
       * 1. id
       * 2. name
       * 3. children
       */
      powerTreeData() {
        if (this.sequencePowerInfo.length === 0) {
          return []
        }
        const macs = this.sequencePowerInfo.map(x => x.device_mac);
        let zoneNamesByZoneMacs = this.$ws.getZoneNamesByZoneMacs(macs);
        return this.sequencePowerInfo.map((item, index) => {
          let myChildren = []
          if (item.channels != null && item.channels.length !== 0) {
            myChildren = item.channels.map(channel => {
              return {
                // 举例： 2C:21:C8:B8:66:1C|1
                id: item.device_mac + '|' + channel.channel_id,
                name: channel.name,
                status: channel.status
              }
            })
          }
          return {
            id: item.device_mac,
            name: zoneNamesByZoneMacs[index],
            children: myChildren
          };
        })
      },
      treeViewFilter () {
        // 搜索为大小写不敏感
        return (item, treeViewSearch, textKey) => {
          const text = item[textKey] != null ? item[textKey].toLowerCase() : ''
          const searchKey = treeViewSearch != null ? treeViewSearch.toLowerCase() : ''
          return text.indexOf(searchKey) > -1
        }
      },
      getSelectedPlayListSongs() {
        if (this.selectedPlayList == null) {
          return []
        }
        const list = this.playList.filter(list => list.list_id === this.selectedPlayList)
        return list == null ? [] : list[0].songs
      },
      /*操作员显示自己的分组*/
      groupList() {
        return this.$store.getters.myGroupList
      },
      apiSuccessMsg() {
        return this.$store.getters.apiSuccessMsg
      },
      // 获取定时方案信息（带有效性）
      getEnhancedSchemaInfo () {
        const newList = JSON.parse(JSON.stringify(this.timerList))
        newList.forEach(list => {
          list.valid = list.time_scheme_id
        })
        return newList
      },
      // 获取开始日期
      getStartDate () {
        let startDate = new Date(this.date)
        if(this.isCreateTimer) {
          startDate = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 1)
        }
        return startDate.toISOString().substr(0, 10)
      },
      // 获取解码设备
      myZones () {
        return this.$store.getters.decodeZones
      },
      // 获取结束日期
      getEndDate() {
        let endDate = new Date(this.date2)
        if(this.isCreateTimer) {
          endDate = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 1)
        }
        return endDate.toISOString().substr(0, 10)
      },
      getMinDate () {
        const minDate = new Date("2021-01-01")
        return minDate.toISOString().substr(0, 10)
      },
      getMaxDate () {
        const maxDate = new Date("2099-12-31")
        return maxDate.toISOString().substr(0, 10)
      },
      // 获取选定定时方案的定时点
      getTimerListsBySchema() {
        const selectedTimerLists = this.schemaSelected.length > 0 ? this.schemaSelected[0].timerlists : []
        // ******** 非管理员账户只显示属于自己账户的定时点
        // ******** 增加权限控制，如果有定时点权限，可显示/管理所有用户的定时点
        return (this.isAdmin || this.isTimerAuthorizedUser)
          ? selectedTimerLists
          : selectedTimerLists.filter(list => list.account === this.user)
      },
      // 获取定时预估播放时长
      getTitleOfTimerSelectedSongs() {
        const chosenSongsTips = this.$t('timer.selectedSongs') + ':  ' + this.selectedSongs.length
        if (this.selectedPlayMode !== 3 || this.selectedSongs.length === 0) {
          return chosenSongsTips
        }
        return chosenSongsTips + '&nbsp;&nbsp;&nbsp;&nbsp;' + this.$t('timer.estimatedDuration') + '：' + this.getAllDurationOfSelectSongs()
      },
      // 获取定时点列表无数据时的提示语
      getLoadingTextForNoTimerData() {
        if (this.isAdmin) {
          return this.schemaSelected.length === 0 ? this.$t('timer.pleaseSelectSchema') : this.$t('timer.noTimerInCurrentSchema')
        }
        if (this.currentSchemaId == null || this.schemaSelected.length === 0) {
          return this.$t('timer.noActiveSchema');
        }
        return this.$t('timer.noTimerForCurrentAccount')
      },
      get_vcard_height() {
        // console.log("height1="+this.winHeight)
        if(this.winHeight > 900) {
          return 650
        }
        else {
          return 0.62*this.winHeight
        }
      },
      get_vlist_max_height() {
        // console.log("height2="+this.winHeight)
        if(this.winHeight > 900) {
          return 400
        }
        else {
          return 0.3*this.winHeight
        }
      },
      // 是否显示电源时序器
      checkIfShowSequencePower () {
        return (this.isNotCloudIpSystem() && customerVersion !== 'C4A2')
      },
      // 定时编辑-音源选择是否显示音频采集器
      checkIfShowAudioCollector () {
        return (customerVersion !== 'C4A2')
      },
      // 表格显示头部
      headers() {
        return this.dynamicHeaders.length > 0 ? this.dynamicHeaders : [
          { text: this.$t('timer.headers.volume'), value: 'volume', align: 'center', sortable: false },
          { text: this.$t('timer.headers.selectedPartitions'), value: 'sections', align: 'center', sortable: false },
          { text: this.$t('timer.headers.selectedGroups'), value: 'groups', align: 'center', sortable: false },
          { text: this.$t('timer.headers.audioSource'), value: 'songs', align: 'center', sortable: false },
          { text: this.$t('timer.headers.status'), value: 'vaild', align: 'center', sortable: false },
          { text: this.$t('timer.headers.actions'), value: 'actions', align: 'center', sortable: false },
        ]
      },
      // 定时方案headers
      listHeaders() {
        return this.dynamicListHeaders.length > 0 ? this.dynamicListHeaders : [
          { text: this.$t('timer.headers.id'), value: 'time_scheme_id', align: 'center', sortable: false },
          { text: this.$t('timer.headers.name'), value: 'time_scheme_name', align: 'center', sortable: false },
          { text: this.$t('timer.headers.status'), value: 'valid', align: 'center', sortable: false },
        ]
      },
      // 播放模式
      playModes() {
        return this.dynamicPlayModes.length > 0 ? this.dynamicPlayModes : [
          { id: 3, name: this.$t('timer.playModes.sequential'), icon: 'mdi-playlist-play' },
          { id: 4, name: this.$t('timer.playModes.loop'), icon: 'mdi-repeat' },
          { id: 5, name: this.$t('timer.playModes.random'), icon: 'mdi-shuffle-variant' },
        ]
      },
      // 验证规则
      newSchemaNameRules() {
        return this.dynamicNewSchemaNameRules.length > 0 ? this.dynamicNewSchemaNameRules : [
          v => (v !== null && v.length !== 0) || this.$t('timer.validation.schemaNameRequired'),
          v => (v.length <= 10 && v.length > 0) || this.$t('timer.validation.schemaNameLength')
        ]
      }
    },
    watch: {
      // 监听语言变化
      '$i18n.locale'() {
        this.initializeTranslations()
      },
      // 统一错误处理
      errorDuration () {
        this.snackbarTimeout = this.errorDuration
      },
      errorId: function () {
        if (this.$route.fullPath !== '/dashboard/timer') {
          return;
        }
        if (this.$store.state.errorId !== null) {
          this.errorMessages = this.$store.state.errorWsMessage
          this.commonErrorSnackBar = true
          // 关闭相关loading
          switch (this.commandName) {
            case 'add_time_scheme': this.createSchemaLoading = false; break;
            case 'remove_time_scheme': this.deleteSchemaLoading = false; break;
            case 'edit_time_scheme': this.editSchemaLoading = false; break;
            case 'add_time_point': this.addTimePointLoading = false; break;
            case 'time_point_valid': this.timerValidRefreshKey += 1; break;
            default: break;
          }
        }
      },
      audioCollectorActiveIds() {
        if (this.audioCollectorActiveIds.length > 1) {
          this.audioCollectorActiveIds = [this.audioCollectorActiveIds.slice(-1)[0]]
        }
      },
      audioCollectorOpenIds() {
        switch (this.audioCollectorOpenIds.length) {
          case 0:
            // 收起树形结构时，清空选中的采集器
            this.audioCollectorActiveIds = []
            break
          case 1:
            if (this.lastActiveOpenId != null && this.lastActiveOpenId !== this.audioCollectorOpenIds[0]) {
              // 收起树形结构时，清空选中的采集器
              this.audioCollectorActiveIds = []
            }
            this.lastActiveOpenId = this.audioCollectorOpenIds[0];
            break
          default:
            // 只能同时展开一个树形结构
            this.audioCollectorOpenIds = [this.audioCollectorOpenIds.slice(-1)[0]];
        }
      },
      activeTreeNodes() {
        if (this.activeTreeNodes.length !== 0) {
          this.lastActiveTreeNode = this.activeTreeNodes[0];
        }
        const node = this.activeTreeNodes.length !== 0 ? this.activeTreeNodes[0] : this.lastActiveTreeNode
        if (node == null || node.length === 0) {
          return
        }

        let needHandleNode = [];
        if (node.children == null) {
          needHandleNode.push(node);
        } else {
          const ids = node.children.map(x => x.id)
          if (this.activeTreeNodes.length === 0) {
            // 取消选中时序器，则取消所有当前时序器的通道选择
            this.powerTreeSelection = this.powerTreeSelection.filter(s => !ids.includes(s.id))
          } else {
            // 选中一个时序器，则将未添加的添加到选中
            this.powerTreeSelection = this.powerTreeSelection.filter(x => !x.id.startsWith(node.id))
            node.children.forEach(n => this.powerTreeSelection.push(n))
          }
        }

        // 单个处理
        needHandleNode.forEach(node => {
          if (this.powerTreeSelection.some(g => g.id === node.id)) {
            // if already selected, cancel the clicked status
            this.powerTreeSelection = this.powerTreeSelection.filter(g => g.id !== node.id);
          } else {
            // else push the node
            this.powerTreeSelection.push(node)
          }
        })
      },
      // 修复360浏览器不能显示和赋值定时点时间为00秒的问题
      time1() {
        if (this.time1 == null || this.time1.length === 8) {
          return
        }
        if (this.time1.length === 5) {
          this.time1 = this.time1 + ':00'
        }
      },
      time2() {
        if (this.time2 == null || this.time2.length === 8) {
          return
        }
        if (this.time2.length === 5) {
          this.time2 = this.time2 + ':00'
        }
      },
      // 关闭删除定时点对话框后，清空选中的删除定时点对象
      deleteTimerActionInTableDialog() {
        if (!this.deleteTimerActionInTableDialog) {
          this.deletedTimerActionInTable = null
        }
      },
      // 关闭编辑定时点对话框后，清空选中的编辑定时点对象
      timerDialog() {
        if (!this.timerDialog) {
          this.editTimerActionInTable = null
        } else {
          /* 打开定时点页面已经判断并获取，此处不需要再次获取
          // 打开对话框时，获取时序数据至内存
          if (this.sequencePowerInfo.length === 0) {
            this.$ws.getSequencePowerInfo()
          }
          */
        }
      },
      // 只能选择一种日期循环方式（星期or日期） todo 修改为使用单一变量
      daySwitch: function () {
        if (this.daySwitch) {
          this.dateSwitch = false
        }
      },
      dateSwitch: function () {
        if (this.dateSwitch) {
          this.daySwitch = false
        }
      },
      // 20220605 增加已选中的电源时序器及电源时序器通道数显示
      powerTreeSelection: {
        handler: function () {
          if (this.powerTreeSelection.length === 0) {
            this.selectedPowerNumber = 0
            this.selectedPowerChannelNumber = 0
            return
          }
          // 举例： 2C:21:C8:B8:66:1C|1    info.id格式如下: item.device_mac + '|' + channel.channel_id,
          const powerSet = new Set() // 采用set去重
          const channelList = []
          this.powerTreeSelection.forEach(info => {
            powerSet.add(info.id.split('|')[0])
            channelList.push(info.id.split('|')[1])
          })
          this.selectedPowerNumber = powerSet.size
          this.selectedPowerChannelNumber = channelList.length
        },
        deep: true
      },
      // 监听修改，实时更新定时点内容
      timerList: function () {
        this.$store.state.timerList.forEach(timer => {
          if (this.schemaSelected.length > 0 && timer.time_scheme_id === this.schemaSelected[0].time_scheme_id) {
            // this.schemaSelected[0] = JSON.parse(JSON.stringify(timer))
            this.schemaSelected.splice(0, 1, JSON.parse(JSON.stringify(timer)))
          }
          if (this.timerSelected.length > 0) {
            this.schemaSelected[0].timerlists.forEach(timePoint => {
              if (timePoint.id === this.timerSelected[0].id && timePoint.name === this.timerSelected[0].name) {
                this.timerSelected.splice(0, 1, JSON.parse(JSON.stringify(timePoint)))
              }
            })
          }
        })
      },
      addSchemaResult: function () {
        this.createSchemaLoading = false
        if (this.$store.state.addSchemaResult === 0) {
          this.successMessages = this.$t('timer.messages.createSchemaSuccess')
          this.commonSuccessSnackBar = true
          this.createSchemaDialog = false
          this.newSchemaName = ''
          // this.$ws.getServerTimer()
          this.$store.commit('updateAddSchemaResult', null)
        }
      },
      editSchemaResult: function () {
        this.editSchemaLoading = false
        if (this.$store.state.editSchemaResult === 0) {
          this.successMessages = this.$t('timer.messages.editSchemaSuccess')
          this.commonSuccessSnackBar = true
          this.editSchemaDialog = false
          // 取消当前选中状态
          // this.schemaSelected = []
          // 清空编辑名字
          this.newSchemaName = ''
          // this.$ws.getServerTimer()
          this.$store.commit('updateEditSchemaResult', null)
        }
      },
      deleteSchemaResult: function () {
        this.deleteSchemaLoading = false
        if (this.$store.state.deleteSchemaResult === 0) {
          this.successMessages = this.$t('timer.messages.deleteSchemaSuccess')
          this.commonSuccessSnackBar = true
          this.deleteSchemaDialog = false
          this.newSchemaName = ''
          // 取消已选择的定时方案
          this.schemaSelected = []
          // this.$ws.getServerTimer()
          this.$store.commit('updateDeleteSchemaResult', null)
        }
      },
      setActiveSchemaResult: function () {
        if (this.$store.state.setActiveSchemaResult === 0) {
          this.successMessages = this.$t('timer.messages.setActiveSchemaSuccess')
          this.commonSuccessSnackBar = true
          this.setActiveSchemaDialog = false
          this.newSchemaName = ''
          this.$store.commit('updateSetActiveSchemaResult', null)
        }
      },
      addTimePointResult: function () {
        this.addTimePointLoading = false
        if (this.$store.state.addTimePointResult === 0) {
          this.successMessages = this.$t('timer.messages.createTimerSuccess')
          this.commonSuccessSnackBar = true
          this.timerDialog = false
          // this.schemaSelected = [] // 取消选中状态，因定时点显示暂不能添加完成后同步更新
          this.timerName = ''
          this.$store.commit('updateAddTimePointResult', null)
        }
      },
      editTimerResult: function () {
        if (this.$store.state.editTimerResult === 0) {
          this.successMessages = this.$t('timer.messages.editTimerSuccess')
          this.commonSuccessSnackBar = true
          this.timerDialog = false
          this.timerSelected = []
          // this.schemaSelected = [] // 取消选中状态，因定时点显示暂不能添加完成后同步更新
          this.timerName = ''
          this.$store.commit('updateEditTimerResult', null)
        }
      },
      deleteTimerResult: function () {
        // this.deleteSchemaLoading = false
        if (this.$store.state.deleteTimerResult === 0) {
          this.successMessages = this.$t('timer.messages.deleteTimerSuccess')
          this.commonSuccessSnackBar = true
          this.deleteTimerDialog = false
          this.deleteTimerActionInTableDialog = false
          this.timerSelected = []
          // this.deleteSchemaDialog = false
          this.$store.commit('updateDeleteTimerResult', null)
        }
      },
      setTimePointValidResult: function () {
        if (this.setTimePointValidResult === 0) {
          // 开始使用通用成功信息
          this.successMessages = this.apiSuccessMsg
          this.commonSuccessSnackBar = true
          this.$store.commit('updateSetTimePointValidResult', null)
        }
      },
      copySchemaResult() {
        if (this.copySchemaResult === 0) {
          this.successMessages = this.$t('timer.messages.copySchemaSuccess');
          this.commonSuccessSnackBar = true;
          this.copySchemaDialog = false;
          this.newSchemaName = ''
          this.$store.commit('updateCopySchemaResult', null)
        }
      },
      copyTimerResult() {
        if (this.copyTimerResult === 0) {
          this.successMessages = this.$t('timer.messages.copyTimerSuccess');
          this.commonSuccessSnackBar = true;
          this.copyTimerDialog = false;
          this.$store.commit('updateCopyTimerResult', null)
        }
      },
      sortTimerResult() {
        if (this.sortTimerResult === 0) {
          this.successMessages = this.$t('timer.messages.sortTimerSuccess');
          this.commonSuccessSnackBar = true;
          this.sortTimerDialog = false;
          this.$store.commit('updateSortTimerResult', null)
        }
      },
    },
    mounted () {
      // 初始化翻译文本
      this.initializeTranslations()
      // this.date = this.getStartDate
      // this.date2 = this.getStartDate
      // 进入页面，选中当前生效的定时方案
      if (this.currentSchemaId != null && this.getEnhancedSchemaInfo != null) {
        this.schemaSelected = []
        this.getEnhancedSchemaInfo.forEach(schema => {
          if (schema.time_scheme_id === this.currentSchemaId) {
            this.schemaSelected[0] = schema
          }
        })
      }
      window.addEventListener('resize', this.reload)
    },
    beforeDestroy () {
      window.removeEventListener('resize',this.reload)
    },
    methods: {
      initializeTranslations() {
        // 初始化消息
        this.successMessages = this.$t('common.success')
        this.errorMessages = this.$t('common.error')

        // 初始化表格标题
        this.dynamicHeaders = [
          {text: this.$t('timer.headers.id'), align: 'center', value: 'id'},
          {text: this.$t('timer.headers.user'), value: 'account', align: 'center', sortable: false},
          {text: this.$t('timer.headers.name'), value: 'name', align: 'center', sortable: false},
          {text: this.$t('timer.headers.timerMode'), value: 'time_mode', align: 'center', sortable: false},
          {text: this.$t('timer.headers.playCycle'), value: 'weekday', align: 'center', sortable: false},
          {text: this.$t('timer.headers.startTime'), value: 'start_time', align: 'center', sortable: true},
          {text: this.$t('timer.headers.endTime'), value: 'end_time', align: 'center', sortable: true},
          {text: this.$t('timer.headers.playMode'), value: 'play_mode', align: 'center', sortable: false},
          {text: this.$t('timer.headers.volume'), value: 'volume', align: 'center', sortable: false},
          {text: this.$t('timer.headers.selectedZones'), value: 'sections', align: 'center', sortable: false},
          {text: this.$t('timer.headers.selectedGroups'), value: 'groups', align: 'center', sortable: false},
          {text: this.$t('timer.headers.playSource'), value: 'songs', align: 'center', sortable: false},
          {text: this.$t('timer.headers.status'), value: 'vaild', align: 'center', sortable: false},
          {text: this.$t('timer.headers.actions'), value: 'actions', align: 'center', sortable: false},
        ]

        // 初始化定时方案表格标题
        this.dynamicListHeaders = [
          {text: this.$t('timer.headers.id'), value: 'time_scheme_id', align: 'center', sortable: false},
          {text: this.$t('timer.headers.name'), value: 'time_scheme_name', align: 'center', sortable: false},
          {text: this.$t('timer.headers.status'), value: 'valid', align: 'center', sortable: false},
        ]

        // 初始化播放模式
        this.dynamicPlayModes = [
          {id: 3, name: this.$t('timer.playModes.sequential'), icon: 'mdi-playlist-play'},
          {id: 4, name: this.$t('timer.playModes.loop'), icon: 'mdi-repeat'},
          {id: 5, name: this.$t('timer.playModes.random'), icon: 'mdi-shuffle-variant'},
        ]

        // 初始化验证规则
        this.dynamicNewSchemaNameRules = [
          v => (v !== null && v.length !== 0) || this.$t('timer.validation.schemaNameRequired'),
          v => (v.length <= 10 && v.length > 0) || this.$t('timer.validation.schemaNameLength')
        ]
      },
      // 重新渲染
      reload() {
        this.render = false
        this.$nextTick(() => {
          this.winHeight = window.innerHeight
          this.render = true
        })
      },
      getAudioCollectorClass(id) {
        const mac = id.includes(id) ? id.split('|')[0] : id
        const zone = this.$ws.getZoneByZoneMac(mac)
        return zone.source === -1 ? 'text-lg-h3 darkOfflineZone' : 'text-lg-h3'
      },
      // 回显定时点音频采集器数据
      prepareAudioCollectorInfo(audioCollector) {
        // 展开树
        if (audioCollector == null || audioCollector.device_mac == null || audioCollector.channel == null) {
          return
        }
        this.audioCollectorOpenIds = [audioCollector.device_mac]
        // 选中已选择的项目
        this.audioCollectorActiveIds = [audioCollector.device_mac + '|' + audioCollector.channel]
      },
      // 修正顺序播放的结束时间
      fixEndTimeOfSequencePlaying() {
        if (this.time1 == null) {
          this.errorMessages = this.$t('timer.errors.noStartTimeForFix')
          this.commonErrorSnackBar = true
          return
        }
        // 未选中歌曲时，修正为开始时间时间
        if (this.selectedSongs.length === 0) {
          this.errorMessages = this.$t('timer.errors.noSongsForFix')
          this.commonErrorSnackBar = true
          return
        }

        // 获取结束时间，开始时间+预估播放时长+延时（默认为2s）
        const delay = 2;
        let durationNumber = 0
        this.selectedSongs.forEach(song => {
          // 每首歌曲加1s
          durationNumber += (song.duration + 1)
        })
        let duration = new Date(durationNumber * 1000).toISOString();
        const hour = duration.substr(11, 2)
        const minute = duration.substr(14, 2)
        const second = duration.substr(17, 2)
        const durationTime = hour + ':' + minute + ':' + (parseInt(second) + delay)
        const endTime = addTimes(this.time1, durationTime)

        // 当加起来超过1天，不设置时间
        if (endTime.split(':')[0] > 23) {
          this.errorMessages = this.$t('timer.errors.songsCannotFinishToday')
          this.commonErrorSnackBar = true
          return
        }

        this.time2 = endTime
      },
      // 音源列表不存在歌曲使用红色字体表示
      songStyleInPlaylist(alive) {
        if (alive) {
          return {}
        }
        return {
          color: 'red',
        }
      },
      // 获取音源列表歌曲提示（歌名+时长）, html格式，需使用v-html消费
      getSongNameAndDurationForSongTooltip(song) {
        const songDurationNumber = song.duration != null ? song.duration : 0
        let duration = new Date(songDurationNumber * 1000).toISOString();
        if (duration.substr(11, 2) !== '00') {
          // 超过1h的歌曲，显示小时
          duration = duration.substr(11, 8)
        } else {
          // 不超过1h的歌曲，不显示小时
          duration = duration.substr(14, 5)
        }
        return song.song_name + '</br>' + this.$t('timer.labels.duration') + ': ' + duration
      },
      addSingleSongs() {
        if(this.singleReadySongIndex !== null) {
          this.selectedSongs.push(this.getSelectedPlayListSongs[this.singleReadySongIndex])
        }
      },
      removeSingleSongs() {
       if(this.singleSelectedSongIndex !== null) {
          this.selectedSongs.splice(this.singleSelectedSongIndex, 1)
          this.singleSelectedSongIndex = null
        }
      },
      addAllLeftSongs() {
        this.getSelectedPlayListSongs.forEach(song => this.selectedSongs.push(song))
      },
      removeAllRightSongs() {
        this.selectedSongs = []
      },
      dbClickLeftSongList(song) {
        this.selectedSongs.push(song)
      },
      dbClickRightSongList(index) {
        this.selectedSongs.splice(index, 1)
        this.singleSelectedSongIndex = null
      },
      songMoveUp() {
        if (this.singleSelectedSongIndex == null || this.singleSelectedSongIndex === 0) {
          return
        }
        this.moveItem(this.selectedSongs, this.singleSelectedSongIndex, this.singleSelectedSongIndex - 1)
        this.singleSelectedSongIndex -= 1
      },
      songMoveDown() {
        if (this.singleSelectedSongIndex == null || this.singleSelectedSongIndex === this.selectedSongs.length - 1) {
          return
        }
        this.moveItem(this.selectedSongs, this.singleSelectedSongIndex, this.singleSelectedSongIndex + 1)
        this.singleSelectedSongIndex += 1
      },
      removeSelectedSongs() {
        // 当选中单个时，删除单个，未选中时删除全部
        if (this.singleSelectedSongIndex != null) {
          this.selectedSongs.splice(this.singleSelectedSongIndex, 1)
        } else {
          this.selectedSongs = []
        }
        this.singleSelectedSongIndex = null
      },
      moveItem(data, from, to) {
        // remove `from` item and store it
        const removedItem = data.splice(from, 1)[0];
        // insert stored item into position `to`
        data.splice(to, 0, removedItem);
      },
      // todo 应用到所有存在active样式问题的v-list-item中
      changeReadySelect(index) {
        this.singleReadySongIndex = (this.singleReadySongIndex === index) ? null : index
        this.$emit('change', this.singleReadySongIndex);
      },
      changeSelected(index) {
        this.singleSelectedSongIndex = (this.singleSelectedSongIndex === index) ? null : index
        this.$emit('change', this.singleSelectedSongIndex);
      },
      // 获取定时模式
      getTimeMode: function (mode) {
        if (mode === 0) {
          return this.$t('timer.modes.weeklyLoop')
        } else if (mode === 1) {
          return this.$t('timer.modes.dateLoop')
        } else {
          return ''
        }
      },
      // 获取播放周期
      getTimerLoop: function (item) {
        if (item.time_mode === 0) {
          return item.weekday
        } else if (item.time_mode === 1) {
          return item.start_date + '  --  ' + item.end_date
        } else {
          return ''
        }
      },
      // 获取定时点状态
      getValidStatus: function (status) {
        // return status === 'true' ? '生效' : '未生效'
        return status === 'true'
      },
      // 设置定时点不同状态的颜色显示
      getColorOfTimerStatus (status) {
        return status === 'true' ? 'primary' : 'grey'
      },
      // 获取定时方案状态
      getSchemaValidStatus: function (id) {
        return this.currentSchemaId === id ? this.$t('timer.status.active') : this.$t('timer.status.inactive')
      },
      // 设置定时方案不同状态的颜色显示
      getColorOfSchemaStatus (id) {
        return this.currentSchemaId === id ? 'primary' : 'grey'
      },
      // 获取星期x
      getDay: function (day) {
        let currentDay
        switch (day) {
          case 1: currentDay = this.$t('timer.weekdays.monday'); break
          case 2: currentDay = this.$t('timer.weekdays.tuesday'); break
          case 3: currentDay = this.$t('timer.weekdays.wednesday'); break
          case 4: currentDay = this.$t('timer.weekdays.thursday'); break
          case 5: currentDay = this.$t('timer.weekdays.friday'); break
          case 6: currentDay = this.$t('timer.weekdays.saturday'); break
          case 7: currentDay = this.$t('timer.weekdays.sunday'); break
        }
        return currentDay
      },
      // 选中定时方案
      rowSchemaClick: function (item, row) {
        // 切换定时方案，清空已选择的定时点信息
        this.timerSelected = []
        if (row.isSelected) {
          row.select(false)
        } else {
          row.select(true)
        }
      },
      // 选中定时点
      rowTimerClick: function (item, row) {
        if (row.isSelected) {
          row.select(false)
        } else {
          row.select(true)
        }
      },
      // 创建定时方案
      createSchema: function () {
        if (!this.schemaNameValid) {
          return
        }
        const sendData = {
          command: 'add_time_scheme',
          uuid: this.$store.state.uuid,
          time_scheme_name: this.newSchemaName,
        }
        this.createSchemaLoading = true
        this.$store.commit('WEBSOCKET_SEND', sendData)
      },
      // 编辑定时方案, todo 集成方案序号的修改
      editSchema: function () {
        if (!this.schemaNameValid) {
          return
        }
        const sendData = {
          command: 'edit_time_scheme',
          uuid: this.$store.state.uuid,
          time_scheme_name: this.newSchemaName,
          time_scheme_id: this.schemaSelected[0].time_scheme_id,
        }
        this.editSchemaLoading = true
        this.$store.commit('WEBSOCKET_SEND', sendData)
      },
      // 删除定时方案
      deleteSchema: function () {
        const sendData = {
          command: 'remove_time_scheme',
          uuid: this.$store.state.uuid,
          time_scheme_name: this.schemaSelected[0].time_scheme_name,
          time_scheme_id: this.schemaSelected[0].time_scheme_id,
        }
        this.deleteSchemaLoading = true
        this.$store.commit('WEBSOCKET_SEND', sendData)
      },
      // 设置定时方案有效
      setActiveSchema: function () {
        this.$ws.setCurrentTimeSchema(this.schemaSelected[0].time_scheme_id)
      },
      // 复制定时方案
      copySchema: function () {
        this.$ws.copyTimeSchema(this.schemaSelected[0].time_scheme_id)
      },
      // 复制定时点
      copyTimer: function () {
        this.$ws.copyTimer(this.schemaSelected[0].time_scheme_id, this.timerSelected[0].id, this.timerSelected[0].name)
      },
      // 定时点排序
      sortTimer: function () {
        this.$ws.sortTimer(this.schemaSelected[0].time_scheme_id)
      },
      preCreateSchema: function () {
        this.schemaNameValid = false
        this.createSchemaDialog = true
      },
      preEditSchema: function () {
        if (this.schemaSelected.length === 0) {
          this.errorMessages = this.$t('timer.errors.selectSchemaToEdit')
          this.commonErrorSnackBar = true
          return
        }
        this.schemaNameValid = false
        this.newSchemaName = this.schemaSelected[0].time_scheme_name
        this.editSchemaDialog = true
      },
      preDeleteSchema: function () {
        if (this.schemaSelected.length === 0) {
          this.errorMessages = this.$t('timer.errors.selectSchemaToDelete')
          this.commonErrorSnackBar = true
          return
        }
        this.deleteSchemaDialog = true
      },
      preSetActiveSchema: function () {
        if (this.schemaSelected.length === 0) {
          this.errorMessages = this.$t('timer.errors.selectSchemaToSet')
          this.commonErrorSnackBar = true
          return
        }
        this.setActiveSchemaDialog = true
      },
      preCopySchema () {
        if (this.schemaSelected.length === 0) {
          this.errorMessages = this.$t('timer.errors.selectSchemaToCopy')
          this.commonErrorSnackBar = true
          return
        }
        this.copySchemaDialog = true
      },
      preCopyTimer () {
        this.copyTimerDialog = true
      },
      preSortTimer () {
        this.sortTimerDialog = true
      },
      /**
       * 切换定时点生效状态，用于switch滑块
       * @param id
       * @param name
       * @param validString
       */
      setTimePointValidForColumn: function (id, name, validString) {
        const changeStatus = !(validString === 'true')
        this.$ws.setTimePointStatus(this.schemaSelected[0].time_scheme_id, id, name, changeStatus)
      },
      setTimePointValid: function (isValid) {
        if (this.timerSelected.length === 0) {
          this.errorMessages = this.$t('timer.errors.selectTimerToSet')
          this.commonErrorSnackBar = true
          return
        }
        this.$ws.setTimePointStatus(this.schemaSelected[0].time_scheme_id,
                                    this.timerSelected[0].id, this.timerSelected[0].name, isValid)
      },
      /* ==========================定时点相关========================== */
      preCreateTimer: function () {
        if (this.schemaSelected.length === 0) {
          this.errorMessages = this.$t('timer.errors.selectSchemaForTimer')
          this.commonErrorSnackBar = true
          return
        }
        this.isCreateTimer = true
        this.timerName = null
        this.timerVolume = 50
        this.daySwitch = true
        this.daySelected=[1,2,3,4,5]
        // 1123 默认选择当前日期
        this.dateSwitch = false
        this.timerDialog = true
        const presentDate = new Date()
        this.time1 = this.formatTime(presentDate)
        // 添加定时点，默认结束时间设置为开始时间+5min
        this.time2 = this.formatTime(new Date(presentDate.getTime() + 5 * 60000))
        this.date = this.getStartDate
        this.date2 = this.getEndDate
        this.partitionSelectTransfer = []
        this.groupSelectTransfer = []
        this.selectedPlayMode = 3 // 默认显示顺序播放
        if (this.playList.length > 0) {
          this.selectedPlayList = this.playList.length > 1 ? this.playList[1].list_id:this.playList[0].list_id// 默认显示第二个播放列表
        }
        this.singleSelectedSongIndex = null
        this.singleReadySongIndex = null
        this.timerValid = true;
        this.selectedSongs = []
        this.powerTreeSelection = []
        this.lastActiveTreeNode = null
        this.activeTreeNodes = []
        this.isVolFollowDevice = false
        this.restorePowerTreeSelection = []
        this.audioCollectorSearch = null
        this.audioCollectorActiveIds = []
        this.audioCollectorOpenIds = []
        this.timerSourceType = 0
        this.timerDeviceType = 0
      },
      formatTime (time) {
        const hour = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
        const minute = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
        const second = time.getSeconds() < 10 ? '0' + time.getSeconds() : time.getSeconds()
        return hour + ':' + minute + ':' + second
      },
      // 删除定时点
      preDeleteTimer: function () {
        if (this.timerSelected.length === 0) {
          this.errorMessages = this.$t('timer.errors.selectTimerToDelete')
          this.commonErrorSnackBar = true
          return
        }
        this.deleteTimerDialog = true
      },
      preDeleteTimerActionInTable(item) {
        if (item == null) {
          return
        }
        this.deletedTimerActionInTable = item
        this.deleteTimerActionInTableDialog = true
      },
      deleteTimerActionInTable() {
        this.$ws.removeTimer(this.schemaSelected[0].time_scheme_id, this.deletedTimerActionInTable.id, this.deletedTimerActionInTable.name)
      },
      deleteTimer: function () {
        if (this.timerSelected.length === 0) {
          this.errorMessages = this.$t('timer.errors.selectTimerToDelete')
          this.commonErrorSnackBar = true
          return
        }
        this.$ws.removeTimer(this.schemaSelected[0].time_scheme_id, this.timerSelected[0].id, this.timerSelected[0].name)
      },
      preEditTimerActionInTable(item) {
        if (item == null) {
          return
        }
        this.editTimerActionInTable = item
        this.preEditTimerCommon(item)
      },
      preEditTimer() {
        // 第一步，检查是否选中了定时点。是否可以不显示按钮（如果没有选择定时点）
        if (this.timerSelected.length === 0) {
          this.errorMessages = this.$t('timer.errors.selectTimerToEdit')
          this.commonErrorSnackBar = true
          return
        }
        this.preEditTimerCommon(this.timerSelected[0])
      },
      // 编辑定时点
      preEditTimerCommon: function (item) {
        this.isCreateTimer = false
        const timerSelectedElement = item
        this.timerName = timerSelectedElement.name
        this.timerVolume = timerSelectedElement.volume
        if (timerSelectedElement.time_mode === 0) {
          this.daySwitch = true
          this.dateSwitch = false
          this.date = null
          this.date2 = null
          this.daySelected = stringToCharArray(timerSelectedElement.weekday)
        } else if (timerSelectedElement.time_mode === 1) {
          this.daySwitch = false
          this.dateSwitch = true
          this.daySelected = []
          this.date = timerSelectedElement.start_date
          this.date2 = timerSelectedElement.end_date
        }
        this.time1 = timerSelectedElement.start_time
        this.time2 = timerSelectedElement.end_time
        this.partitionSelectTransfer = timerSelectedElement.sections
        this.groupSelectTransfer = timerSelectedElement.groups
        this.selectedPlayMode = timerSelectedElement.play_mode
        if (this.playList.length > 0) {
          this.selectedPlayList = this.playList.length > 1 ? this.playList[1].list_id:this.playList[0].list_id// 默认显示第二个播放列表
        }
        this.isVolFollowDevice = timerSelectedElement.vol_follow_device !== 'false'
        this.timerValid = timerSelectedElement.vaild !== 'false'

        // 回显电源时序器数据
        this.prepareSequencePowerInfo(timerSelectedElement.sequence_powers)
        this.audioCollectorSearch = null
        this.audioCollectorActiveIds = []
        this.audioCollectorOpenIds = []
        this.singleSelectedSongIndex = null

        // 回显音源数据
        this.timerSourceType = timerSelectedElement.source_type == null ? 0 : timerSelectedElement.source_type
        if (this.timerSourceType === 0) {
          // 回显歌曲数据
          this.prepareSongDataSelected(timerSelectedElement.songs);
        } else if (this.timerSourceType === 1) {
          // 回显音频采集数据
          this.audioCollectorSearch = null
          this.audioCollectorActiveIds = []
          this.audioCollectorOpenIds = []
          this.prepareAudioCollectorInfo(timerSelectedElement.audio_collector)
        }
        // 回显设备类型 0：解码终端 1：电源时序器
        this.timerDeviceType = timerSelectedElement.device_type == null ? 0 : timerSelectedElement.device_type

        this.timerDialog = true
      },
      /**
       * 用于编辑定时点前组装已选中的数据
       * 20210904 从树形列表修改为拖拽列表
       */
      prepareSongDataSelected: function (songs) {
        if (songs == null || songs.length === 0) {
          this.selectedSongs = []
          return
        }
        // 依据song_path_name去通过playlist获取歌曲的信息
        const list = []
        out:for (let i = 0; i < songs.length; i++) {
          for (let j = 0; j < this.playList.length; j++) {
            for (let k = 0; k < this.playList[j].songs.length; k++) {
              if (this.playList[j].songs[k].song_path_name === songs[i]) {
                list.push(this.playList[j].songs[k])
                continue out
              }
            }
          }
        }
        this.selectedSongs = list
      },
      // 回显电源时序器数据
      prepareSequencePowerInfo(selectedPowerInfos) {
        if (selectedPowerInfos == null || selectedPowerInfos.length === 0) {
          this.powerTreeSelection = []
          this.lastActiveTreeNode = null
          this.activeTreeNodes = []
          return
        }
        const list = []
        selectedPowerInfos.forEach(info => {
          if (info.channels != null && info.channels.length > 0) {
            const power = this.sequencePowerInfo.find(p => p.device_mac === info.device_mac)
            if (power == null) {
              return
            }
            const channelInfos = power.channels
            info.channels.forEach(channel => {
              const channelInfo = channelInfos.find(i => i.channel_id === channel)
              list.push(
                {
                  id: info.device_mac + '|' + channel,
                  name: channelInfo.name,
                  status: channelInfo.status
                }
              )
            });
          }
        })
        this.lastActiveTreeNode = null
        this.activeTreeNodes = []
        this.powerTreeSelection = list
        // 深拷贝防止对象错误修改
        this.restorePowerTreeSelection = JSON.parse(JSON.stringify(list))
      },
      /**
       * 创建/编辑定时点
       * 1. 前置检查
       * 1.1 定时点名称
       * 1.2 定时点是否有效 （删除 -- 定时点有效只有在定时点编辑中显示，因为同时只能有一个定时点有效）
       * 1.3 日期选择
       * 1.4 时间选择
       * 1.5 分区选择/分组选择
       * 1.6 歌曲选择（播放模式/播放歌曲）
        */
      createOrEditTimer: function (isCreateTimer) {
        // 传入的参数为指示，当前请求为创建定时点，如果为false，则为编辑定时点
        if (this.isEmpty(this.timerName)) {
          this.errorMessages = this.$t('timer.errors.timerNameEmpty')
          this.commonErrorSnackBar = true
          return
        }

        if (!this.daySwitch && !this.dateSwitch) {
          this.errorMessages = this.$t('timer.errors.selectDateScheme')
          this.commonErrorSnackBar = true
          return
        }
        if (this.daySwitch && this.daySelected.length === 0) {
          this.errorMessages = this.$t('timer.errors.weekdayNotSelected')
          this.commonErrorSnackBar = true
          return
        }
        // 只有当创建定时点时不允许开始日期设置的比当前早，在编辑旧定时点时，可以保留旧开始日期
        if (this.dateSwitch && this.date < new Date().toISOString().substr(0, 10) && isCreateTimer) {
          this.errorMessages = this.$t('timer.errors.startDateTooEarly')
          this.commonErrorSnackBar = true
          return
        }

        if (this.dateSwitch && this.date > this.date2) {
          this.errorMessages = this.$t('timer.errors.endDateBeforeStartDate')
          this.commonErrorSnackBar = true
          return
        }

        if (this.time1 == null || this.time2 == null) {
          this.errorMessages = this.$t('timer.errors.startOrEndTimeNotSet')
          this.commonErrorSnackBar = true
          return
        }
        if (this.time1 > this.time2) {
          this.errorMessages = this.$t('timer.errors.endTimeBeforeStartTime')
          this.commonErrorSnackBar = true
          return
        }

        if (this.timerDeviceType ===0) {
          if (this.partitionSelectTransfer.length === 0 && this.groupSelectTransfer.length === 0 ) {
            this.errorMessages = this.$t('timer.errors.needZoneOrGroup')
            this.commonErrorSnackBar = true
            return
          }
          if (this.selectedPlayMode == null) {
            this.errorMessages = this.$t('timer.errors.needPlayMode')
            this.commonErrorSnackBar = true
            return
          }

          if (this.timerSourceType === 0 && this.selectedSongs.length === 0) {
              this.errorMessages = this.$t('timer.errors.noSongsSelected')
              this.commonErrorSnackBar = true
              return
          }
          if (this.timerSourceType === 1 && this.audioCollectorActiveIds.length === 0) {
            this.errorMessages = this.$t('timer.errors.noAudioCollectorSelected')
            this.commonErrorSnackBar = true
            return
          }
        }
        else if (this.timerDeviceType === 1) {
          if(this.selectedPowerNumber === 0) {
            this.errorMessages = this.$t('timer.errors.noPowerSequencerSelected')
            this.commonErrorSnackBar = true
            return
          }
        }

        const zoneMacs = this.partitionSelectTransfer;

        let id = 0
        const timerList = this.schemaSelected[0].timerlists
        if (isCreateTimer) {
          id = timerList.length > 0 ? timerList[timerList.length - 1].id + 1 : 0
        } else {
          // 修改获取定时点id方式为从点击编辑按钮的定时点获取，或者从选中行获取
          id = this.editTimerActionInTable ? this.editTimerActionInTable.id : this.timerSelected[0].id
        }
        const timePoint = {
          id: id,
          name: this.timerName,
          valid: this.timerValid,
          // 修改为跟随设置
          vol_follow_device: this.isVolFollowDevice,
          volume: this.timerVolume,
          play_mode: this.selectedPlayMode,
          play_to_end: false,
          resume_play: false,
          inter_cut: false,
          random_single: false,
          start_time: this.time1,
          end_time: this.time2,
          zone_count: this.partitionSelectTransfer.length,
          group_count: this.groupSelectTransfer.length,
          song_count: this.getTimerSelectedSongLength(),
          sequence_power_count: this.selectedPowerNumber,
          source_type: this.timerSourceType,
          device_type: this.timerDeviceType
        }
        if (this.daySwitch) {
          timePoint.time_mode = 0
          timePoint.weekday = this.daySelected.join('')
        } else if (this.dateSwitch) {
          timePoint.time_mode = 1
          timePoint.start_date = this.date
          timePoint.end_date = this.date2
        }

        if (isCreateTimer) {
          this.createSchemaLoading = true
          this.$ws.addTimerInfo(this.schemaSelected[0].time_scheme_id, this.$ws.getUuid(), timePoint,
                                zoneMacs, this.groupSelectTransfer, this.getAllPathOfSelectSongs(),
                                this.getTimerSequencePowerInfo(), this.audioCollectorActiveIds[0])
        } else {
          this.$ws.editTimerInfo(this.schemaSelected[0].time_scheme_id, this.$ws.getUuid(), timePoint,
                                 zoneMacs, this.groupSelectTransfer, this.getAllPathOfSelectSongs(),
                                  this.getTimerSequencePowerInfo(), this.audioCollectorActiveIds[0])
        }

      },
      // 获取已选择的歌曲的数量
      getTimerSelectedSongLength: function () {
        return this.getAllPathOfSelectSongs().length
      },
      // 获取所有已选择歌曲的绝对路径
      getAllPathOfSelectSongs: function () {
        return this.selectedSongs.length === 0 ? [] : this.selectedSongs.map(m => m.song_path_name)
      },
      // 获取电源定时器信息
      getTimerSequencePowerInfo() {
        if (this.powerTreeSelection.length === 0) {
          return;
        }
        const powerList = [];
        this.powerTreeSelection.forEach(selection => {
          const deviceMac = selection.id.split('|')[0]
          const channelNumber = parseInt(selection.id.split('|')[1])
          const power = powerList.find(power => power.device_mac === deviceMac)
          if (power == null) {
            // 未找到电源时序器，创建
            powerList.push({
              device_mac: deviceMac,
              channels: [channelNumber]
            })
          } else {
            // 找到了电源时序器，加入通道号
            power.channels.push(channelNumber)
            power.channels = power.channels.sort((a,b) => a-b);
          }
        })
        return powerList
      },

      // 获取顺序播放下所有已选择歌曲的总时长
      getAllDurationOfSelectSongs() {
        let durationNumber = 0
        if (this.selectedSongs.length === 0) {
          return durationNumber
        }
        this.selectedSongs.forEach(song => {
          // 每首歌曲加1s
          durationNumber += (song.duration + 1)
        })
        let duration = new Date(durationNumber * 1000).toISOString();
        const hour = duration.substr(11, 2)
        const minute = duration.substr(14, 2)
        const second = duration.substr(17, 2)
        const mandatoryTime = hour + this.$t('timeTips.hours') + minute + this.$t('timeTips.minutes') + second + this.$t('timeTips.seconds')
        if (duration.substr(8, 2) !== '01') {
          // 超过1天的歌曲，显示天数 （注：超过30天的播放时长不支持显示）
          const day = parseInt(duration.substr(8, 2)) - 1
          const dayString = day < 10 ? ('0' + day) : day
          return dayString + this.$t('timeTips.days') + mandatoryTime
        }
        return mandatoryTime
      },
      isEmpty (str) {
        return (!str || str.length === 0)
      },
    },
  }
</script>
