<template>
  <v-app>
    <v-container
      class="fill-height text-center"
      fluid
    >
      <v-row
        align="center"
        justify="center"
      >
        <v-col cols="12">
          <h1 class="text-h1 primary--text">{{ $t('notFound.title') }}</h1>
          <p class="text-h2">{{ $t('notFound.message') }}</p>
          <v-btn
            elevation="0"
            class="text-h3 primary--text v-btn--outlined"
            @click="goToDashboard"
            >
              {{ $t('notFound.backToControl') }}
          </v-btn>
        </v-col>
      </v-row>
    </v-container>
  </v-app>
</template>

<script>
    export default {
      name: "NotFound",
      methods: {
        goToDashboard() {
          this.$router.push({ path: '/dashboard/dashboard' })
        }
      },
    }

</script>

<style scoped>
  .primary--text {
    color: #1867c0 !important;
    caret-color: #1867c0 !important;
  }
</style>
