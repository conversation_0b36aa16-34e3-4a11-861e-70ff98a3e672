import Vue from 'vue'
import Vuetify from 'vuetify'
import i18n from '@/i18n'
import '@/sass/overrides.sass'
import { primaryColor } from './websocket'

Vue.use(Vuetify)

const theme = {
  primary: primaryColor,
  secondary: '#4CAF50',
  accent: '#9C27b0',
  info: '#00CAE3',
}

export default new Vuetify({
  lang: {
    t: (key, ...params) => i18n.t(key, params),
  },
  theme: {
    themes: {
      dark: theme,
      light: theme,
      options: { customProperties: true },
    },
  },
})
