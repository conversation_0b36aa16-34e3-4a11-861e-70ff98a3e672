<template>
  <div>
    <!--通用successSnackBar-->
    <v-snackbar
      v-model="commonSuccessSnackBar"
      color="primary"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ successMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="primary" fab small class="ml-6" v-bind="attrs" @click="commonSuccessSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--通用errorSnackBar-->
    <v-snackbar
      v-model="commonErrorSnackBar"
      color="error"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="error" fab small class="ml-6" v-bind="attrs" @click="commonErrorSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <v-dialog v-model="editMonitorDialog" max-width="500px" transition>
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('monitor.monitorSettings') }}</span>
        </v-card-title>

        <v-card-text>
          <v-text-field
            v-show="singleEditFlag && !isAddCustomMonitor"
            v-model="editingMonitorIp"
            :label="$t('monitor.ipAddress')"
            class="pt-6"
            hide-details
            required
            disabled
          />
          <v-text-field
            v-show="singleEditFlag && !isAddCustomMonitor"
            v-model="editingMonitorMac"
            label="MAC"
            class="pt-6"
            hide-details
            required
            disabled
          />
          <v-text-field
            v-show="singleEditFlag || isAddCustomMonitor"
            v-model="editingMonitorName"
            :label="$t('monitor.cameraName')"
            class="pt-6"
            hide-details
            required
          />
          <v-text-field
            v-show="singleEditFlag || isAddCustomMonitor"
            v-model="customeMonitorRTSP"
            :label="$t('monitor.rtspAddress')"
            class="pt-6"
            hide-details
            required
            :disabled="singleEditFlag && editingMonitorIsAutoAdd"
          />
          <v-text-field
            v-show="!isAddCustomMonitor && editingMonitorIsAutoAdd"
            v-model="editingMonitorAccount"
            :label="$t('monitor.username')"
            class="pt-6"
            hide-details
            required
          />
          <v-text-field
            v-show="!isAddCustomMonitor && editingMonitorIsAutoAdd"
            v-model="editingMonitorPwd"
            :append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
            :type="showPassword ? 'text' : 'password'"
            placeholder="*********"
            @click:append="showPassword = !showPassword"
            :label="$t('monitor.password')"
            class="pt-6"
            hide-details
            required
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="editMonitorDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            v-show="singleEditFlag || isAddCustomMonitor"
            color="blue darken-1"
            text
            @click="setSingleMonitorInfo(editingMonitorMac, editingMonitorAccount, editingMonitorPwd, editingMonitorName, customeMonitorRTSP)"
          >
            {{ $t('common.confirm') }}
          </v-btn>
          <v-btn
            v-show="!singleEditFlag && !isAddCustomMonitor"
            color="blue darken-1"
            text
            @click="setMultipleMonitorInfo"
          >
            {{ $t('monitor.actions.batchSet') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--删除设备-->
    <v-dialog
      v-model="deleteDeviceDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('monitor.deleteMonitorDevice') }}
        </v-card-title>
        <v-card-text class="text-center">
          <v-container>
            <v-row dense>
              <v-col cols="12">
                <span style="padding-right: 10px">{{ $t('monitor.aboutToDeleteMonitorDevice') }}</span>
                <span
                  class="font-weight-black text-decoration-underline"
                  style="padding-right: 10px"
                >{{ this.deleteMonitorName }}</span>
                <span>, {{ $t('common.pleaseConfirm') }}</span>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteDeviceDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="deleteSingleDevice()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="confirmMonitorSwitchDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('monitor.videoMonitorSwitch') }}
        </v-card-title>
        <v-card-text class="text-center">
          <v-container>
            <v-row dense>
              <template>
                <v-col cols="12">
                  <span style="padding-right: 10px">{{ $t('monitor.messages.aboutTo') }}{{ localIsEnableMonitor ? $t('monitor.actions.enable') : $t('monitor.actions.disable') }}{{ $t('monitor.messages.videoMonitoringFunction') }}，</span>
                  <span>{{ $t('common.pleaseConfirm') }}</span>
                  <span></span>
                </v-col>
                <v-col cols="12">
                  <span style="color: red; padding-right: 10px;">{{ $t('monitor.messages.restartWarning') }}</span>
                </v-col>
              </template>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="cancelMonitorSwitch"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="toggleMonitorSwitch"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--主显示页面-->
    <v-container
      id="Monitors"
      fluid
      class="mt-n2"
    >
      <v-card>
        <v-card-title style="margin-top: -20px">
          <!--按钮栏-->
          
          <span style="padding-left: 30px" />

          <v-switch style="padding-bottom: 14px"
            v-model="localIsEnableMonitor"
            :label="$t('monitor.videoMonitorSwitch')"
            hide-details
            dense
            @click="confirmMonitorSwitchDialog=true"
          />
          <span style="padding-left: 80px" />
          
          <v-btn
            v-show="monitorSelected.length === 0"
            color="primary"
            @click="addCustomMonitor"
          >
            {{ $t('monitor.addCustomMonitorDevice') }}
          </v-btn>

          <v-btn
            v-show="monitorSelected.length === 1"
            color="primary"
            @click="singleEditItem"
          >
            {{ $t('monitor.editMonitor') }}
          </v-btn>
          <v-btn
            v-show="monitorSelected.length > 1"
            color="primary"
            @click="multipleEditItem"
          >
            {{ $t('monitor.batchEdit') }}
          </v-btn>
          <v-spacer />
          <v-text-field
            v-model="monitorSearch"
            append-icon="mdi-magnify"
            :label="$t('monitor.search')"
            single-line
            hide-details
            full-width
            style="padding-bottom: 35px"
          />
        </v-card-title>
        <v-data-table
          v-if="isEnableMonitor"
          v-model="monitorSelected"
          :headers="monitorHeaders"
          :items="selectableMonitor"
          :search="monitorSearch"
          item-key="monitor_mac"
          :item-class="itemRowBackground"
          multi-sort
          class="elevation-1"
          show-select
          :loading="monitors.length === 0"
          :loading-text="$t('monitor.messages.noMonitorDevices')"
          style="margin-top: -10px"
          :items-per-page="10"
          @click:row="rowClick"
          :footer-props="{
            itemsPerPageOptions: [10,25,50],
            showFirstLastPage: true,
            showCurrentPage: true,
            firstIcon: 'mdi-arrow-collapse-left',
            lastIcon: 'mdi-arrow-collapse-right',
            prevIcon: 'mdi-minus',
            nextIcon: 'mdi-plus'
          }"
        >
          <template v-slot:item.port="{ item }">
            <span>{{ monitors.map(function(x) {return x.monitor_mac; }).indexOf(item.monitor_mac) + 1 }}</span>
          </template>
          <template v-slot:item.is_auto_add="{ item }">
            <span> {{ getMonitorAddType(item.is_auto_add) }}</span>
          </template>
          <template v-slot:item.monitor_status="{ item }">
            <v-chip :color="getColorOfMonitorStatus(item.monitor_status)" dark>{{ getMonitorStatus(item.monitor_status) }}</v-chip>
          </template>
          <template v-slot:item.monitor_mac="{ item }">
            <span> {{ item.monitor_mac.toUpperCase() }} </span>
          </template>

          <template v-slot:item.actions="{ item }">
            <v-icon
              large
              class="mr-2"
              :title="$t('common.edit')"
              :disabled="item.monitor_status === -1"
              @click.stop.prevent="editItem(item)"
            >
              mdi-pencil
            </v-icon>
            <v-icon
              large
              :title="$t('common.delete')"
              :disabled="item.monitor_status !== -1 && item.monitor_status !== 2"
              @click.stop.prevent="preDeleteDevice(item)"
              class="mx-1"
            >
              mdi-delete
            </v-icon>
          </template>
        </v-data-table>
      </v-card>
    </v-container>
  </div>
</template>

<script>
  import { mapState } from 'vuex'

  const deviceStatus = new Map([
    ['-1', 'offline'],
    ['0', 'loggedIn'],
    ['1', 'loginFailed'],
    ['2', 'unknown'],
  ])
  export default {
    name: 'Monitor',
    data: () => ({
      successMessages: '',
      commonSuccessSnackBar: false,
      errorMessages: '',
      commonErrorSnackBar: false,
      snackbarTimeout: 1500,
      monitorSelected: [], // 已选择的摄像头
      monitorSearch: '',
      editMonitorDialog: false,
      // 表格显示头部
      monitorHeaders: [],
      localIsEnableMonitor: false,
      confirmMonitorSwitchDialog: false,
      isAddCustomMonitor: false,
      customeMonitorRTSP: '',
      deleteItem: {}, // 删除的单个摄像头对象
      deleteMonitorName: null,
      deleteMonitorMac: null,
      deleteDeviceDialog: false, //删除设备对话框
      editedItem: {}, // 修改的单个摄像头对象
      editingMonitorMac: null,
      editingMonitorAccount: null,
      editingMonitorPwd: null,
      editingMonitorName: null,
      editingMonitorIp: null,
      editingMonitorIsAutoAdd: false,
      singleEditFlag: true, // 切换单个编辑和批量编辑
      showPassword: false,
    }),
    computed: {
      ...mapState(['errorId', 'errorWsMessage', 'monitors', 'setMonitorInfoResult','addCustomMonitorResult', 'delMonitorInfoResult', 'isEnableMonitor', 'switchMonitorResult',
                  'switchMonitorType']),
      selectableMonitor () {
        return this.monitors.map(x => ({ ...x, isSelectable: x.monitor_status !== -1 }))
      },
    },
    watch: {
      // 监听语言变化
      '$i18n.locale'() {
        this.initializeTranslations()
      },
      // 统一错误处理
      errorId: function () {
        if (this.$route.fullPath !== '/dashboard/monitor') {
          return;
        }
        if (this.$store.state.errorId !== null) {
          this.errorMessages = this.$store.state.errorWsMessage
          this.commonErrorSnackBar = true
        }
      },
      isEnableMonitor () {
        this.localIsEnableMonitor = this.isEnableMonitor
      },
      setMonitorInfoResult () {
        if (this.setMonitorInfoResult !== 0) {
          return
        }
        this.successMessages = this.$t('monitor.messages.setMonitorInfoSuccess')
        this.commonSuccessSnackBar = true
        this.editMonitorDialog = false
        this.$store.commit('updateSetMonitorInfoResult', null)
      },
      addCustomMonitorResult () {
        if (this.addCustomMonitorResult !== 0) {
          return
        }
        this.successMessages = this.$t('monitor.messages.addCustomMonitorSuccess')
        this.commonSuccessSnackBar = true
        this.editMonitorDialog = false
        this.isAddCustomMonitor = false
        this.$store.commit('updateAddCustomMonitorResult', null)
      },
      delMonitorInfoResult () {
        if (this.delMonitorInfoResult !== 0) {
          return
        }
        this.successMessages = this.$t('monitor.messages.deleteMonitorSuccess')
        this.commonSuccessSnackBar = true
        this.deleteDeviceDialog = false
        this.$store.commit('updateDelMonitorInfoResult', null)
      },
      switchMonitorResult() {
        if (this.switchMonitorResult === 0) {
          const prefix = this.switchMonitorType ? this.$t('monitor.actions.enable') : this.$t('monitor.actions.disable')
          this.successMessages = this.$t('monitor.messages.videoMonitoringFunction') + prefix + this.$t('monitor.messages.enabled')
          this.commonSuccessSnackBar = true
          this.$store.commit('updateSwitchMonitorResult', null)
          this.confirmMonitorSwitchDialog = false
        }
      },
    },
    mounted () {
      this.initializeTranslations()
      this.localIsEnableMonitor = this.isEnableMonitor
    },
    methods: {
      initializeTranslations() {
        // 初始化消息
        this.successMessages = this.$t('common.operationSuccess')
        this.errorMessages = this.$t('common.operationFailed')

        // 初始化表格头部
        this.monitorHeaders = [
          { text: this.$t('monitor.table.serialNumber'), align: 'center', value: 'port', sortable: false },
          { text: this.$t('monitor.table.joinMethod'), value: 'is_auto_add', align: 'center', sortable: true },
          { text: this.$t('monitor.table.cameraName'), value: 'name', align: 'center', sortable: false },
          { text: this.$t('monitor.table.ipAddress'), value: 'ip_addr', align: 'center', sortable: false },
          { text: this.$t('monitor.table.mac'), value: 'monitor_mac', align: 'center', sortable: false },
          { text: this.$t('monitor.table.username'), value: 'account', align: 'center', sortable: false },
          { text: this.$t('monitor.table.status'), value: 'monitor_status', align: 'center', sortable: false },
          { text: this.$t('monitor.table.actions'), value: 'actions', sortable: false },
        ]
      },
      cancelMonitorSwitch() {
        this.localIsEnableMonitor = !this.localIsEnableMonitor
        this.confirmMonitorSwitchDialog = false
      },
      toggleMonitorSwitch() {
        let newValue=this.localIsEnableMonitor
        // console.log('监控开关状态:', newValue ? '开启' : '关闭');
        if(newValue) {
          this.$ws.switchMonitor(true)
        } else {
          this.$ws.switchMonitor(false)
        }
      },
      // 离线摄像头显示灰色
      itemRowBackground: function (item) {
        if (this.$vuetify.theme.dark) {
          return item.monitor_status === -1 ? 'darkOfflineZone' : '';
        }
        return item.monitor_status === -1 ? 'offlineZone' : 'onlineZone';
      },
      // 主控制端点击即可选中一行
      rowClick: function (item, row) {
        // 取消离线提示
        if (item.monitor_status === -1) {
          return
        }
        if (row.isSelected) {
          row.select(false)
        } else {
          row.select(true)
        }
      },
      // 获取运行状态
      getMonitorStatus: function (status) {
        const s = status + ''
        const statusKey = deviceStatus.get(s)
        return statusKey ? this.$t(`monitor.status.${statusKey}`) : this.$t('monitor.status.unknown')
      },
      // 获取添加方式
      getMonitorAddType: function (is_auto_add) {
        // console.log("is_auto_add="+is_auto_add)
        return is_auto_add ? this.$t('monitor.addMethod.auto') : this.$t('monitor.addMethod.manual')
      },
      // 添加自定义监控设备
      addCustomMonitor() {
        this.editingMonitorName = ''
        this.customeMonitorRTSP = ''
        this.isAddCustomMonitor = true
        this.editingMonitorIsAutoAdd = false
        this.singleEditFlag = false
        this.editMonitorDialog = true
      },


      preDeleteDevice (item) {
        this.deleteItem = Object.assign({}, item)
        this.deleteMonitorName = item.name
        this.deleteMonitorMac = item.monitor_mac
        this.deleteDeviceDialog = true
      },
      deleteSingleDevice() {
        // 组织editMonitor对象
        const delMonitor = {
          monitor_mac: this.deleteMonitorMac
        }
        const delMonitors = []
        delMonitors.push(delMonitor)
        this.$ws.delMonitorInfo(delMonitors.length, delMonitors)
      },
      editItem (item) {
        this.editedItem = Object.assign({}, item)
        this.prepareSingleEditDialog(item)
      },
      singleEditItem () {
        const item = this.monitorSelected[0]
        this.prepareSingleEditDialog(item)
      },
      prepareSingleEditDialog (item) {
        this.editingMonitorAccount = item.account
        this.editingMonitorIsAutoAdd = item.is_auto_add // false
        this.editingMonitorPwd = item.password
        this.editingMonitorMac = item.monitor_mac != null ? item.monitor_mac.toUpperCase() : ''
        this.editingMonitorName = item.name
        this.editingMonitorIp = item.ip_addr
        this.singleEditFlag = true
        this.showPassword = false

        this.customeMonitorRTSP = item.url
        this.isAddCustomMonitor = false

        this.editMonitorDialog = true
      },
      multipleEditItem () {
        this.singleEditFlag = false
        this.editingMonitorAccount = this.monitorSelected[0].account
        this.editingMonitorIsAutoAdd = this.monitorSelected[0].is_auto_add
        this.editingMonitorPwd = this.monitorSelected[0].password
        this.showPassword = false
        this.isAddCustomMonitor = false
        this.editMonitorDialog = true
      },
      setSingleMonitorInfo (mac, account, pwd, name, url) {
        if(!this.isAddCustomMonitor) {
          if (this.isEmpty(mac) || this.isEmpty(account) || this.isEmpty(pwd) || this.isEmpty(name)) {
            this.errorMessages = this.$t('monitor.errors.requiredFieldsEmpty')
            this.commonErrorSnackBar = true
            return
          }
          if(!this.editingMonitorIsAutoAdd && this.isEmpty(url))
          {
            if (this.isEmpty(name) || this.isEmpty(url)) {
              this.errorMessages = this.$t('monitor.errors.requiredFieldsEmpty')
              this.commonErrorSnackBar = true
              return
            }
          }
          const editMonitor = {
            monitor_mac: mac.toLowerCase(),
            account: account,
            password: pwd,
            name: name,
            url: url
          }
          this.$ws.setMonitorInfo(1, editMonitor)
        }
        else{
          if (this.isEmpty(name) || this.isEmpty(url)) {
            this.errorMessages = this.$t('monitor.errors.requiredFieldsEmpty')
            this.commonErrorSnackBar = true
            return
          }
          if(!url.startsWith("rtsp://")) {
            this.errorMessages = this.$t('monitor.errors.invalidRTSPAddress')
            this.commonErrorSnackBar = true
            return
          }
          this.$ws.addCustomMonitor(name, url)
        }
      },
      setMultipleMonitorInfo () {
        if (this.isEmpty(this.editingMonitorAccount) || this.isEmpty(this.editingMonitorPwd)) {
          this.errorMessages = this.$t('monitor.errors.requiredFieldsEmpty')
          this.commonErrorSnackBar = true
          return
        }
        // 组织editMonitor对象
        const setMonitors = []
        this.monitorSelected.forEach(monitor => {
          const setMonitor = {
            monitor_mac: monitor.monitor_mac.toLowerCase(),
            account: this.editingMonitorAccount,
            password: this.editingMonitorPwd,
            name: monitor.name,
          }
          setMonitors.push(setMonitor)
        })
        this.$ws.setMonitorInfo(setMonitors.length, setMonitors)
      },
      isEmpty (str) {
        return (!str || str.length === 0)
      },
      // 设置监控不同状态的颜色显示
      getColorOfMonitorStatus (status) {
        if (status === 0) {
          return 'primary'
        }
        if (status === 1) {
          return 'red'
        }
        if (status === -1) {
          return 'grey'
        }
        return ''
      },
    },
  }
</script>

<style scoped>
  /deep/ tr.v-data-table__selected {
    background: #c2c9f3 !important;
  }
</style>
