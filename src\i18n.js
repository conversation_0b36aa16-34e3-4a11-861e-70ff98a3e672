import Vue from 'vue'
import VueI18n from 'vue-i18n'

import en from 'vuetify/lib/locale/en'
import zh from 'vuetify/lib/locale/zh-Hans'
import zhHant from 'vuetify/lib/locale/zh-Hant'

Vue.use(VueI18n)

const messages = {
  // 简体中文
  zh: {
    ...require('@/locales/zh.json'),
    $vuetify: zh,
  },
  // 英文
  en: {
    ...require('@/locales/en.json'),
    $vuetify: en,
  },
  // 繁体中文
  'zh-TW': {
    ...require('@/locales/zh-TW.json'),
    $vuetify: zhHant,
  },
}

export default new VueI18n({
  locale: process.env.VUE_APP_I18N_LOCALE || 'zh',
  fallbackLocale: process.env.VUE_APP_I18N_FALLBACK_LOCALE || 'zh',
  messages,
  silentTranslationWarn: true,
})
