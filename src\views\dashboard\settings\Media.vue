<template>
  <div>
    <!--通用successSnackBar-->
    <v-snackbar
      v-model="commonSuccessSnackBar"
      color="primary"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ successMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="primary" fab small class="ml-6" v-bind="attrs" @click="commonSuccessSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--通用errorSnackBar-->
    <v-snackbar
      v-model="commonErrorSnackBar"
      color="error"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="error" fab small class="ml-6" v-bind="attrs" @click="commonErrorSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!-- 对话框 -->
    <v-dialog
      v-model="createListDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('media.createNewList') }}</span>
        </v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col
                cols="12"
              >
                <v-form ref="newListRef" v-model="newListNameValid">
                  <v-text-field
                    ref="newListName"
                    :value="newListName"
                    :label="$t('media.enterNewListName')"
                    :placeholder="$t('media.enterNewListName')"
                    required
                    counter="20"
                    height="50px"
                    :rules="newListNameRules"
                    @change="v => newListName = v"
                    @keydown.enter="createNewList"
                  />
                </v-form>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="createListDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="createNewList"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="renameListDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('media.renameList') }}</span>
        </v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col
                cols="12"
              >
                <v-form ref="changeListNameRef" v-model="replaceListNameValid">
                  <v-text-field
                    :value="selectedListInTree.length === 0 ? '' :  selectedListInTree[0].list_name"
                    :label="$t('media.enterNewListName')"
                    required
                    counter="20"
                    height="50px"
                    :rules="commonRules"
                    @keydown.enter="renameList"
                    @change="v => replacedListName = v"
                  />
                </v-form>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="renameListDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="renameList"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="deleteListDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('media.deleteList') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('media.aboutToDeleteList') }}</span>
          <span class="font-weight-black text-decoration-underline">{{ selectedListInTree.length === 0 ? '' : selectedListInTree[0].list_name }}</span>
          <span>,{{ $t('timer.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteListDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="deleteList"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--添加歌曲-->
    <v-dialog
      v-model="addSongDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('media.addSong') }}</span>
        </v-card-title>

        <!-- 添加选项卡 -->
        <v-tabs v-model="addSongTab">
          <v-tab>{{ $t('media.tabs.selectFromLibrary') }}</v-tab>
          <v-tab v-if="IsAddSongToListShowAddNewSong()">{{ $t('media.tabs.uploadNewSong') }}</v-tab>
        </v-tabs>

        <v-tabs-items v-model="addSongTab">
          <!-- 从曲库选择歌曲的选项卡 -->
          <v-tab-item>
            <v-card flat>
              <v-card-subtitle class="pt-9 text-lg-h3 text-center">
                <span v-html="addSongToListDialogTitle"></span>
              </v-card-subtitle>
              <div class="pt-3 text-center mx-4">
                <el-transfer
                  v-model="addSongTransfer"
                  filterable
                  :filter-placeholder="$t('media.enterSongKeyword')"
                  :data="songListForAddingToList"
                  :props="{
                    key: 'song_path_name',
                  }"
                  :titles="[$t('media.songList'), $t('media.selectedSongs')]"
                >
                  <span slot-scope="{ option }">{{ getSongNameFromPathAndRemoveSuffix(option.song_path_name) }}</span>
                </el-transfer>
              </div>
            </v-card>
          </v-tab-item>

          <!-- 上传新歌曲的选项卡 -->
          <v-tab-item>
            <v-card flat>
              <v-card-text>
                <v-file-input
                  v-model="files"
                  :accept="'.wav, .mp3'"
                  :max-files="40"
                  show-size
                  counter
                  small-chips
                  multiple
                  :label="$t('media.selectSongsToUpload')"
                  style="width: 50vh"
                />

                <v-progress-circular
                  v-if="uploadSongProgress !== 0"
                  class="ml-16 mt-5"
                  :rotate="0"
                  :size="100"
                  :width="25"
                  :value="uploadSongProgress"
                  color="primary"
                >
                  {{ uploadSongProgress }}
                </v-progress-circular>
              </v-card-text>
            </v-card>
          </v-tab-item>
        </v-tabs-items>

        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="addSongDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            :loading="uploadLoading"
            :disabled="!canConfirmAddSong"
            @click="confirmAddSong"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="deleteSongDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('media.deleteSong') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('media.aboutToDeleteSelectedSongs') }}</span>
          <span class="font-weight-black text-decoration-underline">
            {{ isRemoveSongFromMedia ? songSelected.length : selectedSongsInTree.length }}
          </span>
          <span>{{ $t('media.songsConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteSongDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="removeSong()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="auditSongDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('media.auditSong') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('media.aboutToAuditSelected') }}</span>
          <span class="font-weight-black text-decoration-underline">
            {{ songSelected.length }}
          </span>
          <span>{{ $t('media.songsConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="auditSongDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="auditSong()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="deleteSongActionInTableDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('media.deleteSong') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('media.aboutToDeleteSong') }}</span>
          <span class="font-weight-black text-decoration-underline">
            {{ deletedSongActionInTable ? getSongNameFromPath(deletedSongActionInTable.song_path_name) : '' }}
          </span>
          <span>,{{ $t('timer.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteSongActionInTableDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="removeSingleSongFromMedia()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--下载歌曲-->
    <v-dialog
      v-model="downloadSongDialog"
      max-width="400"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('media.downloadSong') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('media.aboutToDownloadSelected') }}</span>
          <span class="font-weight-black text-decoration-underline">
            {{ songSelected.length }}
          </span>
          <span>{{ $t('media.songsConfirm') }}</span>
          <p class="red--text text--darken-4">{{ $t('media.downloadTip') }}</p>
        </v-card-text>
        <v-card-actions class="mt-n6">
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="downloadSongDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="downloadSelectedSongs()"
          >
            {{ $t('media.downloadSelectedSongs') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="createSongDialog"
      width="unset"
      transition
      persistent
    >
      <v-card>
        <v-card-title class="pb-6">
          <span class="headline">{{ $t('media.uploadSong') }}</span>
        </v-card-title>

        <v-file-input
          v-model="files"
          :accept="'.wav, .mp3'"
          :max-files="40"
          show-size
          counter
          small-chips
          multiple
          :label="$t('media.upload.selectSongsLabel')"
          style="width: 50vh"
        />

        <v-progress-circular
          v-if="uploadSongProgress !== 0"
          class="ml-16 mb-n10 mt-10"
          :rotate="0"
          :size="100"
          :width="25"
          :value="uploadSongProgress"
          color="primary"
        >
          {{ uploadSongProgress }}
        </v-progress-circular>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="createSongDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            :loading="uploadLoading"
            :disabled="this.files.length == 0"
            @click="submitFiles"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--主显示页面-->
    <v-container
      id="media"
      fluid
      class="mt-n2"
    >
      <v-row
        dense
      >
        <!--选项卡切换-->
        <v-col cols="12">
          <v-tabs
            v-model="tab"
            icons-and-text
            style="background-color: transparent"
          >
            <v-tabs-slider />
            <v-tab
              v-for="i in tabList"
              :key="i.id"
              :href="`#tab-${i.id}`"
              @click="setCurrentTab(i)"
            >
              {{ i.name }}
              <v-icon>{{ i.icon }}</v-icon>
            </v-tab>
            <v-tabs-items v-model="tab">
              <!--列表管理-->
              <v-tab-item :value="'tab-' + 1">
                <v-card>
                  <v-row>
                    <v-col cols="3" class="treeViewStyle">
                      <v-btn
                        color="primary"
                        @click="createListDialog = true"
                        class="ml-1"
                      >
                        {{ $t('media.createNewList') }}
                      </v-btn>
                      <v-btn
                        color="primary"
                        @click="renameListDialog = true"
                        class="ml-1"
                        :disabled="!checkIfListOperPermission()"
                      >
                        {{ $t('media.renameList') }}
                      </v-btn>
                      <v-btn
                        color="primary"
                        @click="deleteListDialog = true"
                        class="ml-1"
                        :disabled="!checkIfListOperPermission()"
                      >
                        {{ $t('media.deleteList') }}
                      </v-btn>
                      <v-text-field
                        v-model="treeViewSearch"
                        :label="$t('media.search')"
                        flat
                        light
                        hide-details
                        clearable
                        clear-icon="mdi-close-circle-outline"
                        class="ml-2"
                      />
                      <v-treeview
                        v-model="selectedListInTree"
                        item-key="list_id"
                        item-text="list_name"
                        item-children="children"
                        :items="playList"
                        selected-color="primary"
                        return-object
                        open-all
                        activatable
                        :filter="treeViewFilter"
                        :search="treeViewSearch"
                        :active.sync="activeTreeNodes"
                      ></v-treeview>
                    </v-col>
                    <v-col
                      cols="9"
                      class="px-0 pb-0 elevation-1"
                    >
                      <v-card-title class="mt-n4">
                      <v-switch
                        v-model="isAllowDrag"
                        :label="$t('media.enableSongSorting')"
                        :disabled="!checkIfListSortPermission()"
                      />
                      <v-btn
                        color="primary"
                        @click="preAddSongsToList"
                        class="ml-3"
                        v-show="!isAllowDrag"
                        :disabled="!checkIfListOperPermission()"
                      >
                        {{ $t('media.addSong') }}
                      </v-btn>
                      <v-btn
                        color="primary"
                        @click="preRemoveSongFromList"
                        class="ml-1"
                        v-show="!isAllowDrag"
                        :disabled="!checkIfSongDelOperPermission()"
                      >
                        {{ $t('media.deleteSong') }}
                      </v-btn>
                      <div v-show="isAllowDrag">
                        <v-btn
                          color="primary darken-1"
                          text
                          disabled
                          max-width="60px"
                          class="mx-12"
                          style="font-weight: 700"
                        >
                          {{ $t('media.dragOrSelectToSort') }}
                        </v-btn>
                        <v-btn
                          color="primary"
                          class="mx-2"
                          @click="sortSongByName"
                        >
                          {{ $t('media.sortByName') }}
                        </v-btn>
                        <v-btn
                          color="primary"
                          class="mx-2"
                          @click="sortSongByDuration"
                        >
                          {{ $t('media.sortByDuration') }}
                        </v-btn>
                        <v-btn
                          fab
                          color="primary"
                          class="mx-3"
                          :title="$t('media.moveUpSelected')"
                          @click="zoneMoveUp"
                          :disabled="songSelectedForSortIndex == null || songSelectedForSortIndex === 0"
                        >
                          <v-icon size="30">mdi-arrow-up-bold</v-icon>
                        </v-btn>
                        <v-btn
                          fab
                          color="primary"
                          class="mx-3"
                          :title="$t('media.moveDownSelected')"
                          @click="zoneMoveDown"
                          :disabled="songSelectedForSortIndex == null || songSelectedForSortIndex >= sortItemPerPage - 1 || songSelectedForSortIndex >= songListForSort.length - 1"
                        >
                          <v-icon size="30">mdi-arrow-down-bold</v-icon>
                        </v-btn>
                        <v-btn
                          fab
                          color="primary"
                          class="mx-3"
                          :title="$t('media.cancelSortChanges')"
                          @click="revertSorting"
                          :disabled="!isSongSortChange"
                        >
                          <v-icon size="30">mdi-undo</v-icon>
                        </v-btn>
                        <v-btn
                          fab
                          color="primary"
                          class="mx-3"
                          :title="$t('media.confirmSortChanges')"
                          @click="confirmSorting"
                          :disabled="!isSongSortChange"
                        >
                          <v-icon size="30">mdi-check-bold</v-icon>
                        </v-btn>
                      </div>
                      </v-card-title>
                      <v-text-field
                        v-model="songSearchInTree"
                        :label="$t('common.search')"
                        flat
                        light
                        hide-details
                        clearable
                        clear-icon="mdi-close-circle-outline"
                        class="mx-3 mt-n3"
                      />
                      <!--列表视图-->
                      <!-- 开启排序时的表格 -->
                      <v-data-table
                        v-if="isAllowDrag"
                        :headers="headersForSort"
                        :items="songListForSort"
                        :disable-sort="true"
                        item-key="song_md5"
                        class="elevation-1 mt-2"
                        :loading="songListForSort.length === 0"
                        :loading-text="$t('media.noSongsInSelectedList')"
                        style="margin-top: -10px"
                        :items-per-page="15"
                        :page.sync="tablePageForSort"
                        ref="mySongTable"
                        @update:items-per-page="updateSortItemPerPage"
                        :footer-props="{
                          itemsPerPageOptions: [15,50,100,-1],
                          showFirstLastPage: true,
                          showCurrentPage: true,
                          firstIcon: 'mdi-arrow-collapse-left',
                          lastIcon: 'mdi-arrow-collapse-right',
                          prevIcon: 'mdi-minus',
                          nextIcon: 'mdi-plus'
                        }"
                      >
                        <template #body="props">
                          <draggable
                            :list="props.items"
                            tag="tbody"
                            :disabled="!isAllowDrag"
                            :move="onMoveCallback"
                            :force-fallback="true"
                            group="song"
                            animation="150"
                            delay="50"
                            @change="onSongSortChange(props.items)"
                            :fallback-tolerance="1"
                            :scroll-sensitivity="250"
                            chosenClass="chosen-zone-sort"
                          >
                            <data-table-row-for-song-handler
                              v-for="(item, index) in props.items"
                              :key="item.song_md5"
                              :item="item"
                              :headers="songListHeaders"
                            >
                              <!--id，名称，ip，分区状态，音量-->
                              <template v-slot:item.id="{ item }">
                                <span>{{ index + 1 }}</span>
                              </template>
                              <template v-slot:item.song_path_name="{ item }">
                                <span> {{ getSongNameFromPath(item.song_path_name) }} </span>
                              </template>
                              <template v-slot:item.duration="{ item }">
                                <span> {{ getSongDuration(item.duration) }} </span>
                              </template>
                              <template v-slot:item.alive="{ item }">
                                <span>{{ item.alive ? $t('media.normal') : $t('media.invalid') }}</span>
                              </template>
                              <template v-slot:no-data>
                                <span>{{ selectedListInTree.length === 0 ? $t('media.noListSelected') : $t('media.noSongsInSelectedList') }}</span>
                              </template>
                            </data-table-row-for-song-handler>
                          </draggable>
                        </template>
                      </v-data-table>
                      <!-- 不开启排序时的表格 -->
                      <v-data-table
                        v-else
                        v-model="selectedSongsInTree"
                        :headers="songListHeaders"
                        :items="groupSongsInTree"
                        :search="songSearchInTree"
                        :disable-sort="isTableSortingDisabled"
                        :sort-by.sync="sortBy"
                        item-key="id"
                        :item-class="itemRowBackground"
                        multi-sort
                        show-select
                        class="mt-1 mx-2 px-2"
                        :items-per-page="15"
                        @click:row="rowClick"
                        :page.sync="pageInTree"
                        fixed-header
                        height="66vh"
                        @update:sort-by="resetSelectedSongs"
                        :footer-props="{
                          itemsPerPageOptions: [15,50,100],
                          showFirstLastPage: true,
                          showCurrentPage: true,
                          firstIcon: 'mdi-arrow-collapse-left',
                          lastIcon: 'mdi-arrow-collapse-right',
                          prevIcon: 'mdi-minus',
                          nextIcon: 'mdi-plus'
                        }"
                      >
                        <!--id，名称，ip，分区状态，音量-->
                        <template v-slot:item.song_path_name="{ item }">
                          <span> {{ getSongNameFromPath(item.song_path_name) }} </span>
                        </template>
                        <template v-slot:item.duration="{ item }">
                          <span> {{ getSongDuration(item.duration) }} </span>
                        </template>
                        <template v-slot:item.alive="{ item }">
                          <span>{{ item.alive ? $t('media.normal') : $t('media.invalid') }}</span>
                        </template>
                        <template v-slot:no-data>
                          <span>{{ selectedListInTree.length === 0 ? $t('media.noListSelected') : $t('media.noSongsInSelectedList') }}</span>
                        </template>
                      </v-data-table>
                    </v-col>
                  </v-row>
                </v-card>
              </v-tab-item>
              <!--曲库管理-->
              <v-tab-item :value="'tab-' + 2">
                <v-card>
                  <v-card-title style="margin-top: -20px">
                    <!--按钮栏-->
                    <v-btn
                      color="primary"
                      @click="preUploadSongs"
                      class="ml-0 mr-2"
                    >
                      {{ $t('media.actions.uploadSong') }}
                    </v-btn>
                    <v-btn
                      :disabled="songSelected.length === 0"
                      color="primary"
                      @click="preRemoveSongFromMedia"
                      class="mx-2"
                    >
                      {{ $t('media.deleteSong') }}
                    </v-btn>
                    <v-btn
                      color="primary"
                      class="mx-2"
                      :disabled="songSelected.length === 0"
                      @click="preAuditingSong"
                      v-show="isShowAuditSong()"
                    >
                      {{ $t('media.actions.auditSong') }}
                    </v-btn>
                    <v-btn
                      color="primary"
                      class="mx-2"
                      :disabled="songSelected.length === 0"
                      @click="downloadSongDialog = true"
                      v-show="false"
                    >
                      {{ $t('media.actions.downloadSong') }}
                    </v-btn>
                    <v-spacer />
                    <v-text-field
                      v-model="songSearch"
                      append-icon="mdi-magnify"
                      :label="$t('common.search')"
                      single-line
                      hide-details
                      full-width
                      style="padding-bottom: 35px"
                    />
                  </v-card-title>
                  <v-data-table
                    v-model="songSelected"
                    :headers="getMediaSongHeaders"
                    :items="mySongListInfo"
                    :search="songSearch"
                    item-key="id"
                    multi-sort
                    class="elevation-1"
                    show-select
                    :loading="mySongListInfo.length === 0"
                    :loading-text="$t('media.noLocalSongFiles')"
                    style="margin-top: -10px"
                    :items-per-page="25"
                    @click:row="rowClick"
                    fixed-header
                    height="63vh"
                    :footer-props="{
                      itemsPerPageOptions: [25,50,100],
                      showFirstLastPage: true,
                      showCurrentPage: true,
                      firstIcon: 'mdi-arrow-collapse-left',
                      lastIcon: 'mdi-arrow-collapse-right',
                      prevIcon: 'mdi-minus',
                      nextIcon: 'mdi-plus'
                    }"
                  >
                    <template v-slot:item.song_path_name="{ item }">
                      <span> {{ getSongNameFromPath(item.song_path_name) }} </span>
                    </template>
                    <template v-slot:item.duration="{ item }">
                      <span> {{ getSongDuration(item.duration) }} </span>
                    </template>
                    <template v-slot:item.size="{ item }">
                      <span>{{ $util.formatBytes(item.size) }}</span>
                    </template>
                    <template v-slot:item.audit="{ item }">
                      <span :style="{ color: item.audit ? 'black' : 'red' }">{{ item.audit ? $t('media.audited') : $t('media.pendingAudit') }}</span>
                    </template>
                    <!--操作列-->
                    <template v-slot:item.actions="{ item }">
                      <v-icon
                        large
                        @click.stop.prevent="preDownloadSongActionInTable(item)"
                        class="mx-1"
                        :title="$t('media.preview')"
                      >
                        mdi-play-box
                      </v-icon>
                      <v-icon
                        large
                        @click.stop.prevent="preDeleteSongActionInTable(item)"
                        class="mx-1"
                        :title="$t('media.delete')"
                      >
                        mdi-delete
                      </v-icon>
                    </template>
                    <!-- 显示存储信息 -->
                    <template v-slot:footer.prepend v-if="isCloudIpSystemB() && !isAdmin">
                      <v-chip
                        color="primary"
                        dark
                      >
                        <v-icon left>mdi-database</v-icon>
                        {{ $t('media.storageInfo', {
                          total: storageCapacity,
                          used: usedStorageCapacity,
                          remaining: remainStorageCapacity
                        }) }}
                      </v-chip>
                    </template>
                  </v-data-table>
                </v-card>
              </v-tab-item>
              <!--电台管理-->
              <v-tab-item :value="'tab-' + 3">
                <v-card>
                  <v-row>
                    <v-col cols="3" class="treeViewStyle">
                      <v-text-field
                        v-model="radioGroupSearch"
                        :label="$t('media.radio.search')"
                        flat
                        light
                        hide-details
                        clearable
                        clear-icon="mdi-close-circle-outline"
                        class="ml-2"
                      />
                      <v-treeview
                        item-key="id"
                        item-text="name"
                        item-children="children"
                        :items="safeRadioGroupList"
                        selected-color="primary"
                        return-object
                        open-all
                        activatable
                        :filter="radioGroupTreeFilter"
                        :search="radioGroupSearch"
                        :active.sync="activeRadioGroupNodes"
                        @update:active="onRadioGroupSelect"
                      ></v-treeview>
                    </v-col>
                    <v-col
                      cols="9"
                      class="px-0 pb-0 elevation-1"
                    >
                      <v-card-title class="mt-n4">
                        <span class="text-h6">{{ $t('media.radio.radioList') }}</span>
                        <v-spacer />
                        <v-text-field
                          v-model="radioSearch"
                          append-icon="mdi-magnify"
                          :label="$t('common.search')"
                          single-line
                          hide-details
                          style="max-width: 300px"
                        />
                      </v-card-title>
                      <v-data-table
                        :headers="radioHeaders"
                        :items="filteredRadioList"
                        :search="radioSearch"
                        item-key="id"
                        class="mt-1 mx-2 px-2"
                        :items-per-page="15"
                        fixed-header
                        height="66vh"
                        :footer-props="{
                          itemsPerPageOptions: [15,50,100],
                          showFirstLastPage: true,
                          showCurrentPage: true,
                          firstIcon: 'mdi-arrow-collapse-left',
                          lastIcon: 'mdi-arrow-collapse-right',
                          prevIcon: 'mdi-minus',
                          nextIcon: 'mdi-plus'
                        }"
                      >
                        <template v-slot:item.serialNumber="{ item, index }">
                          <span>{{ index + 1 }}</span>
                        </template>
                        <template v-slot:item.name="{ item }">
                          <span>{{ item.name }}</span>
                        </template>
                        <template v-slot:item.url="{ item }">
                          <span>{{ item.url }}</span>
                        </template>
                        <template v-slot:item.group="{ item }">
                          <span>{{ getRadioGroupName(item.groupId) }}</span>
                        </template>
                        <template v-slot:item.createTime="{ item }">
                          <span>{{ formatDate(item.createTime) }}</span>
                        </template>
                        <template v-slot:item.actions="{ item }">
                          <v-btn
                            small
                            color="primary"
                            @click="playRadio(item)"
                          >
                            <v-icon left small>mdi-play</v-icon>
                            {{ $t('media.radio.listen') }}
                          </v-btn>
                        </template>
                        <template v-slot:no-data>
                          <span>{{ !selectedRadioGroupInTree ? $t('media.radio.noGroupSelected') : $t('media.radio.noRadiosInGroup') }}</span>
                        </template>
                      </v-data-table>
                    </v-col>
                  </v-row>
                </v-card>
              </v-tab-item>
            </v-tabs-items>
          </v-tabs>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import { customerVersion, getRadioGroupList, getRadioListByGroupId, playRadioSource, stopRadioSource, addRadioInfo, editRadioInfo, deleteRadioInfo } from '@/plugins/websocket'
  import draggable from "vuedraggable";
  import DataTableRowForSongHandler from "../tables/DataTableRowForSongHandler.vue";
  import axios from "axios";
  import {USER_LIMITS_PLAYLIST} from "../../../store";
  export default {
    components: {
      draggable,
      DataTableRowForSongHandler,
    },
    name: 'Song',
    data: () => ({
      successMessages: '',
      commonSuccessSnackBar: false,
      errorMessages: '',
      commonErrorSnackBar: false,
      snackbarTimeout: 1500,
      songSearch: null,
      songSelected: [],
      tab: null,
      tabList: [],
      currentTab: 'listManage',
      selectedListInTree: [], // 视图左侧选中的列表
      selectedSongsInTree: [], // 视图右侧选中的歌曲
      songSearchInTree: null,
      pageInTree: 1,
      activeTreeNodes: [],
      treeViewSearch: null,
      songListHeaders: [],
      /*集成对话框*/
      newListNameValid: false,
      replaceListNameValid: false,
      newListNameRules: [],
      commonRules: [],
      newListName: '',
      replacedListName: null,
      createListDialog: false,
      renameListDialog: false,
      deleteListDialog: false,
      addSongDialog: false,
      addSongTransfer: [],
      groupSongsInTree: [],
      deleteSongDialog: false,
      auditSongDialog: false,
      isAllowDrag: false,
      isTableSortingDisabled: false, // 是否关闭表格表头排序功能
      songListForSort: [], // 歌曲排序时使用的数据
      sortSongByNameAsc: true, // 按歌曲名称排序升序
      sortSongByDurationAsc: true, // 按歌曲IP排序升序
      tablePageForSort: 1, // 歌曲排序时的页码
      sortItemPerPage: 15, // 歌曲排序的每页数目，默认为15
      songSelectedForSortIndex: null, // 歌曲排序选中歌曲的index
      // 曲库管理
      files: [],
      uploadLoading: false,
      createSongDialog: null,
      alreadyAddSongToList: false,
      isRemoveSongFromMedia: false, // 是否从曲库中删除，false表示仅从列表删除
      deletedSongActionInTable: null,
      deleteSongActionInTableDialog: false,
      downloadSongDialog: false,
      sortBy: [], // 表格根据何参数进行排序
      addSongTab: 0, // 添加歌曲对话框的当前选项卡
      // 电台管理相关数据
      radioGroupList: [], // 电台分组列表
      radioList: [], // 电台列表
      selectedRadioGroupInTree: null, // 选中的电台分组
      activeRadioGroupNodes: [], // 激活的电台分组节点
      radioGroupSearch: null, // 电台分组搜索
      radioSearch: null, // 电台搜索
      radioHeaders: [], // 电台表格头部
    }),
    computed: {
      ...mapState(['errorId', 'errorWsMessage', 'playList', 'addSongListResult', 'renameSongListResult',
        'removeSongListResult', 'addSongToListResult', 'removeSongFromListResult', 'auditSongForMediaResult', 'songMd5SelectedForSort',
        'sortSongListResult', 'uploadSongProgress', "userInfoData", 'localSongListInfo', 'user']),
      ...mapGetters(['decodeZones', 'isAdmin']),
      addSongToListDialogTitle() {
        if (customerVersion === 'C2B6') {
          const baseText1 = this.$t('media.tips.auditedSongsOnly').split('{audited}')[0];
          const baseText2 = this.$t('media.tips.auditedSongsOnly').split('{audited}')[1];
          const spanText = `<span style="color: red;">${this.$t('media.audited')}</span>`;
          return baseText1 + spanText + baseText2;
        } else {
          return this.$t('media.tips.existingSongsOnly');
        }
      },
      mySongListInfo() {
        return this.$store.getters.mySongListInfo.map(obj=> ({ ...obj, type: this.getSongType(obj.song_path_name) }))
      },
      // 单位MB
      storageCapacity() {
        return this.userInfoData.storage_capacity ? this.userInfoData.storage_capacity : 0
      },
      usedStorageCapacity() {
        const mySongs = this.localSongListInfo.filter(task => task.account === this.user)
        if (mySongs.length === 0) {
          return 0
        }
        const sizeSum = mySongs.map(song => song.size).reduce((a, b) => a + b, 0)
        // 歌曲size的单位是字节，需要转换成MB,保留小数点后一位
        return (sizeSum / 1024 / 1024).toFixed(1)
      },
      remainStorageCapacity() {
        if (this.usedStorageCapacity > this.storageCapacity) {
          return 0
        }
        return (this.storageCapacity - this.usedStorageCapacity).toFixed(1);
      },
      /**
       * 检查分区排序是否有修改
       * @returns {boolean}
       */
      isSongSortChange() {
        if (this.groupSongsInTree.length === 0 || this.songListForSort.length === 0) {
          return false
        }
        return !(JSON.stringify(this.songListForSort) === JSON.stringify(this.groupSongsInTree))
      },
      // 适用于分区排序时的表格头部，需要去掉点击表头排序功能
      headersForSort() {
        const copyHeaders = JSON.parse(JSON.stringify(this.songListHeaders))
        copyHeaders.forEach(v => v.sortable = false)
        return copyHeaders
      },
      /**
       * 列表视图分区背景颜色设置
       */
      itemRowBackground: function (song) {
        return song.alive ? 'onlineZone' : 'offlineZone';
      },
      treeViewFilter () {
        return (item, treeViewSearch, textKey) => item[textKey].indexOf(treeViewSearch) > -1
      },
      // 去除已添加到列表中的歌曲，防止重复添加
      songListForAddingToList() {
        if (this.mySongListInfo.length === 0) {
          return []
        }
        const groupSongs = this.groupSongsInTree.map(x => x.song_path_name)
        if(customerVersion === 'C2B6') {
          return this.mySongListInfo.filter(song => !groupSongs.includes(song.song_path_name) && song.audit === true)
        }
        else{
          return this.mySongListInfo.filter(song => !groupSongs.includes(song.song_path_name))
        }
      },
      getMediaSongHeaders() {
        const header = [
          { text: this.$t('media.table.serialNumber'), value: 'id', align: 'center', sortable: true },
          { text: this.$t('media.table.name'), value: 'song_path_name', align: 'center', sortable: false },
          { text: this.$t('media.table.type'), value: 'type', align: 'center', sortable: true },
          { text: this.$t('media.table.duration'), value: 'duration', align: 'center', sortable: true },
          { text: this.$t('media.table.bitrate'), value: 'bitrate', align: 'center', sortable: true },
          { text: this.$t('media.table.size'), value: 'size', align: 'center', sortable: true },
          { text: this.$t('media.table.user'), value: 'account', align: 'center', sortable: false },
        ]
        if(customerVersion === 'C2B6') {
          header.push({text: this.$t('media.table.auditStatus'), value: 'audit', align: 'center', sortable: true})
        }

        header.push({ text: this.$t('media.table.actions'), value: 'actions', align: 'center', sortable: false })
        return header
      },
      canConfirmAddSong() {
        if (this.uploadLoading) return false
        if (this.addSongTab === 0) {
          return this.addSongTransfer.length > 0
        } else {
          return this.files.length > 0
        }
      },

      // 过滤后的电台列表
      filteredRadioList() {
        if (!Array.isArray(this.radioList)) {
          return []
        }
        if (!this.selectedRadioGroupInTree) {
          return this.radioList
        }
        const selectedGroupId = this.selectedRadioGroupInTree.id
        return this.radioList.filter(radio => radio.groupId === selectedGroupId)
      },
      // 电台分组树过滤器
      radioGroupTreeFilter() {
        return (item, search, textKey) => {
          if (!search) return true
          return item[textKey].toLowerCase().indexOf(search.toLowerCase()) > -1
        }
      },
      // 确保电台分组列表始终为数组
      safeRadioGroupList() {
        return Array.isArray(this.radioGroupList) ? this.radioGroupList : []
      },
      // 从store获取电台分组列表结果
      radioGroupListResult() {
        return this.$store.state.radioGroupListResult
      },
      // 从store获取电台详情结果
      radioDetailsResult() {
        return this.$store.state.radioDetailsResult
      },
      // 从store获取播放电台结果
      playRadioResult() {
        return this.$store.state.playRadioResult
      },
      // 从store获取停止电台结果
      stopRadioResult() {
        return this.$store.state.stopRadioResult
      },
      // 从store获取添加电台结果
      addRadioResult() {
        return this.$store.state.addRadioResult
      },
      // 从store获取编辑电台结果
      editRadioResult() {
        return this.$store.state.editRadioResult
      },
      // 从store获取删除电台结果
      deleteRadioResult() {
        return this.$store.state.deleteRadioResult
      }
    },
    watch: {
      // 监听语言变化
      '$i18n.locale'() {
        this.initializeTranslations()
      },
      activeTreeNodes() {
        this.selectedListInTree = JSON.parse(JSON.stringify(this.activeTreeNodes))
      },
      // 监听电台分组选择（已改为使用@update:active事件处理）
      // activeRadioGroupNodes() {
      //   this.selectedRadioGroupInTree = JSON.parse(JSON.stringify(this.activeRadioGroupNodes))
      // },
      // 统一错误处理
      errorId () {
        if (this.$route.fullPath !== '/dashboard/media') {
          return;
        }
        if (this.$store.state.errorId !== null) {
          this.errorMessages = this.$store.state.errorWsMessage
          this.commonErrorSnackBar = true
          // 关闭相关loading
          switch (this.commandName) {
            case 'add_song_to_list':
              this.uploadLoading = false
              this.alreadyAddSongToList = false
              this.$store.commit('resetUploadProgress')
              break
            default:
              break
          }
        }
      },
      addSongListResult() {
        if (this.addSongListResult === 0) {
          this.successMessages = this.$t('media.messages.createListSuccess')
          this.commonSuccessSnackBar = true
          this.createListDialog = false
          this.newListName = ''
          this.$refs.newListRef.reset()
          this.$store.commit('updateAddSongListResult', null)
        }
      },
      renameSongListResult() {
        if (this.renameSongListResult === 0) {
          this.successMessages = this.$t('media.messages.renameListSuccess')
          this.commonSuccessSnackBar = true
          this.renameListDialog = false
          this.replacedListName = null
          this.$refs.changeListNameRef.reset()
          this.$store.commit('updateRenameSongListResult', null)
        }
      },
      removeSongListResult() {
        if (this.removeSongListResult === 0) {
          this.successMessages = this.$t('media.messages.deleteListSuccess')
          this.commonSuccessSnackBar = true
          this.deleteListDialog = false
          setTimeout(() => { this.activeTreeNodes = [] }, 200)
          this.$store.commit('updateRemoveSongListResult', null)
        }
      },
      addSongToListResult() {
        if (this.addSongToListResult === 0) {
          if (this.alreadyAddSongToList) {
            // 曲库上传
            this.successMessages = this.$t('media.messages.uploadSongSuccess')
            this.commonSuccessSnackBar = true
            this.uploadLoading = false
            this.createSongDialog = false

            //在列表中上传新歌曲，需要关闭对话框
            this.addSongDialog = false

            this.$store.commit('updateAddSongToListResult', null)
            this.$store.commit('resetUploadProgress')
            return
          }
          // 添加歌曲至列表
          this.successMessages = this.$t('media.messages.addSongToListSuccess');
          this.commonSuccessSnackBar = true
          this.addSongDialog = false
          this.addSongTransfer = []
          this.$store.commit('updateAddSongToListResult', null)
        }
      },
      removeSongFromListResult() {
        if (this.removeSongFromListResult === 0) {
          if (this.isRemoveSongFromMedia) {
            this.successMessages = this.$t('media.messages.deleteSongFromLibrarySuccess');
            this.songSelected = []
            this.deletedSongActionInTable = null
          } else {
            this.successMessages = this.$t('media.messages.deleteSongFromListSuccess');
            this.selectedSongsInTree = []
          }

          this.commonSuccessSnackBar = true
          this.deleteSongDialog = false
          this.deleteSongActionInTableDialog = false
          this.$store.commit('updateRemoveSongResult')
        }
      },
      auditSongForMediaResult() {
        if (this.auditSongForMediaResult === 0) {
            this.successMessages = this.$t('media.messages.auditSongSuccess');

          this.commonSuccessSnackBar = true
          this.auditSongDialog = false
          this.$store.commit('updateAuditSongResult')
        }
      },
      sortSongListResult() {
        if (this.sortSongListResult === 0) {
          this.successMessages = this.$t('media.messages.sortSongSuccess')
          this.commonSuccessSnackBar = true
          this.$store.commit('updateSortSongListResult', null)
        }
      },
      selectedListInTree: {
        handler: function() {
          // 切换列表时取消表头排序
          this.sortBy = []
          // 切换列表时取消歌曲表单的选中状态
          this.selectedSongsInTree = []
          this.isAllowDrag = false
          if (this.selectedListInTree.length === 0 || this.selectedListInTree[0].songs.length === 0) {
            this.groupSongsInTree = []
            return
          }
          const songArray = []
          let index = 1
          this.selectedListInTree[0].songs.forEach(song => {
            song.id = index
            index++
            songArray.push(song)
          })
          this.groupSongsInTree = songArray
        },
        deep: true
      },
      playList: {
        handler: function () {
          // 实时刷新列表页面数据
          if (this.selectedListInTree.length > 0) {
            const listId = this.selectedListInTree[0].list_id
            this.selectedListInTree = []
            this.selectedListInTree.push(this.playList.find(x => x.list_id === listId))
          }
        },
        deep: true
      },
      songMd5SelectedForSort() {
        if (this.songMd5SelectedForSort != null) {
          this.songSelectedForSortIndex = this.songListForSort.findIndex(x => x.song_md5 === this.songMd5SelectedForSort)
          return
        }
        this.songSelectedForSortIndex = null
      },
      isAllowDrag() {
        // 开启分区排序时，关闭管理界面的vuetify自带表头排序功能
        this.isTableSortingDisabled = this.isAllowDrag;
        if (this.isAllowDrag) {
          // 重置页码
          this.tablePageForSort = 1
          // 复制当前的列表歌曲
          this.songListForSort = JSON.parse(JSON.stringify(this.groupSongsInTree))
        } else {
          // 清空排序分区列表
          this.songListForSort = []
          this.$store.commit('updateSongMd5SelectedForSort', null)
        }
      },
      // 上传完成
      uploadSongProgress: function () {
        if (this.$route.fullPath !== '/dashboard/media') {
          return;
        }
        if (!this.alreadyAddSongToList && this.$store.state.uploadSongProgress >= 99) {
          this.alreadyAddSongToList = true
          if (this.currentTab === 'SongManage') {
            // this.$ws.addSongToList(this.files, '')
            let that = this;
            setTimeout(function() {
                // 这里是要延时执行的代码
                that.$ws.addSongToList(that.files, '')
            }, 400);
          } else if(this.currentTab === 'listManage') {
            // this.$ws.addSongToListForMedia(true,this.files, this.selectedListInTree[0].list_id)
            let that = this;
            setTimeout(function() {
                // 这里是要延时执行的代码
                that.$ws.addSongToListForMedia(true,that.files, that.selectedListInTree[0].list_id)
            }, 500);
          }
        }
      },
      // 当上传文件的列表发生变化时，设置add_song_to_list标志位为false
      files: function () {
        this.alreadyAddSongToList = false
        this.$store.commit('resetUploadProgress')
      },
      // 监听电台分组列表WebSocket响应
      radioGroupListResult() {
        if (this.radioGroupListResult && this.radioGroupListResult.result === 0) {
          this.radioGroupList = this.radioGroupListResult.groups || []
          this.successMessages = this.$t('media.messages.loadRadioGroupsSuccess')
          this.commonSuccessSnackBar = true
        } else{
          this.errorMessages = this.radioGroupListResult.message || this.$t('media.errors.loadRadioGroupsFailed')
          this.commonErrorSnackBar = true
        }
      },
      // 监听电台详情WebSocket响应
      radioDetailsResult() {
        if (this.radioDetailsResult && this.radioDetailsResult.result === 0) {
          this.radioList = this.radioDetailsResult.radios || []
          this.successMessages = this.$t('media.messages.loadRadioDetailsSuccess')
          this.commonSuccessSnackBar = true
        } else {
          this.errorMessages = this.radioDetailsResult.message || this.$t('media.errors.loadRadioDetailsFailed')
          this.commonErrorSnackBar = true
        }
      },
      // 监听播放电台WebSocket响应
      playRadioResult() {
        if (this.playRadioResult && this.playRadioResult.result === 0) {
          this.successMessages = this.$t('media.messages.playRadioSuccess')
          this.commonSuccessSnackBar = true
        } else{
          this.errorMessages = this.playRadioResult.message || this.$t('media.errors.playRadioFailed')
          this.commonErrorSnackBar = true
        }
      },
      // 监听停止电台WebSocket响应
      stopRadioResult() {
        if (this.stopRadioResult && this.stopRadioResult.result === 0) {
          this.successMessages = this.$t('media.messages.stopRadioSuccess')
          this.commonSuccessSnackBar = true
        } else{
          this.errorMessages = this.stopRadioResult.message || this.$t('media.errors.stopRadioFailed')
          this.commonErrorSnackBar = true
        }
      },
      // 监听添加电台WebSocket响应
      addRadioResult() {
        if (this.addRadioResult && this.addRadioResult.result === 0) {
          this.successMessages = this.$t('media.messages.addRadioSuccess')
          this.commonSuccessSnackBar = true
          // 重新加载电台列表
          this.fetchRadioDetails(this.selectedRadioGroupId)
        } else{
          this.errorMessages = this.addRadioResult.message || this.$t('media.errors.addRadioFailed')
          this.commonErrorSnackBar = true
        }
      },
      // 监听编辑电台WebSocket响应
      editRadioResult() {
        if (this.editRadioResult && this.editRadioResult.result === 0) {
          this.successMessages = this.$t('media.messages.editRadioSuccess')
          this.commonSuccessSnackBar = true
          // 重新加载电台列表
          this.fetchRadioDetails(this.selectedRadioGroupId)
        } else{
          this.errorMessages = this.editRadioResult.message || this.$t('media.errors.editRadioFailed')
          this.commonErrorSnackBar = true
        }
      },
      // 监听删除电台WebSocket响应
      deleteRadioResult() {
        if (this.deleteRadioResult && this.deleteRadioResult.result === 0) {
          this.successMessages = this.$t('media.messages.deleteRadioSuccess')
          this.commonSuccessSnackBar = true
          // 重新加载电台列表
          this.fetchRadioDetails(this.selectedRadioGroupId)
        } else{
          this.errorMessages = this.deleteRadioResult.message || this.$t('media.errors.deleteRadioFailed')
          this.commonErrorSnackBar = true
        }
      },
    },
    mounted () {
      this.initializeTranslations()
      this.alreadyAddSongToList = false
      this.isRemoveSongFromMedia = false
      this.initRadioData()
    },
    methods: {
      initializeTranslations() {
        // 初始化消息
        this.successMessages = this.$t('common.operationSuccess')
        this.errorMessages = this.$t('common.operationFailed')

        // 初始化选项卡列表
        this.tabList = [
          { id: 1, identify: 'listManage', name: this.$t('media.tabs.listManagement'), icon: 'mdi-file-tree'},
          { id: 2, identify: 'SongManage', name: this.$t('media.tabs.songManagement'), icon: 'mdi-laptop'},
          { id: 3, identify: 'radioManage', name: this.$t('media.tabs.radioManagement'), icon: 'mdi-radio'}
        ]

        // 初始化表格头部
        this.songListHeaders = [
          { text: this.$t('media.table.serialNumber'), value: 'id', align: 'center', sortable: true },
          { text: this.$t('media.table.name'), value: 'song_path_name', align: 'center', sortable: false },
          { text: this.$t('media.table.type'), value: 'type', align: 'center', sortable: true },
          { text: this.$t('media.table.duration'), value: 'duration', align: 'center', sortable: true },
          { text: this.$t('media.table.status'), value: 'alive', align: 'center', sortable: true },
        ]

        // 初始化电台表格头部
        this.radioHeaders = [
          { text: this.$t('media.table.serialNumber'), value: 'serialNumber', align: 'center', sortable: false },
          { text: this.$t('media.radio.radioName'), value: 'name', align: 'center', sortable: true },
          { text: this.$t('media.radio.radioUrl'), value: 'url', align: 'center', sortable: false },
          { text: this.$t('media.radio.radioGroup'), value: 'group', align: 'center', sortable: true },
          { text: this.$t('media.radio.createTime'), value: 'createTime', align: 'center', sortable: true },
          { text: this.$t('media.table.actions'), value: 'actions', align: 'center', sortable: false }
        ]

        // 初始化验证规则
        this.newListNameRules = [
          v => !!v || this.$t('media.validation.listNameRequired'),
          v => (v.length <= 20 && v.length > 0) || this.$t('media.validation.listNameLength'),
        ]
        this.commonRules = [
          v => !!v || this.$t('media.validation.nameRequired'),
          v => (v.length <= 20 && v.length > 0) || this.$t('media.validation.nameLength'),
        ]
      },
      IsAddSongToListShowAddNewSong() {
        if (customerVersion === 'C2B6') {
          return false
        }
        return true
      },
      // 批量下载已选择歌曲
      downloadSelectedSongs() {
        const urlList = this.songSelected.map(x => this.$cgiHost + x.song_path_name)
        urlList.forEach(url => this.downloadItem(url))
        this.downloadSongDialog = false
      },
      // 下载单个歌曲
      downloadItem (url) {
        // axios.get(url, { responseType: 'blob' })
        //   .then(response => {
        //     const blob = new Blob([response.data], { type: 'audio/mpeg' })
        //     const link = document.createElement('a')
        //     link.href = URL.createObjectURL(blob)
        //     link.download = this.getSongNameFromPath(url)
        //     link.click()
        //     URL.revokeObjectURL(link.href)
        //   }).catch(console.error)
        const link = document.createElement('a')
        link.href = url
        link.target = "_blank"
        //link.download = this.getSongNameFromPath(url)
        link.click();
      },
      preDownloadSongActionInTable(item) {
        if (item == null) {
          return
        }
        this.downloadItem(this.$cgiHost + item.song_path_name)
      },
      preDeleteSongActionInTable(item) {
        if (item == null) {
          return
        }
        this.deletedSongActionInTable = item
        this.isRemoveSongFromMedia = true
        this.deleteSongActionInTableDialog = true
      },
      // 上传文件
      submitFiles: function () {
        if (this.files.length === 0) {
          this.errorMessages = this.$t('media.errors.pleaseSelectSongs')
          this.commonErrorSnackBar = true
          return
        }
        if(this.files.length >50) {
          this.errorMessages = this.$t('media.errors.maxSongsLimit')
          this.commonErrorSnackBar = true
          return
        }
        if(this.$ws.getAllFileSize(this.files) >500*1024*1024) {
          this.errorMessages = this.$t('media.errors.totalSizeLimit')
          this.commonErrorSnackBar = true
          return
        }
        // 歌曲上传：限定"*.mp3,*.wav"
        for (let i = 0; i < this.files.length; i++) {
          const name = this.files[i].name
          const type = (name.substr(name.lastIndexOf('.'))).toLowerCase()
          if (type !== '.mp3' && type !== '.wav') {
            this.errorMessages = this.$t('media.errors.invalidFileFormat')
            this.commonErrorSnackBar = true
            return
          }
        }
        this.uploadLoading = true
        this.alreadyAddSongToList = false
        const result = this.$ws.uploadSong(this.files)
        if (result) {
        } else {
          this.commonErrorSnackBar = true
          this.errorMessages = this.$t('media.errors.uploadFailed')
          this.uploadLoading = false
        }
      },
      preAddSongsToList() {
        this.$store.commit('resetUploadProgress')
        // 上传歌曲对话框，重新打开时应清除数据
        this.files = []
        this.uploadLoading = false
        this.addSongDialog = true
      },
      preUploadSongs() {
        this.$store.commit('resetUploadProgress')
        // 上传歌曲对话框，重新打开时应清除数据
        this.files = []
        this.uploadLoading = false
        this.createSongDialog = true
      },
      /**
       * 取消排序修改
       * 暂不支持单次撤销
       */
      revertSorting() {
        this.songListForSort = JSON.parse(JSON.stringify(this.groupSongsInTree))
      },
      /**
       * 确认歌曲排序修改并提交
       */
      confirmSorting() {
        const songMd5s = this.songListForSort.map(x => x.song_md5)
        this.$ws.sortSongList(this.selectedListInTree[0].list_id, songMd5s)
      },
      updateSortItemPerPage(newValue) {
        this.sortItemPerPage = newValue !== -1 ? newValue : Number.MAX_SAFE_INTEGER
      },
      sortSongByName() {
        this.songListForSort.sort((a, b) => this.sortSongByNameAsc ? a.song_name.localeCompare(b.song_name) : b.song_name.localeCompare(a.song_name))
        this.sortSongByNameAsc = !this.sortSongByNameAsc
      },
      sortSongByDuration() {
        this.songListForSort.sort((a, b) => this.sortSongByDurationAsc ? a.duration- b.duration : b.duration - a.duration)
        this.sortSongByDurationAsc = !this.sortSongByDurationAsc
      },
      /**
       * 分区上移
       */
      zoneMoveUp() {
        this.moveItem(this.songListForSort, this.songSelectedForSortIndex, this.songSelectedForSortIndex - 1)
        this.songSelectedForSortIndex -= 1
      },
      /**
       * 分区下移
       */
      zoneMoveDown() {
        this.moveItem(this.songListForSort, this.songSelectedForSortIndex, this.songSelectedForSortIndex + 1)
        this.songSelectedForSortIndex += 1
      },
      /**
       * 通用方法，移动数组中元素从指定index到达指定的index
       * @param data
       * @param from
       * @param to
       */
      moveItem(data, from, to) {
        // remove `from` item and store it
        const removedItem = data.splice(from, 1)[0];
        // insert stored item into position `to`
        data.splice(to, 0, removedItem);
      },
      rowClick: function (item, row) {
        if (row.isSelected) {
          row.select(false)
        } else {
          row.select(true)
        }
      },
      getSongNameFromPath(songPathName) {
        const songPathNameValid = songPathName.lastIndexOf('/') !== -1;
        return songPathNameValid ? songPathName.split("/").pop() : '';
      },
      getSongNameFromPathAndRemoveSuffix(songPathName) {
        const songPathNameValid = songPathName.lastIndexOf('/') !== -1;
        const songName = songPathNameValid ? songPathName.split("/").pop() : '';
        return songName.substring(0, songName.lastIndexOf('.')) || songName
      },
      // 获取音源列表歌曲提示（歌名+时长）
      getSongDuration(songDurationNumber) {
        let duration = new Date(songDurationNumber * 1000).toISOString();
        if (duration.substr(11, 2) !== '00') {
          // 超过1h的歌曲，显示小时
          return  duration.substr(11, 8)
        }
        // 不超过1h的歌曲，不显示小时
        return duration.substr(14, 5)
      },
      // 更改当前选项卡
      setCurrentTab: function (tab) {
        this.currentTab = tab.identify
        // 当切换到电台管理TAB时，自动加载电台数据
        if (tab.identify === 'radioManage') {
          this.initRadioData()
        }
      },
      // 如下从Dashboard.Vue中复制，可重构
      // 通用列表检测
      checkList: function (valid, name) {
        if (!valid || name === null || name === '') {
          this.errorMessages = '输入不合法，请重新输入'
          this.commonErrorSnackBar = true
          return true
        }
        let needReturn = false
        this.playList.forEach((list) => {
          if (list.list_name === name) {
            this.errorMessages = '列表名已存在，请重新输入'
            this.commonErrorSnackBar = true
            needReturn = true
          }
        })
        return needReturn
      },
      // 创建新列表确认
      createNewList: function () {
        if (this.checkList(this.newListNameValid, this.newListName)) {
          return
        }
        this.$ws.addSongList(this.$ws.getUuid(), this.newListName)
      },
      // 重命名列表
      renameList: function () {
        if (this.selectedListInTree[0].list_name === 'TTS List') {
          this.errorMessages = '该列表不允许修改'
          this.commonErrorSnackBar = true
          return
        }
        if (this.checkList(this.replaceListNameValid, this.replacedListName)) {
          return
        }
        this.$ws.renameSongList(this.selectedListInTree[0].list_id, this.replacedListName)
      },
      // 删除列表
      deleteList: function () {
        this.$ws.removeSongList(this.selectedListInTree[0].list_id)
      },
      // 添加曲库歌曲至列表
      addSongIntoList() {
        this.alreadyAddSongToList = false
        const songNames = []
        this.addSongTransfer.forEach(song => songNames.push(this.getSongNameFromPath(song)))
        this.$ws.addSongToListForMedia(false,songNames, this.selectedListInTree[0].list_id)
      },
      preRemoveSongFromList() {
        this.isRemoveSongFromMedia = false
        this.deleteSongDialog = true
      },
      preRemoveSongFromMedia() {
        this.isRemoveSongFromMedia = true
        this.deleteSongDialog = true
      },
      removeSong() {
        if (this.isRemoveSongFromMedia) {
          this.removeSongFromMedia()
        } else {
          this.removeSongFromList()
        }
      },
      preAuditingSong() {
        this.auditSongDialog = true
      },
      isShowAuditSong() {
        return ( customerVersion === 'C2B6' && (this.isAdmin || this.$store.state.authority & USER_LIMITS_PLAYLIST) )
      },
      auditSong() {
        const songNames = this.songSelected.map(song => this.getSongNameFromPath(song.song_path_name))
        this.$ws.auditSongForMedia(songNames)
      },
      removeSongFromList() {
        const songNames = this.selectedSongsInTree.map(song => this.getSongNameFromPath(song.song_path_name))
        this.$ws.removeSongFromListForMedia(songNames, this.selectedListInTree[0].list_id, false)
      },
      removeSongFromMedia() {
        const songNames = this.songSelected.map(song => this.getSongNameFromPath(song.song_path_name))
        this.$ws.removeSongFromListForMedia(songNames, '', true)
      },
      removeSingleSongFromMedia() {
        const list = []
        list.push(this.getSongNameFromPath(this.deletedSongActionInTable.song_path_name))
        this.$ws.removeSongFromListForMedia(list, '', true)
      },
      resetSelectedSongs() {
        this.selectedSongsInTree = []
      },
      // 是否支持对选中的列表进行操作
      checkIfListOperPermission () {
        if (this.selectedListInTree.length === 0 || this.selectedListInTree[0].list_name === 'TTS List') {
          return false
        }
        if ( this.$store.state.user === 'admin' || this.selectedListInTree[0].list_account === this.$store.state.user || this.$store.state.authority & USER_LIMITS_PLAYLIST) {
          return true
        }
        return false
      },
      // 是否支持对选中的歌曲进行删除操作
      checkIfSongDelOperPermission () {
        if (this.selectedSongsInTree.length === 0) {
          return false
        }
        //如果是TTL LIST，WEB先不主动判断权限，由后台进行判断
        if (this.selectedListInTree.length !== 0 && this.selectedListInTree[0].list_name === 'TTS List') {
          return true
        }
        if ( this.$store.state.user === 'admin' || this.selectedListInTree[0].list_account === this.$store.state.user || this.$store.state.authority & USER_LIMITS_PLAYLIST) {
          return true
        }
        return false
      },
      // 是否支持对选中列表内歌曲进行排序
      checkIfListSortPermission () {
        // 如果列表内歌曲数量只有1，不允许排序 (也包括未选列表的情况）
        if (this.groupSongsInTree.length <= 1) {
          return false
        }
        // ******** 不允许排序TTS列表
        if (this.selectedListInTree[0].list_name === 'TTS List') {
          return false
        }
        // 允许排序情形
        if ( this.$store.state.user === 'admin' || this.selectedListInTree[0].list_account === this.$store.state.user || this.$store.state.authority & USER_LIMITS_PLAYLIST) {
          return true
        }
        return false
      },
      /**
       * 控制是否允许分区进行排序
       * todo 增加单个分区Lock的选项
       * @param evt
       * @param originalEvent
       */
      onMoveCallback(evt, originalEvent) {
        // item为移动的对象，即zone属性
        // const item = evt.draggedContext.element;
        const originIndex = evt.draggedContext.index;
        const futureIndex = evt.draggedContext.futureIndex;
        return true;
      },
      /**
       * 排序后数组发生变化事件
       * 注意： 如果每页数目不为all，则传入的item只有当前每页数目数量，如15
       * @param items
       */
      onSongSortChange(items) {
        if (items.length < this.songListForSort.length) {
          // 当每页数目非all时，替换掉指定index的元素
          this.songListForSort.splice(0, items.length, ...items)
        } else {
          this.songListForSort = items;
        }
      },
      // 根据歌曲位置判断类型为TTS还是音乐
      getSongType(songPathName) {
        return (songPathName != null && songPathName.startsWith('/Data/Program/Common/Music/')) ? 'TTS' : this.$t('media.music')
      },
      confirmAddSong() {
        if (this.addSongTab === 0) {
          // 从曲库添加歌曲
          this.addSongIntoList()
        } else {
          // 上传新歌曲
          this.submitFiles()
        }
      },
      // 电台管理相关方法
      // 获取电台分组列表
      fetchRadioGroups() {
        try {
          getRadioGroupList()
        } catch (error) {
          console.error('发送获取电台分组请求失败:', error)
          this.radioGroupList = []
          this.errorMessages = this.$t('media.radio.fetchGroupError')
          this.commonErrorSnackBar = true
        }
      },
      // 获取指定分组的电台列表
      fetchRadioDetails(groupId = 1) {
        try {
          getRadioListByGroupId(groupId)
        } catch (error) {
          console.error('发送获取电台详情请求失败:', error)
          this.radioList = []
          this.errorMessages = this.$t('media.radio.fetchDetailError')
          this.commonErrorSnackBar = true
        }
      },
      // 播放网络电台
      playRadio(radio) {
        try {
          // 获取选中的分区MAC地址列表（这里需要根据实际情况获取）
          const zoneMacs = this.getSelectedZoneMacs() || []
          
          if (zoneMacs.length === 0) {
            this.errorMessages = this.$t('media.radio.noZoneSelected')
            this.commonErrorSnackBar = true
            return
          }

          playRadioSource(radio.name, radio.url, zoneMacs)
        } catch (error) {
          console.error('播放电台失败:', error)
          this.errorMessages = this.$t('media.radio.playError')
          this.commonErrorSnackBar = true
        }
      },
      // 停止网络电台播放
      stopRadio(sessionId) {
        try {
          stopRadioSource(sessionId)
        } catch (error) {
          console.error('停止电台播放失败:', error)
          this.errorMessages = this.$t('media.radio.stopError')
          this.commonErrorSnackBar = true
        }
      },
      // 添加电台信息
      addRadio(name, url) {
        try {
          addRadioInfo(name, url)
        } catch (error) {
          console.error('添加电台失败:', error)
          this.errorMessages = this.$t('media.radio.addError')
          this.commonErrorSnackBar = true
        }
      },
      // 编辑电台信息
      editRadio(id, name, url) {
        try {
          editRadioInfo(id, name, url)
        } catch (error) {
          console.error('编辑电台失败:', error)
          this.errorMessages = this.$t('media.radio.editError')
          this.commonErrorSnackBar = true
        }
      },
      // 删除电台信息
      deleteRadio(id) {
        try {
          deleteRadioInfo(id)
        } catch (error) {
          console.error('删除电台失败:', error)
          this.errorMessages = this.$t('media.radio.deleteError')
          this.commonErrorSnackBar = true
        }
      },
      // 获取选中的分区MAC地址列表（需要根据实际UI实现）
      getSelectedZoneMacs() {
        // 这里需要根据实际的分区选择逻辑来实现
        // 暂时返回空数组，实际使用时需要获取用户选中的分区
        return []
      },
      // 处理电台分组选择事件（强制单选）
      onRadioGroupSelect(activeNodes) {
        const nodes = Array.isArray(activeNodes) ? activeNodes : []
        const lastId = nodes.length ? nodes[nodes.length - 1] : null

        // 始终仅保留一个激活节点（最后一个）
        this.$nextTick(() => {
          this.activeRadioGroupNodes = lastId != null ? [lastId] : []
        })

        if (lastId != null) {
          // 通过id从tree数据中找到对象
          const findById = (list, id) => {
            if (!Array.isArray(list)) return null
            for (const item of list) {
              if (item && item.id === id) return item
              const found = findById(item.children, id)
              if (found) return found
            }
            return null
          }
          const selectedGroup = findById(this.safeRadioGroupList, lastId) || { id: lastId }
          this.selectedRadioGroupInTree = selectedGroup
          // 获取选中分组的电台详情
          this.fetchRadioDetails(lastId)
        } else {
          this.selectedRadioGroupInTree = null
          this.radioList = []
        }
      },
      // 初始化电台数据
      async initRadioData() {
        await this.fetchRadioGroups()
        //await this.fetchRadioDetails()
      },
      // 根据分组ID获取分组名称
      getRadioGroupName(groupId) {
        if (!Array.isArray(this.radioGroupList)) {
          return '未知分组'
        }
        const group = this.radioGroupList.find(g => g.id === groupId)
        return group ? group.name : '未知分组'
      },
      // 格式化日期
      formatDate(dateString) {
        if (!dateString) return ''
        const date = new Date(dateString)
        return date.toLocaleDateString()
      },
    },
  }
</script>

<style scoped>
  /deep/ tr.v-data-table__selected {
    background: #c2c9f3 !important;
  }
  /* 设置treeView的最大高度和滚动 */
  /deep/ .treeViewStyle {
    max-height: 81vh;
    overflow-y: auto;
  }
  .chosen-zone-sort {
    border-style: solid;
    border-width: 2px;
    /*border-color: var(--v-secondary-base) !important;*/
  }
  /deep/ .v-treeview-node__label {
    font-size: 1.2em;
  }
</style>
