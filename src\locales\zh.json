{"toggleSidebar": "收缩导航栏", "avatar": "IP广播系统", "buttons": "按钮", "calendar": "日历", "charts": "图表", "components": "组件", "ct": "CT", "dtables": "数据表格", "eforms": "扩展表单", "error": "错误页面", "etables": "扩展表格", "example": "示例", "forms": "表单", "fullscreen": "全屏地图", "google": "谷歌地图", "grid": "网格系统", "icons": "图标", "lock": "锁屏页面", "maps": "地图", "multi": "多级折叠", "notifications": "通知", "pages": "页面", "plan": "选择计划", "pricing": "定价", "my-profile": "我的资料", "edit-profile": "编辑资料", "register": "注册页面", "rforms": "常规表单", "rtables": "常规表格", "rtl": "RTL支持", "search": "搜索", "tables": "表格", "tabs": "标签页", "tim": "Creative Tim", "timeline": "时间轴", "typography": "排版", "upgrade": "2020-2022 版权所有", "user": "用户资料", "vforms": "验证表单", "widgets": "小部件", "wizard": "向导", "customer": {"title": {"ipBroadcastSystem": "IP广播系统", "cloudBroadcastSystem": "云广播系统", "villageVillageBroadcastSystem": "村村通广播系统", "aiSmartLinkSystem": "AI智能互联系统", "iotDigitalThingsLinkSystem": "IOT数字物联系统", "iotCloudPlatform": "物联网云服务平台", "cloudInterconnectControlSystem": "云互联智控系统", "smartCloudBroadcastSystem": "智慧云广播系统"}}, "login": {"title": "登录", "username": "用户名", "password": "密码", "usernamePlaceholder": "请输入用户名", "passwordPlaceholder": "*********", "loginButton": "登录", "usernameRequired": "提示: 用户名不能为空", "passwordRequired": "提示: 密码不能为空", "loginError": "用户名或密码输入错误", "loginSuccess": "登录成功", "serverConnectionFailed": "服务器连接失败，请稍后重试", "currentUser": "当前登录用户"}, "appbar": {"collapseNavbar": "收起导航栏", "showNavbar": "显示导航栏", "controlCenter": "控制中心", "cloudControl": "微信云控制", "wechatScan": "使用微信扫一扫绑定", "enableCloudControl": "开启云控制", "disableCloudControl": "关闭云控制", "accountManagement": "账户管理", "logout": "注销账户", "currentUser": "当前登录用户"}, "language": {"zh": "简体中文", "en": "English", "zh-TW": "繁體中文", "selectLanguage": "选择语言"}, "timeTips": {"hours": "小时", "minutes": "分钟", "seconds": "秒", "days": "天"}, "tts": {"title": "文字转语音", "inputText": "请输入文字", "fileName": "文件名", "audioFileName": "语音文件名", "audioFileNamePlaceholder": "请输入完成后的语音文件名", "textTooLong": "文本长度不能超过 {maxBytes} 字节"}, "routes": {"controlCenter": "控制中心", "deviceManagement": "设备管理", "mediaManagement": "媒体管理", "timerManagement": "定时管理", "accountManagement": "账户管理", "monitorManagement": "监控管理", "logManagement": "日志管理", "systemSettings": "系统设置", "systemMaintenance": "系统维护", "informationPublish": "信息发布", "logout": "退出登录", "error": "出错啦"}, "deviceTypes": {"allDevices": "全部设备", "decodeTerminal": "解码终端", "smartPager": "智能寻呼台", "audioCollector": "音频采集器", "fireCollector": "消防采集器", "powerSequencer": "电源时序器", "audioCoprocessor": "音频协处理器", "audioRepeater": "音频中继器", "remoteController": "远程遥控器", "phoneGateway": "电话网关", "ampController": "功放控制器", "noiseDetector": "噪声自适应终端"}, "dashboard": {"soundCardRecord": "声卡采集", "soundCardOutputDevice": "声卡输出设备", "soundCardInputDevice": "声卡输入设备", "deviceVolume": "设备音量", "transmissionMode": "传输模式", "lowBandwidth": "低带宽", "highQuality": "高品质", "startRecord": "开始采集", "stopRecord": "停止采集", "speaker": "发音人", "maleVoice": "男声", "femaleVoice": "女声", "volume": "音量", "pitch": "音调", "speed": "语速", "importTxtFile": "导入TXT文件", "browseTxtFile": "浏览本地文件(UTF-8编码)", "load": "加载", "defaultParams": "默认参数", "startSynthesis": "开始合成", "systemRegister": "系统注册", "systemNotRegistered": "提示：当前系统未注册，请注册以解锁全部功能", "registrationCode": "注册码", "enterRegistrationCode": "请输入注册码", "aboutToDelete": "即将删除", "groups": "个分组", "pleaseConfirm": "请确认", "createGroup": "新建分组", "enterGroupName": "请输入新分组名称", "zoneList": "分区列表", "selectedZones": "已选分区", "enterZoneKeyword": "请输入分区关键字", "groupName": "分组名称", "stopTask": "停止任务", "aboutToStop": "即将停止", "tasks": "个任务", "oneKeyAlarm": "一键告警", "confirmStartStopAlarm": "请确认是否启动/停止一键告警功能？", "setPlayModeSuccess": "设置播放模式成功", "applyToAllManualTasks": "是否应用于所有手动任务？否则将在新的手动任务中生效", "restoreTodayTimer": "恢复今日定时任务", "aboutToRestore": "即将恢复今日定时任务", "cancelTodayTimer": "取消今日定时任务", "aboutToCancel": "即将取消今日定时任务", "stopTimerTask": "停止定时任务", "aboutToStopTimer": "即将停止定时任务", "playSource": "播放节目源", "audioMixerConfirm": "您选择的分区包含音频协处理器，是否确认继续操作？", "audioSourceList": "音源列表", "onlineZones": "在线分区", "showOnlineZones": "显示在线分区", "showAllZones": "显示所有分区", "selectAll": "全选", "deselectAll": "反选", "selected": "已选", "currentConditionSelected": "当前条件已选", "totalSelected": "共选中", "search": "搜索", "itemsPerPage": "每页数量", "newGroup": "新建分组", "editGroup": "编辑分组", "deleteGroup": "删除分组", "zoneCount": "分区数量", "user": "用户", "containsZones": "包含分区", "groupHasNoZones": "此分组未添加分区", "listView": "列表视图", "noGroupSelected": "未选中分组", "selectedGroupHasNoZones": "已选中分组未添加分区信息", "audioCollection": "音频采集", "powerSequencer": "电源时序器", "restoreTodayTimerTask": "恢复今日定时任务", "playMode": "设置播放模式", "previousTrack": "上一曲", "play": "播放", "pause": "暂停", "nextTrack": "下一曲", "stop": "停止", "oneKeyAlarmBtn": "一键告警", "monitorControl": "监控控制", "openListening": "开启监听控制", "closeListening": "关闭监听控制", "soundCardRecordBtn": "声卡采集", "closeSoundCardRecord": "关闭声卡采集", "textToSpeech": "文字转语音", "playSourceBtn": "播放节目源", "setIdleStatus": "设置分区为空闲状态", "allZones": "所有分区", "allGroups": "全部分组", "messages": {"playSourceSuccess": "播放节目源成功", "playCollectSuccess": "播放音频采集器音源成功", "ttsSuccess": "文字转语音合成成功", "registerSuccess": "注册成功, 即将跳转回登录界面", "setPlayModeSuccess": "设置手动任务播放模式成功", "taskStopped": "任务已停止", "setPlayStatusSuccess": "设置手动任务播放状态成功", "controlPrevNextSuccess": "控制手动任务播放{word}成功", "alarmTaskStatus": "告警任务已{status}", "cancelTimerSuccess": "取消指定今日定时任务成功，将于第二天自动恢复", "timerTaskStopped": "定时任务已停止", "restoreTimerSuccess": "恢复指定今日定时任务成功", "timerTimePassed": "定时点时间已过，无法恢复", "groupNameEmpty": "分组名称不能为空", "selectAtLeastOneZone": "请至少选择一个分区", "selectOneGroupToEdit": "请选择一个分组进行编辑", "canOnlyEditOneGroup": "只能同时编辑一个分组，请重新选择", "groupNameAlreadyUsed": "该分组名已被占用，请使用其他分组名", "selectAtLeastOneGroup": "请至少选择一个分组", "selectZoneOrGroup": "请选择分区或分组", "selectSourceFromList": "请从音源列表中选择节目源", "itemsPerPageCannotBeZero": "每页数量不能为0或负数，请重新输入", "selectZoneOrGroupForVolume": "请选择分区或分组进行音量调节", "noTxtFileSelected": "未选择txt文件，无法加载", "loadTxtFileSuccess": "加载txt文件成功", "textContentEmpty": "文本内容不能为空，请重新输入或者导入txt文本", "audioFileNameEmpty": "语音文件名不能为空，请重新输入", "registrationCodeEmpty": "注册码不能为空，请重新输入", "noMonitorSpeakerSet": "当前未设置监听音箱，请重试", "monitorSpeakerOffline": "监听音箱离线，请重试", "selectOneZoneForListening": "请选择一个分区进行监听", "canOnlySelectOneZoneForListening": "只能选择一个分区进行监听，请重新选择", "selectOneZoneForMonitor": "请选择一个分区开启视频监控", "canOnlySelectOneZoneForMonitor": "只能选择一个分区开启视频监控，请重新选择", "runSoundCardClient": "请运行声卡采集客户端", "setVolumeNotSuccessful": "设置不成功，请重试", "startRecordingFailed": "启动采集失败", "startRecording": "开始采集", "todayTimerCount": "今日{status}定时点数量为0", "manualTaskCountZero": "手动任务数量为0", "createGroupSuccess": "创建分组成功", "editGroupSuccess": "编辑分组成功", "deleteGroupSuccess": "删除分组成功", "adjustVolumeSuccess": "调节音量成功", "stopRecording": "停止采集", "stopPlaySuccess": "停止播放成功"}, "status": {"executed": "已执行", "executing": "正在执行", "executeAfter": "后执行", "playing": "播放", "paused": "暂停", "stopped": "停止", "started": "启动", "notExecuted": "未执行", "cancelled": "已取消"}, "tabs": {"zones": "分区", "groups": "分组", "listView": "列表视图", "timerTasks": "定时任务", "manualTasks": "手动任务"}, "tooltips": {"resetVolumeSpeedPitch": "重置音量、语速和音调为50", "openVideoMonitor": "打开该分区的视频监控画面", "openListeningFor": "开启节目源监听（{name}）", "stopListeningFor": "停止监听音箱（{name}）", "songDuration": "时长: {duration}", "listInfo": "数量: {count}", "userListInfo": "用户: {user}<br/>数量: {count}", "executeAfterTime": "{time}后执行", "audioCollection": "音频采集：", "powerSequencer": "电源时序器：", "cancelTodayTimer": "取消今日定时任务", "stopTimer": "停止定时任务"}, "labels": {"itemsPerPage": "每页数量", "listView": "列表视图", "zoneCamera": "分区摄像头显示", "zoneVolume": "分区音量显示", "audioCollector": "音频采集卡相关", "filename": "当文件名过长时才添加动画", "bindingCamera": "格式化绑定摄像头格式", "noDataTip": "格式化没有数据时候的提示", "addSearchFunction": "增加搜索功能", "pagination": "分页插件", "zoneControl": "分区总控制", "groupControl": "分组总控制", "listControl": "列表总控制", "timerControl": "定时任务控制", "manualControl": "手动任务控制", "playControl": "播放控制栏", "comments": "对话框相关", "soundCardDialog": "声卡采集对话框", "ttsDialog": "TTS对话框", "systemRegDialog": "系统注册对话框", "groupDialog": "分组对话框", "stopTaskDialog": "停止任务", "alarmDialog": "启动/停止一键告警", "playModeDialog": "应用播放模式到所有手动任务", "restoreTimerDialog": "恢复今日定时任务对话框", "cancelTimerDialog": "取消今日定时任务对话框", "stopTimerDialog": "停止定时任务对话框", "playSourceDialog": "播放节目源对话框", "errorSnackBar": "通用errorSnackBar", "successSnackBar": "通用successSnackBar", "audioSourceList": "音源列表", "mainZoneControl": "主分区控制", "tabSwitch": "选项卡切换", "hideProgress": "隐藏加载进度条", "tableView": "表格视图", "playModeSection": "播放模式", "previousTrack": "上一曲", "playPause": "播放/暂停", "nextTrack": "下一曲", "stopBtn": "停止", "controlButtons": "控制按钮", "alarmBtn": "一键告警", "monitorBtn": "监控控制", "listeningBtn": "开启监听控制", "closeListeningBtn": "关闭监听控制", "soundCardBtn": "声卡采集", "closeSoundCardBtn": "关闭声卡采集", "ttsBtn": "TTS语音", "playSourceBtn": "播放节目源", "idleBtn": "设置分区为空闲状态"}}, "playMode": {"single": "单曲播放", "singleLoop": "单曲循环", "sequential": "顺序播放", "loop": "循环播放", "random": "随机播放"}, "table": {"number": "编号", "user": "用户", "taskName": "任务名称", "zones": "分区", "playingSong": "播放歌曲", "playMode": "播放模式", "status": "状态", "name": "名称", "startTime": "开始时间", "endTime": "结束时间", "volume": "音量", "selectedZones": "选中分区", "selectedGroups": "选中分组", "audioSource": "播放音源", "operation": "操作", "deviceIP": "设备IP", "bindingCamera": "绑定摄像头", "runningStatus": "运行状态"}, "validation": {"maxCharacters": "最多 {max} 个字符", "textRequired": "文本不能为空", "ttsContentMaxLength": "文本内容不能超过5000个字符", "fileNameMaxLength": "文件名不能超过20个字符", "invalidLength": "字符长度无效，需要 {len} 个字符", "passwordRequirements": "密码必须包含大写字母、数字字符和特殊字符", "invalidInput": "输入不合法，请重新输入"}, "status": {"online": "在线", "offline": "离线", "unregistered": "未注册"}, "information": {"title": "请在信息发布客户端内操作！", "openClient": "点击打开客户端"}, "notFound": {"title": "404 Not Found", "message": "非常抱歉，你要找的页面不见了", "backToControl": "返回控制中心"}, "store": {"operation": "操作", "operationFailed": "操作失败", "success": "成功", "serverDisconnected": "服务器已断开连接", "conflictTimingPoints": "冲突定时点：\n", "music": "音乐", "enableTimingPoint": "启用定时点", "disableTimingPoint": "禁用定时点", "addUser": "添加用户", "removeUser": "移除用户", "editUser": "编辑用户", "setMonitorSpeaker": "设置监听音箱", "resetMonitorFunction": "未选择音箱，重置监听功能", "setSourceMonitor": "设置节目源监听", "stopMonitorSpeaker": "停止监听音箱", "setFireChannel": "设置消防通道", "textToSpeech": "文字转语音", "errors": {"unknownMistake": "当前操作不成功，请重新操作", "normalOperation": "操作正常", "inadequateUserRights": "用户权限不足", "userAlreadyExists": "用户已存在", "userDoesNotExist": "用户不存在", "userPasswordError": "用户密码错误", "userNotLoggedIn": "用户未登录", "userIsLoggedIn": "用户已登录", "targetDoesNotExist": "目标不存在", "targetDoesExist": "目标已存在", "numberOfTargetsDoesNotMatch": "目标数目不符", "targetFormatDoesNotMatch": "目标格式不符", "targetParameterDoesNotMatch": "目标参数不符", "busyTarget": "目标繁忙", "goalOffline": "目标离线", "partitionIdDoesNotMatch": "选中分区标识不符", "quantitativeLimit": "已达数量限制", "sipBusy": "当前sip设备繁忙中", "sipNumberOffline": "sip号码离线", "sipUnknownError": "SIP操作失败，请重新操作", "sipAccountDoesNotExist": "sip账号不存在", "sipDialingRulesDoNotExist": "sip拨号规则不存在", "sipDialingRulesAreNotUniform": "sip拨号规则不统一", "timingSchemeIdError": "定时方案ID错误", "timingPointIdError": "定时点ID错误", "timingSchemeNameIncorrect": "定时方案名称错误(名称为空字符或名称字符超出最大)", "timingPointNameIncorrect": "定时点名称错误(名称为空字符或名称字符超出最大)", "timingPointNameError": "日期时间错误", "specifiedDateEarlierThanToday": "指定日期早于今天", "startDateLaterThanEndDate": "开始日期晚于结束日期", "startDateTodayStartTimeEarlier": "开始日期为今天且开始时间早于现在", "startTimeLaterThanEndTime": "开始时间晚于结束时间", "noEquipmentSelected": "没有选择设备", "noSongSelected": "没有选择歌曲", "timingPointExceedsLimit": "定时点超出数量限制", "timingSchemeExceedsLimit": "定时方案超出数量限制", "waitingToReceivePartitionData": "等待接收分区分组数据", "upgradeFileDoesNotExist": "升级文件不存在", "syntheticParameterError": "合成参数错误", "speakerDoesNotExist": "发音人不存在", "syntheticTextTooLong": "合成文字过长", "synthesisFailure": "合成失败", "audioTooShort": "音频过短", "workingModeDoesNotMatch": "工作模式不符", "currentSourceCannotBeMonitored": "当前节目源不支持监听", "deviceDoesNotExist": "设备不存在", "deviceOffline": "设备离线", "logFileDownload": "日志文件下载中", "logFileDoesNotExist": "日志文件不存在", "registerFail": "服务器注册失败", "operationTimeout": "操作超时", "networkNotMatch": "存在终端设备与服务器的网络不在同一子网内,如果操作失败,请检查相应的网络配置", "timingPointConflict": "检测到定时点存在冲突，请解决后再次启用！", "manualAlarmSongNotExist": "未找到警报声文件sysAlarm.mp3,请上传", "existSubUserCanNotDeleted": "该账户下存在子账户，无法删除", "exceededStorageCapacity": "存储空间不足", "deviceNotSupportFunction": "设备不支持该功能"}}, "deviceStatus": {"offline": "离线", "idle": "空闲", "localPlay": "本地播放", "networkOnDemand": "网络点播", "scheduled": "定时", "audioMixing": "音频混音", "intercom": "对讲", "monitorEventTrigger": "监控事件触发", "monitoring": "监听", "fireAlarm": "消防告警", "networkPaging": "网络寻呼", "hundredVolt": "100V", "sipCall": "SIP通话", "api": "API", "netRadio": "网络电台", "phoneGateway": "电话网关", "audioCollection": "音频采集"}, "timer": {"deleteSchema": "删除定时方案", "aboutToDeleteSchema": "即将删除定时方案", "pleaseConfirm": "请确认", "setActiveSchema": "设置当前定时方案生效", "onlyOneSchemaActive": "提示：同一时间只能由一个定时方案有效", "aboutToSetSchemaActive": "即将设置该定时方案生效", "copySchema": "复制定时方案", "aboutToCopySchema": "即将复制定时方案", "deleteTimer": "删除定时点", "aboutToDeleteTimer": "即将删除定时点", "copyTimer": "复制定时点", "aboutToCopyTimer": "即将复制定时点", "sortTimer": "定时点排序", "aboutToSortTimer": "即将对所有定时点按开始时间进行升序排列", "createTimer": "新建定时点", "editTimer": "编辑定时点", "timerName": "定时点名称", "timerNamePlaceholder": "请输入定时点名称", "effective": "生效", "volumeFollowDevice": "音量跟随设备", "dateSelection": "1.日期选择", "weeklyLoop": "按星期循环", "specifyDate": "指定日期", "startDate": "开始日期", "endDate": "结束日期", "timeSelection": "2.时间选择", "startTime": "开始时间", "endTime": "结束时间", "manualInput": "手动输入：", "fixEndTime": "修正结束时间", "deviceSelection": "3.设备选择", "decoder": "解码终端", "powerSequencer": "电源时序器", "selectedZoneCount": "已选分区数量:", "zoneSelection": "分区选择", "selectedGroupCount": "已选分组数量:", "groupSelection": "分组选择", "selectedDeviceCount": "已选设备数量:", "selectedChannelCount": "已选通道数量:", "powerSequencerSelection": "电源时序器选择", "sourceSelection": "4.音源选择", "songPlay": "歌曲播放", "audioCollection": "音频采集", "noAudioCollector": "提示：未找到音频采集器，请检查设备。", "noPlaylist": "提示：播放列表为空，无法选择歌曲，请在控制中心添加播放列表", "playlist": "播放列表", "playMode": "播放模式", "listSongs": "列表歌曲", "addSingle": "添加单曲", "addAll": "添加全部", "deleteSingle": "删除单曲", "createSchema": "新建定时方案", "editSchema": "编辑定时方案", "schemaNamePlaceholder": "请输入定时方案名称", "powerSequencerSelectionTitle": "电源时序器选择", "reset": "重置", "zoneSelectionTitle": "分区选择", "zoneList": "分区列表", "selectedZones": "已选分区", "zoneKeywordPlaceholder": "请输入分区关键字", "close": "关闭", "groupSelectionTitle": "分组选择", "groupList": "分组列表", "selectedGroups": "已选分组", "groupKeywordPlaceholder": "请输入分组关键字", "timerScheme": "定时方案", "createTimerScheme": "新建定时方案", "editTimerScheme": "编辑定时方案", "deleteTimerScheme": "删除定时方案", "copyTimerScheme": "复制定时方案", "setCurrentSchemeActive": "设置当前方案生效", "search": "搜索", "timerPointSettings": "定时点设置", "createTimerPoint": "新建定时点", "editTimerPoint": "编辑定时点", "deleteTimerPoint": "删除定时点", "enableTimerPoint": "启用定时点", "disableTimerPoint": "禁用定时点", "copyTimerPoint": "复制定时点", "sortTimerPoints": "定时点排序", "edit": "编辑", "delete": "删除", "noTimerSchemeSet": "当前未设置定时方案", "headers": {"id": "编号", "user": "用户", "name": "名称", "timerMode": "定时模式", "playCycle": "播放周期", "startTime": "开始时间", "endTime": "结束时间", "playMode": "播放模式", "volume": "音量", "selectedZones": "选中分区", "selectedGroups": "选中分组", "playSource": "播放音源", "status": "状态", "actions": "操作", "selectedPartitions": "选中分区", "audioSource": "播放音源"}, "playModes": {"sequential": "顺序播放", "loop": "循环播放", "random": "随机播放"}, "validation": {"schemaNameRequired": "提示: 定时方案名称不能为空", "schemaNameLength": "提示: 输入超过字数限制"}, "messages": {"createSchemaSuccess": "新建定时方案成功", "editSchemaSuccess": "编辑定时方案成功", "deleteSchemaSuccess": "删除定时方案成功", "setActiveSchemaSuccess": "设置当前定时方案成功", "createTimerSuccess": "新建定时点成功", "editTimerSuccess": "编辑定时点成功", "deleteTimerSuccess": "删除定时点成功", "copySchemaSuccess": "复制定时方案成功", "copyTimerSuccess": "复制定时点成功", "sortTimerSuccess": "定时点排序成功", "noAudioCollectorSelected": "未选择音频采集器"}, "table": {"id": "编号", "user": "用户", "name": "名称", "timerMode": "定时模式", "playCycle": "播放周期", "startTime": "开始时间", "endTime": "结束时间", "playMode": "播放模式"}, "audioCollector": {"selected": "已选择音频采集器："}, "selectedSongs": "已选歌曲", "estimatedDuration": "预估播放时长", "pleaseSelectSchema": "请选择定时方案", "noTimerInCurrentSchema": "当前定时方案下无定时点配置", "noActiveSchema": "当前不存在生效的定时方案", "noTimerForCurrentAccount": "当前帐户无定时点配置", "errors": {"selectSchemaToEdit": "请选择一个定时方案进行编辑", "selectSchemaToDelete": "请选择一个定时方案进行删除", "selectSchemaToSet": "请选择一个定时方案进行设置", "selectSchemaToCopy": "请选择一个定时方案进行复制", "selectTimerToSet": "请选择一个定时点进行设置", "selectSchemaForTimer": "请选择一个定时方案进行定时点操作", "selectTimerToDelete": "请选择一个定时点进行定时点删除操作", "selectTimerToEdit": "请选择一个定时点进行定时点编辑操作", "timerNameEmpty": "定时点名称为空或不合法，请重新输入", "selectDateScheme": "请选择定时点日期方案", "startDateTooEarly": "定时点开始日期需晚于当前日期，请重新设置", "noStartTimeForFix": "未设置定时点开始时间，无法修正结束时间", "noSongsForFix": "未选择定时点歌曲，无法修正结束时间", "songsCannotFinishToday": "定时点歌曲不能在当日播放完毕，无法修正结束时间", "weekdayNotSelected": "定时点星期日期未选择", "endDateBeforeStartDate": "定时点结束日期需晚于开始日期，请重新设置", "startOrEndTimeNotSet": "定时点开始时间或结束时间未设置，请重新设置", "endTimeBeforeStartTime": "定时点结束时间需晚于开始时间，请重新设置", "needZoneOrGroup": "定时点需要至少添加一个分区或分组，请重新设置", "needPlayMode": "定时点需要设置播放模式，请重新设置", "noSongsSelected": "未选择定时点歌曲，请重新选择", "noAudioCollectorSelected": "未选择音频采集器，请重新选择", "noPowerSequencerSelected": "未选择电源时序器，请重新选择"}, "comments": {"commonSuccessSnackBar": "通用成功提示条", "commonErrorSnackBar": "通用错误提示条", "dialogs": "对话框", "copyTimerPoint": "复制定时点", "timerPointSorting": "定时点排序", "createEditTimerDialog": "新建定时点、编辑定时点通用对话框", "addDividerLine": "添加分割网格线", "audioSourceSelection": "音源列表选中", "setLabelSize": "设置标签大小", "songSelection": "歌曲选择"}, "steps": {"dateSelection": "1.日期选择", "timeSelection": "2.时间选择", "deviceSelection": "3.分区选择"}, "labels": {"duration": "时长"}, "modes": {"weeklyLoop": "按周循环", "dateLoop": "日期循环"}, "status": {"active": "生效", "inactive": "未生效"}, "weekdays": {"monday": "星期一", "tuesday": "星期二", "wednesday": "星期三", "thursday": "星期四", "friday": "星期五", "saturday": "星期六", "sunday": "星期日"}}, "system": {"registration": {"title": "系统注册", "status": "注册状态", "machineCode": "机器码", "code": "注册码", "codePlaceholder": "请输入注册码", "register": "注册", "unregistered": "未注册", "registered": "已注册", "registeredWithExpiry": "已注册（到期时间：{date}）", "dongleDetected": "检测到加密狗", "dongleExpired": "加密狗授权已过期", "codeExpired": "注册码授权已过期", "success": "注册成功, 即将跳转回登录界面", "failed": "注册失败，请检查注册码！", "codeRequired": "注册码不能为空，请重新输入"}, "info": {"title": "系统信息", "version": "系统版本", "webAddress": "网页地址", "authorization": "系统授权", "currentTime": "当前时间", "startTime": "启动时间", "runTime": "运行时间", "examMode": "考试模式", "enable": "启用", "examModeTooltip": "启用考试模式后，系统仅允许管理员登录"}, "network": {"title": "网络信息", "settings": "网络设置", "macAddress": "MAC地址", "ipAddress": "IP地址", "subnetMask": "子网掩码", "gateway": "网关", "dnsServer": "DNS服务器", "configInstruction": "请在下方输入相应的网络配置，点击按钮进行系统网络设置", "set": "设置", "configSuccess": "系统网络配置成功，即将跳转到登录界面", "fieldsRequired": "IP地址/子网掩码/网关/dns服务器不能为空，请重新输入", "invalidIp": "IP地址格式错误，请重新输入"}, "storage": {"title": "存储信息", "totalSpace": "总存储空间", "availableSpace": "可用存储空间", "totalMemory": "总内存", "availableMemory": "可用内存"}, "time": {"title": "设置系统日期时间", "instruction": "请在下方选择日期和时间后，点击设置按钮确认更改", "set": "设置", "success": "设置系统日期时间成功"}}, "account": {"removeUser": "移除用户", "aboutToRemoveUser": "即将移除用户", "pleaseConfirm": "请确认", "addUser": "添加用户", "editUser": "编辑用户", "accountField": "账号", "accountPlaceholder": "请输入账号（允许使用以字母开头且仅包含字母、数字和下划线的组合）", "nameField": "名称", "namePlaceholder": "请输入账号别名（支持中文，若与账号相同则可不填）", "usernameField": "用户名", "usernamePlaceholder": "请输入用户名", "passwordField": "密码", "passwordPlaceholder": "请输入账号密码", "selectParent": "选择上级", "allowRepeatedLogin": "允许重复登录", "allowRepeatedLoginTooltip": "启用后允许该账号重复登录", "globalPlaylistPermission": "全局播放列表管理权限", "globalPlaylistPermissionTooltip": "启用后可以管理所有用户的播放列表", "globalTimerPermission": "全局定时点管理权限", "globalTimerPermissionTooltip": "启用后可以管理所有用户的定时点", "audioCollectorPermission": "音频采集器使用权限", "audioCollectorPermissionTooltip": "启用后可以使用音频采集器", "deviceKeywordPlaceholder": "请输入分区关键字", "deviceList": "设备列表", "selectedDevices": "已选设备", "deviceType": "设备类型", "save": "保存", "advancedSettings": "高级设置", "userPermissions": "用户权限", "allowCreateSubAccount": "允许创建子账户", "allowCreateSubAccountTooltip": "启用后允许该账号创建子账户", "setUserPermissions": "设置用户权限", "storageSpace": "存储空间", "totalStorageSpace": "总存储空间", "usedSpace": "已用空间", "remainingSpace": "剩余空间", "setStorageSpace": "设置存储空间(MB)", "storageSpaceHint": "大小范围：50MB~1TB (1048576MB)", "setStorageSpaceButton": "设置存储空间", "accountManagement": "账户管理", "search": "搜索", "loadingText": "正在查询账户信息，请稍候", "notBoundZones": "未绑定分区", "allZones": "所有分区", "serialNumber": "序号", "account": "账号", "name": "名称", "boundDeviceCount": "绑定设备数量", "operation": "操作", "edit": "编辑", "advanced": "高级", "delete": "删除", "headers": {"serialNumber": "序号", "account": "账号", "name": "名称", "password": "密码", "accountType": "账号类型", "boundDeviceCount": "绑定设备数量", "operation": "操作"}, "accountTypes": {"admin": "管理员", "level2": "二级用户", "level3": "三级用户", "level4": "四级用户"}, "messages": {"selectUserToEdit": "请选择一位用户进行编辑", "selectUserToDelete": "请选择一位用户进行删除", "accountLengthError": "账号长度错误，请输入3~16位字符", "passwordLengthError": "密码长度错误，请输入3~16位字符", "cannotDeleteWithSubUsers": "该账户下存在子账户，无法删除！", "setStorageSuccess": "设置存储空间成功", "setPermissionSuccess": "设置用户权限成功", "enterCorrectStorageSize": "请输入正确的存储空间大小数字", "storageRangeError": "存储空间有效大小范围为50MB~1TB (1048576MB)"}, "validation": {"required": "请填入数字", "number": "请填入数字", "storageRange": "有效范围：50MB~1TB (1048576MB)"}, "permissions": {"allowRepeatedLogin": "允许重复登录", "allowRepeatedLoginTooltip": "启用后允许该账号重复登录", "globalPlaylistManagement": "全局播放列表管理权限", "globalPlaylistManagementTooltip": "启用后可以管理所有用户的播放列表", "globalTimerManagement": "全局定时点管理权限", "globalTimerManagementTooltip": "启用后可以管理所有用户的定时点", "audioCollectorUsage": "音频采集器使用权限", "audioCollectorUsageTooltip": "启用后可以使用音频采集器"}, "table": {"serialNumber": "序号", "account": "账号", "name": "名称", "boundDeviceCount": "绑定设备数量", "actions": "操作"}, "actions": {"edit": "编辑", "advanced": "高级", "delete": "删除"}, "errors": {"hasSubAccounts": "该账户下存在子账户，无法删除！", "selectUserToEdit": "请选择一位用户进行编辑", "selectUserToDelete": "请选择一位用户进行删除", "accountLengthError": "账号长度错误，请输入3~16位字符", "passwordLengthError": "密码长度错误，请输入3~16位字符", "invalidStorageSize": "请输入正确的存储空间大小数字", "storageSizeRange": "存储空间有效大小范围为50MB~1TB (1048576MB)"}, "userTypes": {"admin": "管理员", "secondLevel": "二级用户", "thirdLevel": "三级用户", "fourthLevel": "四级用户"}}, "log": {"title": "日志管理", "tabs": {"systemLog": "系统日志", "callRecord": "广播录音"}, "deviceSelection": "设备选择", "deviceSelectionHint1": "提示： 指定", "deviceSelectionHint2": "单个", "deviceSelectionHint3": "设备用于日志查询", "deviceKeywordPlaceholder": "请输入设备关键字", "deviceList": "设备列表", "selectedDevices": "已选设备", "logType": "日志类型", "deviceType": "设备类型", "callerDevice": "主叫设备", "selected": "已选", "startDate": "开始日期", "endDate": "结束日期", "query": "查询", "downloadCurrentLog": "下载当前日志", "callRecord": {"autoRecordSwitch": "自动录音开关", "enable": "启用", "disable": "禁用", "serialNumber": "序号", "recordId": "录音ID", "callId": "通话ID", "callerMac": "主叫MAC", "callerName": "主叫名称", "calleeList": "被叫列表", "recordType": "录音类型", "recordStatus": "录音状态", "startTime": "开始时间", "endTime": "结束时间", "duration": "时长(秒)", "filePath": "文件路径", "fileSize": "文件大小", "download": "下载录音", "play": "播放录音", "playError": "播放失败", "downloadError": "下载失败", "comingSoon": "功能开发中...", "deleteConfirmMessage": "确定要删除这条通话录音吗？删除后无法恢复。", "recordStatusRecording": "录音中", "recordStatusCompleted": "录音完成", "recordStatusError": "录音错误", "recordStatusInterrupted": "录音中断", "deviceListEllipsis": "{firstDevice}等{count}台设备", "callType": "通话类型", "callTypeBroadcast": "广播寻呼", "callTypeIntercom": "双向对讲"}, "logTypes": {"all": "全部日志", "runtime": "运行日志", "playback": "播放日志", "sync": "同步日志", "fire": "消防日志", "call": "通话日志", "monitor": "监听日志", "paging": "寻呼日志", "advanced": "高级操作日志"}, "deviceTypes": {"allDevices": "全部设备", "none": "无", "decoder": "解码终端", "pager": "智能寻呼台", "audioCollector": "音频采集器", "fireCollector": "消防采集器", "powerSequencer": "电源时序器", "audioProcessor": "音频协处理器", "audioRepeater": "音频中继器", "remoteController": "远程遥控器", "phoneGateway": "电话网关", "ampController": "功放控制器", "noiseDetector": "噪声自适应终端"}, "headers": {"serialNumber": "序号", "date": "日期", "time": "时间", "deviceNameOrUser": "设备名称/操作用户", "logType": "日志类型", "logContent": "日志内容"}, "messages": {"queryLogSuccess": "查询日志成功", "querySuccess": "查询日志成功", "pleaseSelectLogDate": "请选择查询日志的日期", "accountOnlySupportsSpecificDevice": "当前账号仅支持指定设备查询，请重新选择设备", "pleaseSelectDeviceOrSwitchMode": "当前模式为查询单个设备日志，请选择设备或切换查询模式", "pleaseSelectOneDevice": "请选择一个设备进行日志查询，请重试", "canOnlySelectSingleDevice": "只能选择单个设备进行日志查询，请重试"}, "tableHeaders": {"serialNumber": "序号", "date": "日期", "time": "时间", "deviceNameOrUser": "设备名称/操作用户", "logType": "日志类型", "logContent": "日志内容"}}, "settings": {"themeSettings": "主题设置", "darkMode": "深色模式", "sidebarBackground": "边栏背景", "backgroundImage": "背景图像", "guideSettings": "引导设置", "documentation": "说明文档"}, "maintenance": {"aboutToPerform": "即将进行", "pleaseConfirm": "请确认操作", "uploadConfigFile": "上传配置文件", "selectConfigFile": "选择配置文件", "dataReset": "数据重置", "selectDataToReset": "请选择需要重置的数据项并确认", "factoryReset": "恢复出厂设置", "factoryResetWarning": "警告: 恢复到默认出厂设置后，您的所有配置将会丢失！！", "factoryResetCaution": "非必要情况无需恢复出厂，请谨慎操作！", "serverRestart": "服务器重启", "serverRestartWarning": "警告: 重启服务器将中断正在进行的所有音频播放！！", "serverRestartNotice": "服务器重启后将回到登录页面，请稍等片刻再重新登录！", "backupRestore": "备份/还原", "serverConfigFileCount": "服务器当前配置文件数量:", "delete": "删除", "masterBackupServer": "主备服务器", "serverType": "服务器类型", "masterServer": "主服务器", "backupServer": "备用服务器", "serverStatus": "服务器状态", "functionSwitch": "功能开关", "disableMasterBackup": "停用主备", "enableMasterBackup": "启用主备", "masterServerIP": "主服务器IP", "backupServerIP": "备用服务器IP", "setBackupServer": "设置备用服务器", "systemUpgrade": "系统升级", "currentServerVersion": "当前服务器版本:", "upgradeWarning": "警告:服务器升级将中断正在进行的所有音频播放，升级成功后系统将自动重启！！", "selectUpgradePackage": "选择升级包", "serverUpgrade": "服务器升级", "dataTypes": {"groupFiles": "分组文件", "playlistFiles": "播放列表文件", "timerFiles": "定时文件", "zoneFiles": "分区文件", "pagerFiles": "寻呼台文件", "audioCollectionFiles": "音频采集文件", "fireCollectionFiles": "消防采集文件", "monitorDevices": "监控设备", "upgradeFirmware": "升级固件", "powerSequencerFiles": "电源时序器文件", "logFiles": "日志文件"}, "tabs": {"dataReset": "数据重置", "backupRestore": "备份/还原", "masterBackupServer": "主备服务器", "systemUpgrade": "系统升级"}, "operations": {"serverUpgrade": "服务器升级", "resetSpecificData": "重置指定数据", "factoryReset": "恢复出厂设置", "serverRestart": "服务器重启", "deleteConfigFile": "删除配置文件", "restoreConfigFile": "还原配置文件", "backupServerSettings": "备用服务器设置"}, "messages": {"resetDataSuccess": "重置指定数据成功", "factoryResetSuccess": "恢复出厂设置成功, 即将跳转回登录界面", "uploadUpgradePackageSuccess": "上传服务器升级包成功，请求服务器升级", "uploadUpgradePackageFailed": "上传服务器升级包失败", "backupSuccess": "服务器备份数据成功", "removeBackupFileSuccess": "移除备份文件成功", "uploadConfigFileSuccess": "上传配置文件成功", "uploadConfigFileFailed": "上传配置文件失败", "restoreDataSuccess": "服务器还原数据成功", "setBackupServerSuccess": "设置主备服务器成功", "pleaseSelectConfigFile": "请选择配置文件上传", "pleaseSelectUpgradePackage": "请选择升级包上传", "invalidBackupFileFormat": "文件名格式不正确，请上传文件名包含{identifier}的tar.gz格式的备份包", "invalidUpgradeFileFormat": "文件名格式不正确，请上传文件名以{prefix}开头的tar.gz格式的固件包"}, "serverSyncStatus": {"masterConnectedToBackup": "已连接至备用服务器", "masterConnectingToBackup": "正在连接备用服务器", "masterSyncDisabled": "未启用主备服务器功能", "masterNoBackupSpecified": "未指定备用服务器", "masterBackupOffline": "备用服务器已离线", "masterConnectFailedWaitAuth": "连接备用服务器失败(等待授权)", "masterConnectFailedAuth": "连接备用服务器失败(授权失败)", "masterConnectFailedVersion": "连接备用服务器失败(程序不匹配)", "masterConnectFailedNetwork": "连接备用服务器失败(网络异常)", "backupConnectedToMaster": "已连接至主服务器", "backupNotConnectedToMaster": "未连接至主服务器", "backupRefusedConnection": "主服务器连接已被拒绝", "backupChangedToMaster": "已切换至主服务器模式"}, "backup": {"currentConfigCount": "服务器当前配置文件数量"}, "actions": {"backup": "备份", "upload": "上传", "export": "导出", "restore": "还原"}, "noConfigFilesInServer": "服务器中没有配置文件"}, "media": {"createNewList": "新建列表", "enterNewListName": "请输入新列表名称", "renameList": "重命名列表", "deleteList": "删除列表", "aboutToDeleteList": "即将删除列表", "addSong": "添加歌曲", "selectFromLibrary": "从曲库选择", "uploadNewSong": "上传新歌曲", "enterSongKeyword": "请输入歌曲关键字", "songList": "歌曲列表", "selectedSongs": "已选歌曲", "selectSongsToUpload": "请选择歌曲上传，支持mp3和wav格式", "deleteSong": "删除歌曲", "aboutToDeleteSelectedSongs": "即将删除已选", "songsConfirm": "首歌曲,请确认", "auditSong": "审核歌曲", "aboutToAuditSelected": "即将审核已选", "downloadSong": "下载歌曲", "aboutToDownloadSelected": "即将下载已选", "downloadSelectedSongs": "下载已选歌曲", "downloadTip": "提示：建议单次下载歌曲数量不超过 5 首", "uploadSong": "上传歌曲", "listManagement": "列表管理", "libraryManagement": "曲库管理", "search": "搜索", "enableSongSorting": "开启歌曲排序", "dragOrSelectToSort": "拖拽或选中分区排序", "sortByName": "按歌曲名称排序", "sortByDuration": "按歌曲时长排序", "moveUpSelected": "上移所选歌曲", "moveDownSelected": "下移所选歌曲", "cancelSortChanges": "取消排序修改", "confirmSortChanges": "确认排序修改", "noSongsInSelectedList": "已选中列表未添加歌曲", "noListSelected": "未选中列表", "normal": "正常", "invalid": "失效", "audited": "已审核", "pendingAudit": "待审核", "preview": "预览", "delete": "删除", "aboutToDeleteSong": "即将删除歌曲", "storageInfo": "当前账户总存储空间：{total}MB，已用空间：{used}MB，剩余空间：{remaining}MB", "music": "音乐", "noLocalSongFiles": "本地歌曲文件不存在，请上传", "songTypes": {"tts": "TTS", "music": "音乐"}, "messages": {"createListSuccess": "新建列表成功", "renameListSuccess": "重命名列表成功", "deleteListSuccess": "删除列表成功", "uploadSongSuccess": "上传歌曲成功", "addSongToListSuccess": "添加歌曲至列表成功", "deleteSongFromLibrarySuccess": "从曲库中删除指定歌曲成功", "deleteSongFromListSuccess": "从列表中删除指定歌曲成功", "auditSongSuccess": "歌曲审核成功", "sortSongSuccess": "指定列表歌曲排序成功"}, "errors": {"pleaseSelectSongs": "请选择歌曲上传", "maxSongsLimit": "单次最多允许上传50首歌曲", "totalSizeLimit": "单次上传歌曲总大小超过500MB", "invalidFileFormat": "请上传后缀名为mp3或wav的歌曲文件", "uploadFailed": "上传失败", "invalidInput": "输入不合法，请重新输入", "listNameExists": "列表名已存在，请重新输入", "cannotModifyList": "该列表不允许修改"}, "validation": {"listNameRequired": "提示: 列表名不能为空", "listNameLength": "提示: 输入超过字数限制", "nameRequired": "提示: 用户名不能为空", "nameLength": "提示: 输入超过字数限制"}, "tips": {"auditedSongsOnly": "提示：本页面仅显示曲库中已审核但该列表尚未添加的歌曲", "existingSongsOnly": "提示：本页面仅显示曲库中存在但该列表尚未添加的歌曲"}, "tabs": {"selectFromLibrary": "从曲库选择", "uploadNewSong": "上传新歌曲", "listManagement": "列表管理", "songManagement": "曲库管理", "radioManagement": "电台管理"}, "radio": {"radioManagement": "电台管理", "radioGroups": "电台分组", "radioList": "电台列表", "radioName": "电台名称", "radioUrl": "电台地址", "radioGroup": "所属分组", "createTime": "创建日期", "listen": "试听", "noRadiosInGroup": "该分组暂无电台", "selectGroup": "请选择分组", "loadingRadios": "加载电台中...", "loadingGroups": "加载分组中...", "search": "搜索电台分组", "noGroupSelected": "请先选择电台分组", "fetchGroupError": "获取电台分组失败", "fetchDetailError": "获取电台详情失败"}, "upload": {"selectSongsLabel": "请选择歌曲上传，支持mp3和wav格式"}, "actions": {"uploadSong": "上传歌曲", "auditSong": "审核歌曲", "downloadSong": "下载歌曲"}, "table": {"serialNumber": "序号", "name": "名称", "type": "类型", "duration": "时长", "bitrate": "比特率(Kbps)", "size": "大小", "user": "用户", "auditStatus": "审核状态", "status": "状态", "actions": "操作"}}, "index": {"serverDisconnectedReconnecting": "服务器断开连接，正在重新连接，请稍后", "serverRestarting": "服务器重启中,请稍后", "serverConnectionError": "服务器连接异常，即将返回登录页面", "serverReconnectSuccess": "服务器重连成功", "reloginReasons": {"permissionChanged": "用户权限更改", "userRemoved": "用户已移除", "loginElsewhere": "用户在其他地方登录", "systemRestart": "系统重启", "accountAbnormal": "账号异常"}, "reloginMessage": "{reason}，即将返回登录页面", "reconnectAttempt": "服务器连接异常，尝试重新连接，当前重试次数: {count}"}, "common": {"confirm": "确认", "cancel": "取消", "save": "保存", "delete": "删除", "edit": "编辑", "add": "添加", "close": "关闭", "loading": "加载中...", "success": "成功", "error": "错误", "warning": "警告", "info": "信息", "machineCode": "机器码", "enterRegistrationCode": "请输入注册码", "operationSuccess": "操作成功", "operationFailed": "操作失败", "search": "搜索", "pleaseConfirm": "请确认", "upload": "上传", "none": "无", "status": "状态", "number": "编号", "name": "名称", "actions": "操作", "user": "用户", "unknown": "未知", "enabled": "已启用", "disabled": "已禁用", "confirmDelete": "确认删除", "deleteSuccess": "删除成功"}, "monitor": {"monitorSettings": "监控设置", "ipAddress": "IP地址", "cameraName": "摄像头名称", "rtspAddress": "RTSP地址", "username": "用户名", "password": "密码", "batchSettings": "批量设置", "deleteMonitorDevice": "删除监控设备", "aboutToDeleteMonitorDevice": "即将删除监控设备", "videoMonitorSwitch": "视频监控开关", "aboutToToggleMonitor": "即将{action}视频监控功能，", "enable": "启用", "disable": "关闭", "monitorSwitchNotice": "注意：变更视频监控功能开关后，请重启服务器！", "addCustomMonitorDevice": "添加自定义监控设备", "editMonitor": "编辑监控", "batchEdit": "批量编辑", "search": "搜索", "noMonitorDevicesFound": "没有搜索到监控设备", "edit": "编辑", "delete": "删除", "headers": {"serialNumber": "序号", "addMethod": "加入方式", "cameraName": "摄像头名称", "ipAddress": "IP地址", "mac": "MAC", "username": "用户名", "status": "状态", "actions": "操作"}, "status": {"offline": "离线", "loggedIn": "已登录", "loginFailed": "登录失败", "unknown": "未知"}, "addMethod": {"auto": "自动", "manual": "手动"}, "actions": {"batchSet": "批量设置", "enable": "启用", "disable": "关闭"}, "messages": {"aboutTo": "即将", "videoMonitoringFunction": "视频监控功能", "restartWarning": "注意：变更视频监控功能开关后，请重启服务器！", "noMonitorDevices": "没有搜索到监控设备", "setMonitorInfoSuccess": "设置监控设备信息成功", "addCustomMonitorSuccess": "添加自定义监控设备成功", "deleteMonitorSuccess": "删除监控设备成功", "monitorSwitchSuccess": "已{action}视频监控，重启系统后生效", "requiredFieldsEmpty": "必填项不能为空，请重试", "rtspAddressError": "RTSP地址错误，请重新输入"}, "table": {"serialNumber": "序号", "joinMethod": "加入方式", "cameraName": "摄像头名称", "ipAddress": "IP地址", "mac": "MAC", "username": "用户名", "status": "状态", "actions": "操作"}, "errors": {"requiredFieldsEmpty": "必填项不能为空，请重试", "invalidRTSPAddress": "RTSP地址错误，请重新输入"}}, "partitionSystem": {"dialogs": {"editDeviceName": "修改设备名称", "deviceUpgrade": "设备升级", "modifyDeviceIP": "修改设备IP", "intercomSettings": "对讲设置", "triggerSettings": "触发设置", "editNetworkMode": "修改设备网络模式", "sipSettings": "SIP设置", "informationPublish": "信息发布", "advancedVolumeSettings": "音量高级设置", "taskManagement": "任务管理", "newEditTask": "新建/编辑任务", "taskPartitionSelection": "任务分区选择", "partitionSelection": "分区选择", "groupSelection": "分组选择", "soundEffectSettings": "音效设置", "monitorBinding": "监控绑定", "alarmSoundSelection": "告警音效选择", "triggerSongSelection": "触发歌曲选择", "monitorSpeaker": "监听音箱", "fireManagement": "消防管理", "powerManagement": "电源管理", "restartDevice": "重启设备", "resetDeviceData": "重置设备数据", "bluetoothSettings": "蓝牙设置", "deleteDevice": "删除设备", "deleteTask": "删除任务", "noiseAdaptiveSettings": "噪声自适应终端设置", "keyManagement": "按键管理", "audioCollectorSettings": "采播设置", "audioCollectorPartitionSelection": "采播设置-分区选择", "audioMixerSettings": "混音设置", "audioMixerPartitionSelection": "混音设置-分区选择", "phoneGatewaySettings": "电话网关设置", "phoneGatewayPartitionSelection": "电话网关设置-分区选择", "ampControllerStatus": "功放控制器状态"}, "labels": {"enterNewName": "请输入新名称", "deviceName": "设备名称", "currentFirmwareVersion": "当前固件版本", "selectServerFirmware": "选择服务器已有固件", "upgradeCurrentDeviceOnly": "仅升级当前设备(关闭时升级所有同类型设备）", "serverFirmware": "服务器固件", "uploadNewFirmware": "本地上传新固件", "autoGetIpAddress": "自动获取IP地址", "ipAddress": "IP地址", "subnetMask": "子网掩码", "defaultGateway": "默认网关", "key1": "按键1", "key2": "按键2", "micInputVolumeLevel": "麦克风输入音量等级", "intercomOutputVolumeLevel": "对讲输出音量等级", "setAllSameTypeDevices": "设置所有同类型设备", "enableNoiseAdaptive": "启用噪声自适应", "noiseDetectorChannels": "噪声检测器通道", "inactive": "无效", "averageNoiseLevel": "平均噪声值", "noiseVolumeSettings": "噪声/音量设置", "segment": "段", "noiseLevel": "噪声值", "volumeLevel": "音量值", "bindPartitions": "绑定分区", "availablePartitions": "可用分区", "addPartition": "添加分区", "removePartition": "移除分区", "fixedValue": "固定值", "triggerSwitch": "触发开关", "enable": "开启", "triggerMode": "触发方式", "levelTrigger": "电平触发", "shortCircuitTrigger": "短路触发", "triggerSong": "触发歌曲", "triggerVolume": "触发音量", "networkMode": "网络模式", "mainServerIp": "主服务器IP地址", "mainServerPort": "主服务器端口号", "backupServerIp": "备用服务器IP地址", "backupServerPort": "备用服务器端口号", "sipSwitch": "SIP开关", "callVolume": "通话音量", "transportProtocol": "传输协议", "serverIpAddress": "服务器IP地址", "serverPort": "服务器端口号", "sipAccount": "SIP账号", "sipPassword": "SIP密码", "enterDisplayContent": "请输入显示内容", "selectEffect": "请选择特效", "moveSpeed": "移动速度（数值范围：1-64；数值越小,速度越快)", "stayTime": "停留时间（数值范围：1-255；单位秒)", "displaySwitch": "显示开关", "globalSubVolume": "全局子音量", "localVolume": "本地音量", "taskName": "任务名称", "selectedPartitions": "已选分区", "selectedGroups": "已选分组", "songPlayback": "歌曲播放", "audioCollection": "音频采集", "playlist": "播放列表", "playlistSongs": "列表歌曲", "doubleClickToAdd": "双击添加", "selectedSongs": "已选歌曲", "doubleClickToDelete": "双击删除", "partitionList": "分区列表", "groupList": "分组列表", "currentSoundEffect": "当前音效", "selectSoundEffect": "请选择音效", "currentBinding": "当前绑定", "currentSelection": "当前选择", "disabled": "失效", "channelName": "通道名称", "triggered": "已触发", "notTriggered": "未触发", "currentTriggerPartitions": "当前触发分区", "controlMode": "控制模式", "manualMode": "手动模式", "autoMode": "自动模式", "open": "打开", "restartAllSameTypeDevices": "重启所有同类型设备", "resetAllSameTypeDevicesData": "重置所有同类型设备数据", "bluetoothName": "蓝牙名称", "bluetoothEncryption": "蓝牙加密方式", "noPasswordRequired": "无需密码", "passwordRequired": "需要密码", "bluetoothPassword": "蓝牙密码", "alarmSound": "告警音效", "triggerPartitionCount": "触发分区数量", "triggerStatus": "触发状态", "switch": "开关", "playMode": "播放模式", "playVolume": "播放音量", "playSource": "播放音源", "featuresAndOperations": "特性及操作", "currentAlarmSound": "当前告警音效", "autoTrigger": "自动触发", "partitionVolume": "分区音量", "selectedPartitionCount": "已选分区数量", "mixingSwitch": "混音开关", "triggerType": "触发类型", "functionSwitch": "功能开关", "amplifierControllerStatus": "功放控制器状态", "masterAmplifier": "主功放", "backupAmplifier": "备用功放", "backupAmplifierStatus": "备用功放状态", "statusSimulationControl": "状态模拟控制", "dragOrSelectPartitionSort": "拖拽或选中分区排序"}, "buttons": {"upgrade": "升级", "cancel": "取消", "confirm": "确定", "close": "关闭", "resetChanges": "重置修改", "confirmChanges": "确认修改", "edit": "编辑", "delete": "删除", "createTask": "新建任务", "editTask": "编辑任务", "deleteTask": "删除任务", "selectPartitions": "分区选择", "selectGroups": "分组选择", "addAll": "添加全部", "setCurrentChannel": "设置当前通道", "setAllChannels": "设置所有通道", "allOn": "全开", "allOff": "全关", "saveSort": "保存排序", "bluetoothSettings": "蓝牙设置", "intercomSettings": "对讲设置", "sipSettings": "SIP设置", "informationPublish": "信息发布", "monitorSpeaker": "监听音箱", "deviceName": "设备名称", "deviceVolume": "设备音量", "deviceUpgrade": "设备升级", "ipSettings": "IP设置", "networkMode": "网络模式", "soundEffectSettings": "音效设置", "triggerSettings": "触发设置", "monitorBinding": "监控绑定", "fireManagement": "消防管理", "powerManagement": "电源管理", "audioCollectorSettings": "采播设置", "parameterSettings": "参数设置", "gatewaySettings": "网关设置", "ampSettings": "功放设置", "taskManagement": "任务管理", "keyManagement": "按键管理", "restartDevice": "重启设备", "resetDevice": "重置设备", "sortByDeviceName": "按设备名称排序", "sortByDeviceIP": "按设备IP排序", "partitionSelection": "分区选择"}, "steps": {"deviceSelection": "1.设备选择", "audioSourceSelection": "2.音源选择"}, "placeholders": {"enterTaskName": "请输入任务名称", "enterPartitionKeyword": "请输入分区关键字", "enterGroupKeyword": "请输入分组关键字", "enterChannelName": "请输入通道名称"}, "tips": {"volumeCalculation": "提示：设备输出音量等于主音量与子音量的乘积", "noAudioCollectorFound": "提示：未找到音频采集器，请检查设备。", "playlistEmpty": "提示：播放列表为空，无法选择歌曲，请在控制中心添加播放列表", "noMonitorBound": "当前未绑定监控，请选择监控设备进行绑定", "noAlarmSoundSelected": "当前未选择告警音效，请选择一个节目源作为告警音效", "noTriggerSongSelected": "当前未选择触发歌曲，请选择一个节目源作为触发歌曲", "monitorSpeakerWarning": "提示： 只能设置一个分区作为监听音箱，如果不设置，监听功能将", "noFireCollectorChannelInfo": "没有搜索到当前消防采集器通道信息", "noPowerChannelInfo": "没有搜索到当前电源通道信息", "aboutToRestartDevice": "即将重启设备", "pleaseConfirm": "请确认", "aboutToResetDevice": "即将重设设备", "andRestoreFactorySettings": "并恢复出厂设置，请确认", "aboutToDeleteDevice": "即将删除设备"}, "warnings": {"networkModeWarning": "警告：即将修改设备网络模式，可能造成设备无法上线等严重后果，非调试人员请勿操作。", "factoryResetWarning": "警告：设备即将恢复出厂设置，可能造成设备无法上线等严重后果，非调试人员请勿操作。"}, "messages": {"deviceNameEmpty": "设备名称不能为空", "deviceNameExists": "设备名称已存在", "deviceNameTooLong": "设备名称过长，请重新输入", "selectFirmwareForUpgrade": "请选择固件进行升级", "selectFirmwareForUpload": "请选择固件上传", "firmwareNameFormatError": "文件名格式不正确，当前设备只支持文件名以{prefix}开头的tar.gz格式固件", "ipAddressCannotBeEmpty": "IP地址/子网掩码/默认网关不能为空，请重新输入", "soundEffectNotChanged": "音效未修改，请重新选择", "selectFireCollectorChannel": "请选择一个消防采集器通道进行操作", "channelNameCannotBeEmpty": "通道名称不能为空，请重新输入", "channelAlarmSoundNotSet": "当前通道告警音效未设置，请重新设置", "channelNotBoundToPartition": "当前通道未绑定分区，请重新设置", "canOnlySetOneMonitorSpeaker": "只能设置一个分区作为监听音箱，请重试", "monitorSpeakerSameAsCurrent": "监听音箱与当前一致，请重新选择", "monitorDeviceNotSelected": "未选择监控设备，请重试", "canOnlyDeleteOfflineDevices": "只能删除离线设备，请重试", "taskNameEmptyOrInvalid": "任务名称为空或不合法，请重新输入", "taskNeedsAtLeastOnePartitionOrGroup": "任务需要至少添加一个分区或分组，请重新设置", "taskNeedsPlayMode": "任务需要设置播放模式，请重新设置", "noTaskSongsSelected": "未选择任务歌曲，请重新选择", "noAudioCollectorSelected": "未选择音频采集器，请重新选择", "selectTaskForDelete": "请选择一个任务进行删除操作", "noFirmwareAvailable": "服务器中没有可供升级的固件", "operationSuccess": "操作成功", "operationFailed": "操作失败", "textCannotBeEmpty": "文本不能为空", "bluetoothNameFormat": "蓝牙名称为数字和字母组成的32位以内字符", "bluetoothPasswordFormat": "蓝牙密码固定为4位数字", "phoneNumberFormat": "电话号码格式错误", "nameConflictOrInvalid": "名称重复或不合法，请重试", "intercomSettingsSuccess": "设置对讲终端参数成功", "triggerSettingsSuccess": "设置触发参数成功", "audioCollectorSettingsSuccess": "设置采播参数成功", "parameterSettingsSuccess": "设置参数成功", "canOnlyDeleteOfflineDevice": "只能删除离线设备，请重试", "taskNeedsPartitionOrGroup": "任务需要至少添加一个分区或分组，请重新设置", "textLengthExceedsLimit": "文本长度不能超过 {maxBytes} 字节", "channelNameExceedsLimit": "通道名称不能超过 {maxBytes} 字节"}, "status": {"normal": "正常", "fault": "故障", "idle": "空闲", "unknown": "未知", "registrationFailed": "注册失败", "registrationSuccess": "注册成功", "registering": "注册中", "registrationTimeout": "注册超时", "accountPasswordError": "账户/密码错误", "inCall": "通话中"}, "effects": {"auto": "自动", "pageFlip": "翻页", "continuousLeftMove": "连续左移", "leftMoveWithStay": "左移（带停留）", "continuousDownMove": "连续下移", "downMoveWithStay": "下移（带停留）", "blink": "闪烁", "continuousUpMove": "连续上移", "upMoveWithStay": "上移（带停留）", "snow": "飘雪", "autoWithDescription": "自动（当屏幕空间足够显示时静止，否则连续左移）", "pageFlipWithDescription": "翻页（当屏幕空间足够显示时静止，否则翻页显示）"}, "triggers": {"levelTrigger": "电平触发", "shortCircuitTrigger": "短路触发"}, "tooltips": {"audioCollection": "音频采集：", "selectedSongs": "已选歌曲", "estimatedPlayDuration": "预估播放时长", "songDurationEach": "每首歌曲加1s", "selectedAudioCollector": "已选择音频采集器："}, "confirmations": {"aboutToDeleteTask": "即将删除任务", "pleaseConfirm": "请确认", "aboutToDeleteDevice": "即将删除设备", "deleteTask": "删除任务", "keyManagement": "按键管理", "audioCollectorSettings": "采播设置", "parameterSettings": "参数设置", "partitionSelection": "分区选择"}, "deviceTypes": {"smartPagerA": "智能寻呼台A", "smartPagerB": "智能寻呼台B", "smartPagerC": "智能寻呼台C", "decoderTerminalA": "解码终端A", "decoderTerminalB": "解码终端B", "decoderTerminalC": "解码终端C", "decoderTerminalD": "解码终端D", "decoderTerminalE": "解码终端E", "decoderTerminalF": "解码终端F", "decoderTerminalG": "解码终端G", "fireCollectorA": "消防采集器A", "fireCollectorB": "消防采集器B", "fireCollectorC": "消防采集器C", "fireCollectorF": "消防采集器F", "audioCollectorA": "音频采集器A", "audioCollectorB": "音频采集器B", "audioCollectorC": "音频采集器C", "audioCollectorF": "音频采集器F", "powerSequencerA": "电源时序器A", "powerSequencerB": "电源时序器B", "powerSequencerC": "电源时序器C", "powerSequencerF": "电源时序器F", "audioRelay": "音频中继器", "audioRelayC": "音频中继器C", "remoteController": "远程遥控器", "remoteControllerC": "远程遥控器C", "remoteControllerF": "远程遥控器F", "phoneGateway": "电话网关", "ampController": "功放主备控制器", "noiseAdaptiveTerminal": "噪声自适应终端", "audioProcessor": "音频协处理器"}, "priorities": {"default": "默认（低于定时音源）", "high": "高优先级（高于定时音源）"}, "ampStatus": {"workingBackup": "工作中 (备份主功放 {id})"}, "keyActions": {"none": "无", "pauseResume": "暂停/恢复", "stop": "停止", "previous": "上一曲", "next": "下一曲", "volumeUp": "音量加", "volumeDown": "音量减"}, "triggerModes": {"levelTrigger": "电平触发", "shortCircuitTrigger": "短路触发"}, "soundEffects": {"off": "关闭", "pop": "流行", "dance": "舞曲", "rock": "摇滚", "classical": "古典", "vocal": "人声", "soft": "柔和", "custom": "自定义"}, "playModes": {"sequential": "顺序播放", "loop": "循环播放", "random": "随机播放"}, "radioLabels": {"off": "关闭", "on": "开启", "mixedTrigger": "混合触发"}, "inputLabels": {"key": "按键", "audioSourcePriority": "音源优先级", "triggerChannel": "触发通道", "mixingPriority": "混音优先级(数值越大，优先级越高)", "musicSignalFadeLevel": "音乐信号淡化级别(MIC信号存在时有效)", "phoneWhitelist": "电话白名单(如需添加多个号码,请用逗号分隔)", "followDeviceVolume": "跟随设备音量"}, "transferLabels": {"partitionList": "分区列表", "selectedPartitions": "已选分区", "enterPartitionKeyword": "请输入分区关键字"}, "switchLabels": {"enableDeviceSort": "开启设备排序"}, "buttonTitles": {"moveUpSelectedPartition": "上移所选分区", "moveDownSelectedPartition": "下移所选分区", "cancelSortChanges": "取消排序修改", "confirmSortChanges": "确认排序修改"}, "tableMessages": {"decoderPartitionNotExist": "解码分区设备不存在", "noCurrentTypeDeviceFound": "没有搜索到当前类型设备"}, "iconTitles": {"bluetoothPlay": "蓝牙播放", "voiceIntercom": "语音对讲", "deleteDevice": "删除设备"}, "successMessages": {"setParametersSuccess": "设置参数成功", "deleteRemoteControllerTaskSuccess": "删除远程遥控器任务成功", "addRemoteControllerTaskSuccess": "添加远程遥控器任务成功", "editRemoteControllerTaskSuccess": "编辑远程遥控器任务成功", "setRemoteControllerKeySuccess": "设置远程遥控器按键成功", "deviceAdvancedVolumeSetSuccess": "设备高级音量设置成功", "modifyDeviceNetworkModeSuccess": "修改设备网络模式成功", "setSipAccountInfoSuccess": "设置SIP账户信息成功", "setInformationPublishParametersSuccess": "设置信息发布参数成功", "setPartitionBluetoothParametersSuccess": "设置分区蓝牙参数成功", "modifyDeviceInfoSuccess": "修改设备信息成功", "upgradeFirmwareSuccess": "升级固件成功", "modifyDeviceIpSuccess": "修改设备IP成功", "uploadNewFirmwareSuccess": "上传新固件成功", "startUpgrade": "开始升级", "currentFirmwareIsLatest": "当前固件已是最新版本", "upgradeComplete": "升级完成", "upgradeCompleteStartingDevice": "升级完成，正在启动设备", "restartDeviceSuccess": "重启设备成功", "resetDeviceDataSuccess": "重置设备数据成功", "setSoundEffectSuccess": "设置音效成功", "monitorBindingSuccess": "监控绑定成功", "partitionDeviceCustomSortSuccess": "分区设备自定义排序成功，即将返回登录页面", "deleteDeviceSuccess": "删除设备成功"}, "errorMessages": {"upgradeFirmwareFailed": "升级固件失败", "uploadNewFirmwareFailed": "上传新固件失败", "upgradeFailed": "升级失败", "connectServerTimeout": "连接服务器超时", "currentDeviceNotSelected": "当前未选中设备，请重试", "cannotModifyOfflineDevice": "无法修改离线设备，请选择在线设备", "currentFirmwareServerExists": "当前固件服务器已存在，请重试"}, "headerLabels": {"serialNumber": "序号", "deviceName": "名称", "deviceType": "类型", "deviceIp": "设备IP", "mac": "MAC", "networkMode": "网络模式", "boundCamera": "绑定摄像头", "signalQuality": "信号质量", "iccid": "ICCID", "firmwareVersion": "固件版本", "runningStatus": "运行状态"}, "userLabels": {"andOthers": "等", "users": "个用户"}, "signalQuality": {"veryPoor": "极差", "poor": "较差", "average": "一般", "good": "较好", "excellent": "极好", "unknown": "未知"}, "songInfo": {"duration": "时长", "selectedSongs": "已选歌曲", "estimatedPlayDuration": "预估播放时长"}, "validation": {"invalidVolumeArrayLength": "音量设置数组长度无效，应为8段", "invalidVolumeRange": "第{segment}段（{noiseLevel}dB）的音量值无效，应在1-100之间", "volumeNotIncreasing": "第{currentSegment}段（{currentNoise}dB）的音量值{currentVolume}%不能低于第{previousSegment}段（{previousNoise}dB）的音量值{previousVolume}%"}, "statusLabels": {"idle": "空闲", "normal": "正常", "fault": "故障", "unknown": "未知"}}, "utils": {"playModes": {"single": "单曲播放", "singleLoop": "单曲循环", "sequential": "顺序播放", "loop": "循环播放", "random": "随机播放"}, "channelFormat": "（CH{channel}）", "channelsFormat": "（{channels}）", "andMoreSongs": " 等{count}首歌曲", "andMoreDevices": " 等{count}台", "andMoreZones": " 等{count}个分区", "andMoreGroups": " 等{count}个分组"}}