<template>
  <div>
    <!--通用successSnackBar-->
    <v-snackbar
      v-model="commonSuccessSnackBar"
      color="primary"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ successMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="primary" fab small class="ml-6" v-bind="attrs" @click="commonSuccessSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--通用errorSnackBar-->
    <v-snackbar
      v-model="commonErrorSnackBar"
      color="error"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="error" fab small class="ml-6" v-bind="attrs" @click="commonErrorSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <v-dialog
      v-model="notifyDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
          dark
        >
          <v-toolbar-title>{{ this.currentTab!==null ? this.currentTab.name : '' }}</v-toolbar-title>
        </v-toolbar>
        <v-card-text>
          <span style="font-weight: 500; font-size: 1.15em">{{ $t('maintenance.aboutToPerform') }}</span>
          <span class="font-weight-black text-decoration-underline ml-1" style="font-weight: 500; font-size: 1.15em">{{ currentOperation }}</span>
          <span style="font-weight: 500; font-size: 1.15em" class="ml-1">, {{ $t('maintenance.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-btn
            color="primary darken-1"
            text
            @click="notifyDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="commonHandle"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="uploadBackupDialog"
      max-width="500px"
      transition
      persistent
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('maintenance.uploadConfigFile') }}</span>
        </v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col cols="12">
                <v-file-input
                  v-model="uploadBackupFile"
                  :label="$t('maintenance.selectConfigFile')"
                  style="padding-top: 5px"
                  clearable
                  counter
                  show-size
                />
              </v-col>
              <v-col v-if="uploadBackupProcess !== 0" cols="12">
                <v-progress-circular
                  v-if="uploadBackupProcess !== 0"
                  class="ml-16"
                  :rotate="0"
                  :size="100"
                  :width="25"
                  :value="uploadBackupProcess"
                  color="primary"
                >
                  {{ uploadBackupProcess }}
                </v-progress-circular>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="uploadBackupDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            :loading="uploadBackupLoading"
            :disabled="uploadBackupLoading"
            @click="uploadBackup"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-container
      id="maintein"
      fluid
      tag="section"
    >
      <v-row dense>
        <!--选项卡切换-->
        <v-col cols="12">
          <v-tabs
            v-model="tab"
            icons-and-text
            style="background-color: transparent"
            grow
          >
            <v-tabs-slider />

            <v-tab
              v-for="i in tabList"
              :key="i.id"
              :href="`#tab-${i.id}`"
              class="text-lg-h4"
              @click="setCurrentTab(i)"
            >
              {{ i.name }}
              <v-icon size="30">{{ i.icon }}</v-icon>
            </v-tab>
          </v-tabs>
        </v-col>
      </v-row>
      <v-tabs-items v-model="tab">
        <!--数据重置总控制-->
        <v-tab-item :value="'tab-' + 1">
          <v-row dense>
            <v-col
              cols="12"
              sm="12"
            >
              <v-card :height="isWindowsServer ? '500px' : '540px'">
                <v-toolbar
                  color="primary"
                  dark
                >
                  <v-toolbar-title class="text-xl-h3">{{ $t('maintenance.dataReset') }}</v-toolbar-title>
                </v-toolbar>
                <v-card-text class="red--text text--darken-4 text-h3">
                  <span>{{ $t('maintenance.selectDataToReset') }}</span>
                  <v-checkbox
                    v-for="data in dataType"
                    :key="data.id"
                    v-model="cleanDataType"
                    :label="data.name"
                    :value="data.id"
                    class="ma-0 pt-3 input-class"
                    multiple
                  />
                </v-card-text>

                <v-btn
                  color="primary"
                  class="text-lg-h4 ml-5"
                  :loading="resetDataLoading"
                  :disabled="resetDataLoading"
                  @click="resetData"
                >
                  {{ $t('common.confirm') }}
                </v-btn>

              </v-card>

            </v-col>
            <v-col
              cols="6"
              sm="6"
            >
              <v-card height="250px">
                <v-toolbar
                  color="primary"
                  dark
                >
                  <v-toolbar-title class="text-xl-h3">{{ $t('maintenance.factoryReset') }}</v-toolbar-title>
                </v-toolbar>
                <v-card-text class="red--text text--darken-4 text-h3 ml-3">
                  {{ $t('maintenance.factoryResetWarning') }}
                </v-card-text>
                <v-card-text class="black--text text-h3 ml-3">
                  {{ $t('maintenance.factoryResetCaution') }}
                </v-card-text>
                <v-card-actions>
                  <v-btn
                    color="primary"
                    class="text-lg-h4 ml-5"
                    :loading="resetDefaultLoading"
                    :disabled="resetDefaultLoading"
                    @click="resetDefault"
                  >
                    {{ $t('common.confirm') }}
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-col>
            <v-col
              cols="6"
              sm="6"
            >
              <v-card height="250px">
                <v-toolbar
                  color="primary"
                  dark
                >
                  <v-toolbar-title class="text-xl-h3">{{ $t('maintenance.serverRestart') }}</v-toolbar-title>
                </v-toolbar>
                <v-card-text class="red--text text--darken-4 text-h3 ml-3">
                  {{ $t('maintenance.serverRestartWarning') }}
                </v-card-text>
                <v-card-text class="black--text text-h3 ml-3">
                  {{ $t('maintenance.serverRestartNotice') }}
                </v-card-text>
                <v-card-actions>
                  <v-btn
                    color="primary"
                    class="text-lg-h4 ml-5"
                    :loading="rebootLoading"
                    :disabled="rebootLoading"
                    @click="serverRestart"
                  >
                    {{ $t('common.confirm') }}
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-col>
          </v-row>
        </v-tab-item>
        <!--数据备份总控制-->
        <v-tab-item :value="'tab-' + 2">
          <v-container
            class="fill-height"
            fluid
          >
            <v-row
              align="center"
              justify="center"
            >
              <v-col
                cols="12"
                sm="8"
                md="5"
              >
                <v-card class="elevation-12">
                  <v-toolbar
                    color="primary"
                  >
                    <v-toolbar-title style="font-size: 2.0em;">{{ $t('maintenance.tabs.backupRestore') }}</v-toolbar-title>
                  </v-toolbar>
                  <v-card-text>
                    <v-container>
                      <v-row dense>
                        <v-col cols="12">
                          <span style="margin-right: 20px">{{ $t('maintenance.backup.currentConfigCount') }}:</span>
                          <span>{{ systemBackupNames.length + ' / 10' }}</span>
                          <v-btn
                            color="primary"
                            class="ml-8"
                            @click="backupServer"
                          >
                            {{ $t('maintenance.actions.backup') }}
                          </v-btn>
                          <v-btn
                            color="primary"
                            class="ml-4"
                            @click="preUploadBackup"
                          >
                            {{ $t('maintenance.actions.upload') }}
                          </v-btn>
                        </v-col>
                        <v-col cols="12">
                          <v-list
                            dense
                          >
                            <v-list-item-group v-if="systemBackupNames.length > 0" color="primary">
                              <v-list-item
                                v-for="(backupName, i) in systemBackupNames"
                                :key="i"
                                :input-value="selectedBackupName === backupName"
                                @click.native="backupChange(backupName)"
                              >
                                <v-list-item-content>
                                  <v-list-item-title style="font-size: 0.90em; padding-bottom: 3px;" v-text="backupName.substring(8)" />
                                </v-list-item-content>
                              </v-list-item>
                            </v-list-item-group>
                            <v-list-item-group v-else color="primary">
                              <v-list-item
                                v-for="(backupName, i) in emptyBackupArray"
                                :key="i"
                                disabled
                              >
                                <v-list-item-content>
                                  <v-list-item-title style="font-size: 0.90em" v-html="backupName" />
                                </v-list-item-content>
                              </v-list-item>
                            </v-list-item-group>
                          </v-list>
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-card-text>
                  <v-card-actions v-if="selectedBackupName != null" style="padding-top: 5px" class="justify-center align-center">
                    <a :href="backUpFileDownloadPath" download>
                      <v-btn
                        color="primary"
                      >
                        {{ $t('maintenance.actions.export') }}
                      </v-btn>
                    </a>
                    <v-btn
                      color="primary"
                      class="ml-4"
                      @click="preDeleteBackupFile"
                    >
                      {{ $t('common.delete') }}
                    </v-btn>
                    <v-btn
                      color="primary"
                      class="ml-4"
                      :loading="restoreSystemDataLoading"
                      :disabled="restoreSystemDataLoading"
                      @click="preRestoreBackupFile"
                    >
                      {{ $t('maintenance.actions.restore') }}
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-tab-item>

        <!--主备服务器控制-->
        <v-tab-item :value="'tab-' + 3">
          <v-container
            class="fill-height"
            fluid
          >
            <v-row
              align="center"
              justify="center"
            >
              <v-col
                cols="12"
                sm="8"
                md="4"
              >
               <v-card class="elevation-12">
                  <v-toolbar
                    color="primary"
                  >
                    <v-toolbar-title style="font-size: 2.0em;">{{ $t('maintenance.tabs.masterBackupServer') }}</v-toolbar-title>
                  </v-toolbar>
                <v-card-text>
                  <v-text-field
                    :label="$t('maintenance.serverType')"
                    :value="isBackupServer ? $t('maintenance.backupServer') : $t('maintenance.masterServer')"
                    disabled
                    class="pt-6"
                    hide-details
                  />
                  <v-text-field
                    :label="$t('maintenance.serverStatus')"
                    :value="getBackupSyncStatusCode(server_sync_status)"
                    disabled
                    class="pt-6"
                    hide-details
                  />
                  <v-radio-group
                    v-show="!isBackupServer"
                    v-model="mt_isEnableServerSync"
                    row
                  >
                    <span class="font-weight-bold mt-3" style="font-size: 20px;">{{ $t('maintenance.functionSwitch') }}</span>
                    <v-spacer />
                    <v-radio
                      :label="$t('maintenance.disableMasterBackup')"
                      color="primary"
                      :value="false"
                      class="pl-16 ml-16 mt-3"
                    />
                    <v-radio
                      :label="$t('maintenance.enableMasterBackup')"
                      color="primary"
                      :value="true"
                      class="pl-3 ml-3 mt-3"
                    />
                  </v-radio-group>
                  <div v-show="mt_isEnableServerSync">
                    <v-text-field
                      :label="isBackupServer ? $t('maintenance.masterServerIP') : $t('maintenance.backupServerIP')"
                      :disabled="isBackupServer"
                      v-model="mt_server_sync_dest_ip"
                      clearable
                    />
                  </div>
                </v-card-text>
                <v-card-actions style="padding-top: 0" v-show="!isBackupServer">
                    <v-spacer />
                    <v-btn
                      color="primary"
                      style="font-size: 18px"
                      text
                      @click="setBackupServer"
                    >
                      {{ $t('maintenance.setBackupServer') }}
                    </v-btn>
                </v-card-actions>
              </v-card>

              </v-col>
            </v-row>
          </v-container>
        </v-tab-item>

        <!--系统升级控制-->
        <v-tab-item :value="'tab-' + 4">
          <v-container
            class="fill-height"
            fluid
          >
            <v-row
              align="center"
              justify="center"
            >
              <v-col
                cols="12"
                sm="8"
                md="4"
              >
                <v-card class="elevation-12">
                  <v-toolbar
                    color="primary"
                  >
                    <v-toolbar-title style="font-size: 2.0em;">{{ $t('maintenance.tabs.systemUpgrade') }}</v-toolbar-title>
                  </v-toolbar>
                  <v-card-text>
                    <v-container>
                      <v-row dense>
                        <v-col cols="12">
                          <span style="margin-right: 20px">{{ $t('maintenance.currentServerVersion') }}:</span>
                          <span>{{ $store.state.version }}</span>
                        </v-col>
                        <v-col
                          cols="12"
                        >
                          <v-card-text class="red--text text--darken-4 text-h3">
                            {{ $t('maintenance.upgradeWarning') }}
                          </v-card-text>
                        </v-col>
                        <v-col cols="12">
                          <v-file-input
                            v-model="uploadFile"
                            :label="$t('maintenance.selectUpgradePackage')"
                            style="padding-top: 5px"
                            clearable
                            counter
                            show-size
                          />
                        </v-col>
                        <v-col cols="12">
                          <v-progress-circular
                            v-if="uploadServePackageProcess !== 0"
                            class="ml-16"
                            :rotate="0"
                            :size="100"
                            :width="25"
                            :value="uploadServePackageProcess"
                            color="primary"
                          >
                            {{ uploadServePackageProcess }}
                          </v-progress-circular>
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-card-text>
                  <v-card-actions style="padding-top: 0">
                    <v-spacer />
                    <v-btn
                      color="primary"
                      text
                      :loading="upgradeServeLoading"
                      :disabled="upgradeServeLoading"
                      @click="upgradeServe"
                    >
                      {{ $t('maintenance.serverUpgrade') }}
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-tab-item>
      </v-tabs-items>
    </v-container>
  </div>
</template>

<style scoped>
  /*修改文本框label的字体大小*/
  .v-radio >>> .v-label{
    font-size: 1.35em !important;
    font-weight: bold;
  }
</style>

<script>
  import { mapState, mapGetters } from 'vuex'

  //主备服务器状态定义 - 移除硬编码，改为动态获取

  const TabTypes = { // 选项卡类型
    DATA_RESET: 1,
    BACKUP_RESTORE: 2,
    MASTER_BACKUP_SERVER: 3,
    SYSTEM_UPGRADE: 4
  }

  export default {
    name: 'Maintenance',
    data: () => ({
      TabTypes, // Make TabTypes available to the template
      tab: null,
      currentTab: null,
      // 11/16 修改重置类型为多选
      cleanDataType: [],
      successMessages: '',
      commonSuccessSnackBar: false,
      errorMessages: '',
      commonErrorSnackBar: false,
      snackbarTimeout: 1500,
      resetDataLoading: false,
      rebootLoading: false,
      resetDefaultLoading: false,
      // 服务器升级相关
      uploadFile: null,
      upgradeServeLoading: false,
      currentOperation: null,
      notifyDialog: false,
      // 备份恢复相关
      selectedBackupName: null,
      uploadBackupFile: null,
      emptyBackupArray: [],
      uploadBackupDialog: false,
      uploadBackupLoading: false,
      restoreSystemDataLoading: false,
      //主备服务器
      mt_isEnableServerSync: null,
      mt_server_sync_dest_ip: null
    }),
    computed: {
      ...mapState(['resetDataResult', 'rebootResult', 'resetDefaultResult', 'uploadServePackageProcess',
                   'uploadServePackageResult', 'upgradeServeResult', 'errorId', 'errorWsMessage', 'systemBackupNames',
                    'uploadBackupProcess', 'uploadBackupResult', 'backupServerResult', 'removeBackupFileResult',
                    'restoreServerDataResult', 'commandName', 'isCloudServer',
                    'isEnableServerSync', 'isBackupServer', 'server_sync_dest_ip', 'server_sync_status', 'setBackupServerResult']),
      ...mapGetters(['isWindowsServer']),
      uploadFileName: function () {
        return this.uploadFile === null ? '' : this.uploadFile.name
      },
      backUpFileDownloadPath () {
        if (this.selectedBackupName === null) {
          return null
        }
        return this.$cgiHost + this.selectedBackupName
      },
      selectedBackupFileName () {
        if (this.selectedBackupName == null) {
          return ''
        }
        return this.selectedBackupName.substring(8)
      },
      dataType() {
        const dataType = [
          {id: 1, name: this.$t('maintenance.dataTypes.groupFiles')},
          {id: 2, name: this.$t('maintenance.dataTypes.playlistFiles')},
          {id: 4, name: this.$t('maintenance.dataTypes.timerFiles')},
          {id: 8, name: this.$t('maintenance.dataTypes.zoneFiles')},
          {id: 1024, name: this.$t('maintenance.dataTypes.pagerFiles')},
          {id: 16, name: this.$t('maintenance.dataTypes.audioCollectionFiles')},
          {id: 32, name: this.$t('maintenance.dataTypes.fireCollectionFiles')}
        ]
        if (!this.isWindowsServer && !this.isCloudServer) {
          dataType.push({id: 64, name: this.$t('maintenance.dataTypes.monitorDevices')})
        }
        dataType.push({id: 128, name: this.$t('maintenance.dataTypes.upgradeFirmware')})
        dataType.push({id: 256, name: this.$t('maintenance.dataTypes.powerSequencerFiles')})
        dataType.push({id: 512, name: this.$t('maintenance.dataTypes.logFiles')})
        return dataType
      },
      // 选项卡
      tabList() {
        const tabList = [{ id: TabTypes.DATA_RESET, name: this.$t('maintenance.tabs.dataReset'), icon: 'mdi-restore' }, { id: TabTypes.BACKUP_RESTORE, name: this.$t('maintenance.tabs.backupRestore'), icon: 'mdi-backup-restore' }]
        if (!this.isWindowsServer) {
          if (this.isEnableServerSync!==null) {
            tabList.push({ id: TabTypes.MASTER_BACKUP_SERVER, name: this.$t('maintenance.tabs.masterBackupServer'), icon: 'mdi-laptop' })
          }
          tabList.push({ id: TabTypes.SYSTEM_UPGRADE, name: this.$t('maintenance.tabs.systemUpgrade'), icon: 'mdi-package-up' })
        }
        return tabList
      }
    },
    watch: {
      // 监听语言变化
      '$i18n.locale'() {
        this.initializeTranslations()
      },
      // 统一错误处理
      errorId: function () {
        if (this.$route.fullPath !== '/dashboard/maintenance') {
          return;
        }
        if (this.errorId !== null) {
          this.errorMessages = this.$store.state.errorWsMessage
          this.commonErrorSnackBar = true
          // 关闭相关loading
          switch (this.commandName) {
            case 'restore_server_data': this.restoreSystemDataLoading = false; break;
            case 'reset_server_data': this.resetDataLoading = false; break;
            case 'reboot_server': this.rebootLoading = false; break;
            case 'factory_reset': this.resetDefaultLoading = false; break;
            case 'upgrade_server':
              this.upgradeServeLoading = false;
              this.$store.commit('updateUploadServePackageResult', null)
              this.$store.commit('updateUploadServePackageProgress', 0)
              break;
            default: break;
          }
        }
      },
      resetDataResult: function () {
        this.resetDataLoading = false
        if (this.$store.state.resetDataResult === 0) {
          this.successMessages = this.$t('maintenance.messages.resetDataSuccess')
          this.commonSuccessSnackBar = true
          this.$store.commit('updateResetDataResult', null)
        }
      },
      rebootResult: function () {
        this.rebootLoading = false
        if (this.$store.state.rebootResult === 0) {
          // this.successMessages = '服务器重启成功, 即将回退到登录页面'
          // this.commonSuccessSnackBar = true
          // this.$store.commit('updateRebootBySelf', true)
          this.$store.commit('updateRebootResult', null)
        }
      },
      resetDefaultResult: function () {
        this.resetDefaultLoading = false
        if (this.$store.state.resetDefaultResult === 0) {
          this.successMessages = this.$t('maintenance.messages.factoryResetSuccess')
          this.commonSuccessSnackBar = true
          this.editDeviceIpDialog = false
          this.$store.commit('updateLogoutBySelf', true)
          // 2021.01.10 成功后跳转到登录界面，延时1000ms
          setTimeout(() => {
            this.$router.push('/')
          }, 1000)
          this.$store.commit('updateResetDefaultResult', null)
        }
      },
      // 上传完成时（100），发送请求升级服务器 todo 修改为上传进度100或者在ws中写
      uploadServePackageResult: function () {
        if (this.$store.state.uploadServePackageResult === null) {
          return
        }
        if (this.$store.state.uploadServePackageResult) {
          this.successMessages = this.$t('maintenance.messages.uploadUpgradePackageSuccess')
          this.commonSuccessSnackBar = true
          this.$ws.upgradeServer(this.uploadFileName)
        } else {
          this.errorMessages = this.$t('maintenance.messages.uploadUpgradePackageFailed')
          this.commonErrorSnackBar = true
          this.upgradeServeLoading = false
        }
        this.$store.commit('updateUploadServePackageResult', null)
      },
      // 服务器升级结果
      upgradeServeResult: function () {
        this.upgradeServeLoading = false
        if (this.$store.state.upgradeServeResult === 0) {
          // this.successMessages = '服务器升级成功, 即将回退到登录页面'
          // this.commonSuccessSnackBar = true
          this.$store.commit('updateUpgradeServeResult', null)
        }
      },
      // 系统备份结果处理
      backupServerResult: function () {
        if (this.backupServerResult === 0) {
          this.successMessages = this.$t('maintenance.messages.backupSuccess')
          this.commonSuccessSnackBar = true
          this.$store.commit('updateBackupServerResult', null)
        }
      },
      // 移除配置结果
      removeBackupFileResult () {
        if (this.removeBackupFileResult === 0) {
          this.successMessages = this.$t('maintenance.messages.removeBackupFileSuccess')
          this.commonSuccessSnackBar = true
          // 20210709 删除备份文件成功后，取消其他文件的选中状态
          this.selectedBackupName = null
          this.$store.commit('updateRemoveBackupFileResult', null)
        }
      },
      // 上传配置文件结果
      uploadBackupResult: function () {
        this.uploadBackupLoading = false
        if (this.uploadBackupResult === null) {
          return
        }
        if (this.uploadBackupResult) {
          this.successMessages = this.$t('maintenance.messages.uploadConfigFileSuccess')
          this.commonSuccessSnackBar = true
          // 发送服务器上传数据通知后台
          this.$ws.uploadServerDataFromLocal(this.selectedBackupFileName)
          // 关闭上传对话框
          setTimeout(() => {
            this.uploadBackupDialog = false
          }, 300)
        } else {
          this.errorMessages = this.$t('maintenance.messages.uploadConfigFileFailed')
          this.commonErrorSnackBar = true
          this.$store.commit('updateUploadBackupProcess', 0)
        }
        this.$store.commit('updateUploadBackupResult', null)
      },
      // 服务器还原数据结果
      restoreServerDataResult: function () {
        this.restoreSystemDataLoading = false
        if (this.restoreServerDataResult === 0) {
          this.successMessages = this.$t('maintenance.messages.restoreDataSuccess')
          this.commonSuccessSnackBar = true
          this.$store.commit('updateRestoreServerDataResult', null)
        }
      },
      //设置主备服务器结果
      setBackupServerResult: function () {
        if (this.setBackupServerResult === 0) {
          this.successMessages = this.$t('maintenance.messages.setBackupServerSuccess')
          this.commonSuccessSnackBar = true
          this.$store.commit('updateSetBackupSyncResult', null)
        }
      },
    },
    mounted () {
      this.initializeTranslations()
      this.cleanDataType = []
      this.uploadFile = null
    },
    methods: {
      initializeTranslations() {
        // 初始化消息
        this.successMessages = this.$t('common.operationSuccess')
        this.errorMessages = this.$t('common.operationFailed')
        this.currentTab = this.tabList.find(tab => tab.id === TabTypes.DATA_RESET)
        this.emptyBackupArray = [this.$t('maintenance.noConfigFilesInServer')]
      },
      // 服务器备份
      backupServer () {
        this.$ws.backupServerData(1)
      },
      // 上传配置文件准备
      preUploadBackup () {
        this.uploadBackupFile = null
        this.uploadBackupLoading = false
        this.$store.commit('updateUploadBackupProcess', 0)
        this.uploadBackupDialog = true
      },
      uploadBackup () {
        if (this.uploadBackupFile == null) {
          this.errorMessages = this.$t('maintenance.messages.pleaseSelectConfigFile')
          this.commonErrorSnackBar = true
          return
        }
        // 固件上传：限定"*.tar.gz", 且名称包含BackupConfig的配置文件
        const file = this.uploadBackupFile
        const backupIdentifier = 'BackupConfig'
        if (file.name == null || !file.name.includes(backupIdentifier) || !file.name.endsWith('.tar.gz')) {
          // this.errorMessages = '文件名格式不正确，当前设备只支持文件名以' + prefix + '开头的tar.gz格式固件'
          this.errorMessages = this.$t('maintenance.messages.invalidBackupFileFormat', { identifier: backupIdentifier })
          this.commonErrorSnackBar = true
          return
        }
        this.uploadBackupLoading = true
        this.$ws.uploadServerData(file)
      },
      // 升级服务器过程
      upgradeServe: function () {
        if (this.uploadFile == null) {
          this.errorMessages = this.$t('maintenance.messages.pleaseSelectUpgradePackage')
          this.commonErrorSnackBar = true
          return
        }
        // 固件上传：限定"*.tar.gz"
        const file = this.uploadFile
        const prefix = 'S'
        if (!file.name.startsWith(prefix) || !file.name.endsWith('.tar.gz')) {
          // this.errorMessages = '文件名格式不正确，当前设备只支持文件名以' + prefix + '开头的tar.gz格式固件'
          this.errorMessages = this.$t('maintenance.messages.invalidUpgradeFileFormat', { prefix: prefix })
          this.commonErrorSnackBar = true
          return
        }
        this.currentOperation = this.$t('maintenance.operations.serverUpgrade')
        this.notifyDialog = true
      },
      // 更改当前选项卡
      setCurrentTab: function (tab) {
        // 当前选项卡中点击该选项卡，不触发重新查询
        if (tab.id === this.currentTab.id) {
          return
        }
        if (tab.id === TabTypes.SYSTEM_UPGRADE) {
          this.uploadFile = null
          this.upgradeServeLoading = false
          this.$store.commit('updateUploadServePackageProgress', 0)
        }
        if (tab.id === TabTypes.BACKUP_RESTORE) {
          this.restoreSystemDataLoading = false
          this.$ws.getBackupFileNames()
        }
        if (tab.id === TabTypes.MASTER_BACKUP_SERVER) {
          this.mt_isEnableServerSync = this.isEnableServerSync
          this.mt_server_sync_dest_ip = this.server_sync_dest_ip
        }
        this.currentTab = tab
      },
      // 重置数据
      resetData: function (name) {
        this.currentOperation = this.$t('maintenance.operations.resetSpecificData')
        this.notifyDialog = true
      },
      // 恢复出厂
      resetDefault: function () {
        // 创造对话框
        this.currentOperation = this.$t('maintenance.operations.factoryReset')
        this.notifyDialog = true
      },
      // 服务器重启
      serverRestart: function () {
        this.currentOperation = this.$t('maintenance.operations.serverRestart')
        this.notifyDialog = true
      },
      // 通用操作函数
      commonHandle: function () {
        this.notifyDialog = false
        switch (this.currentOperation) {
          case this.$t('maintenance.operations.serverUpgrade'):
            this.$ws.uploadServePackage(this.uploadFile)
            break
          case this.$t('maintenance.operations.resetSpecificData'):
            this.resetDataLoading = true
            this.$ws.resetData(this.cleanDataType)
            break
          case this.$t('maintenance.operations.factoryReset'):
            this.resetDefaultLoading = true
            this.$ws.resetDefault()
            break
          case this.$t('maintenance.operations.serverRestart'):
            this.rebootLoading = true
            this.$ws.restart()
            break
          case this.$t('maintenance.operations.deleteConfigFile'):
            this.$ws.removeBackupFile(this.selectedBackupFileName)
            break
          case this.$t('maintenance.operations.restoreConfigFile'):
            this.restoreSystemDataLoading = true
            this.$ws.restoreServerData(this.selectedBackupFileName)
            break
          case this.$t('maintenance.operations.backupServerSettings'):
            this.$ws.setBackupServerInfo(true, this.mt_isEnableServerSync, this.mt_server_sync_dest_ip)
            break
          default:
            break
        }
        setTimeout(() => {
          this.resetDataLoading = false
          this.resetDefaultLoading = false
        }, 500)
      },
      // 监听已选择的配置文件
      backupChange: function (backupName) {
        if (this.selectedBackupName == null || this.selectedBackupName !== backupName) {
          this.selectedBackupName = backupName
        } else {
          this.selectedBackupName = null
        }
      },
      preDeleteBackupFile () {
        this.currentOperation = this.$t('maintenance.operations.deleteConfigFile')
        this.notifyDialog = true
      },
      preRestoreBackupFile () {
        this.currentOperation = this.$t('maintenance.operations.restoreConfigFile')
        this.notifyDialog = true
      },
      setBackupServer () {
        this.currentOperation = this.$t('maintenance.operations.backupServerSettings')
        this.notifyDialog = true
      },
      getBackupSyncStatusCode (status_code) {
        // 动态获取主备服务器状态文本
        const statusMap = new Map([
          [0, this.$t('maintenance.serverSyncStatus.masterConnectedToBackup')],
          [1, this.$t('maintenance.serverSyncStatus.masterConnectingToBackup')],
          [2, this.$t('maintenance.serverSyncStatus.masterSyncDisabled')],
          [3, this.$t('maintenance.serverSyncStatus.masterNoBackupSpecified')],
          [4, this.$t('maintenance.serverSyncStatus.masterBackupOffline')],
          [5, this.$t('maintenance.serverSyncStatus.masterConnectFailedWaitAuth')],
          [6, this.$t('maintenance.serverSyncStatus.masterConnectFailedAuth')],
          [7, this.$t('maintenance.serverSyncStatus.masterConnectFailedVersion')],
          [8, this.$t('maintenance.serverSyncStatus.masterConnectFailedNetwork')],
          [20, this.$t('maintenance.serverSyncStatus.backupConnectedToMaster')],
          [21, this.$t('maintenance.serverSyncStatus.backupNotConnectedToMaster')],
          [22, this.$t('maintenance.serverSyncStatus.backupRefusedConnection')],
          [23, this.$t('maintenance.serverSyncStatus.backupChangedToMaster')]
        ])
        return statusMap.get(status_code) || this.$t('common.unknown')
      }
    },
  }
</script>
