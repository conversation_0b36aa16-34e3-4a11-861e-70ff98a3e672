@echo off
cd ..
set current_path="%cd%"

set LOGO_TEMP_NAME="lg_temp"
set LOGO_C0A0="logo_null"
set LOGO_C0A1="logo_null"
set LOGO_C1A0="logo_aisp"
set LOGO_C1A1="logo_cyzg"
set LOGO_C1A2="logo_hiptre"
set LOGO_C1A3="logo_ytzx"
set LOGO_C1A4="logo_svs"
set LOGO_C1A6="logo_seingrdiu"
set LOGO_C2A0="logo_lax"
set LOGO_C2A1="logo_gldt"
set LOGO_C2A2="logo_centre"
set LOGO_C2A3="logo_sywy"
set LOGO_C2A4="logo_lotfun"
set LOGO_C2A5="logo_thzn"
set LOGO_C2A6="logo_junnan"
set LOGO_C2A7="logo_scgj"
set LOGO_C2A8="logo_fensav"
set LOGO_C2A9="logo_huiming"
set LOGO_C2B0="logo_dahua"
set LOGO_C2B1="logo_dahua"
set LOGO_C2B2="logo_jiuwu"
set LOGO_C2B3="logo_pulai"
set LOGO_C2B4="logo_funinc"
set LOGO_C2B5="logo_meahanrt"
set LOGO_C3A0="logo_jusbe"
set LOGO_C4A0="logo_null"
set LOGO_C4A2="logo_null"
set LOGO_C4B0="logo_null"
set LOGO_C5A0="logo_null"

set BG_TEMP_NAME="bg_temp"
set BG_C0A0="background_null"
set BG_C0A1="background_mhi"
set BG_C1A0="background_aisp"
set BG_C1A1="background_null"
set BG_C1A2="background_null"
set BG_C1A3="background_null"
set BG_C1A4="background_null"
set BG_C1A6="background_null"
set BG_C2A0="background_null"
set BG_C2A1="background_null"
set BG_C2A2="background_null"
set BG_C2A3="background_null"
set BG_C2A4="background_null"
set BG_C2A5="background_null"
set BG_C2A6="background_null"
set BG_C2A7="background_scgj"
set BG_C2A8="background_null"
set BG_C2A9="background_null"
set BG_C2B0="background_null"
set BG_C2B2="background_null"
set BG_C2B3="background_null"
set BG_C2B4="background_null"
set BG_C2B5="background_null"
set BG_C3A0="background_jusbe"
set BG_C4A0="background_null"
set BG_C4A2="background_null"
set BG_C4B0="background_null"
set BG_C5A0="background_null"

set CUSTOMER="C1A0"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C1A0% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C1A0% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C1A1"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C1A1% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C1A1% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C1A2"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C1A2% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C1A2% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C1A3"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C1A3% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C1A3% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C1A4"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C1A4% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C1A4% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%


set CUSTOMER="C1A6"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C1A6% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C1A6% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%


set CUSTOMER="C2A0"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2A0% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2A0% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C2A1"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2A1% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2A1% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C2A2"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2A2% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2A2% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C2A3"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2A3% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2A3% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C2A4"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2A4% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2A4% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C2A5"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2A5% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2A5% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C2A6"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2A6% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2A6% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C2A7"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2A7% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2A7% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C2A8"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2A8% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2A8% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C2A9"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2A9% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2A9% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C2B0"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2B0% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2B0% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%


set CUSTOMER="C2B2"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2B2% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2B2% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%


set CUSTOMER="C2B3"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2B3% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2B3% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%


set CUSTOMER="C2B4"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2B4% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2B4% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%


set CUSTOMER="C2B5"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C2B5% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C2B5% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%


set CUSTOMER="C3A0"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C3A0% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C3A0% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%


set CUSTOMER="C4A0"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C4A0% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C4A0% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C4A2"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C4A2% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C4A2% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C4B0"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C4B0% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C4B0% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C5A0"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C5A0% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C5A0% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

set CUSTOMER="C8A0"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C8A0% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C8A0% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%


set CUSTOMER="C0A1"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C0A1% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C0A1% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%


set CUSTOMER="C0A0"
echo "%CUSTOMER%"
copy multiBuild\.env_%CUSTOMER% .env
cmd /c "npm run build"
del dist\config.json
dir /b dist\img\* | findstr %LOGO_C0A0% > temp.txt
set LOGO_TARGET=""
set /p LOGO_TARGET=<temp.txt
move dist\img\%LOGO_TARGET% dist\img\%LOGO_TEMP_NAME%
del dist\img\logo*
move dist\img\%LOGO_TEMP_NAME% dist\img\%LOGO_TARGET%
dir /b dist\img\* | findstr %BG_C0A0% > temp.txt
set BG_TARGET=""
set /p BG_TARGET=<temp.txt
move dist\img\%BG_TARGET% dist\img\%BG_TEMP_NAME%
del dist\img\background*
move dist\img\%BG_TEMP_NAME% dist\img\%BG_TARGET%
rd /s /q dist_%CUSTOMER%
move dist dist_%CUSTOMER%

echo "finish all build..."
pause