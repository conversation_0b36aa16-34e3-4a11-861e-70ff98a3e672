<template>
  <div>
    <!--对话框相关-->

    <!-- 声卡采集对话框   -->
    <v-dialog
      v-model="soundCardRecord.dialog"
      max-width="600px"
      transition
      @update:model-value="onSoundCardRecordDialogUpdate"
      >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('dashboard.soundCardRecord') }}</span>
        </v-card-title>
        <v-card-text>
          <!-- 使用 v-radio-group -->
          <v-radio-group v-model="soundCardRecord.selectedDevice" column>
            <div style="margin-bottom: 10px;">
              <!-- 声卡输出设备 -->
              <v-radio
                style="margin-bottom: 24px;"
                :label="$t('dashboard.soundCardOutputDevice')"
                value="output"
              ></v-radio>
              <v-slider
                :disabled="soundCardRecord.selectedDevice === 'input' || !soundCardRecord.soundCardInfo.output.isValid"
                v-model="soundCardRecord.soundCardInfo.output.volume"
                min="0"
                max="100"
                append-icon="mdi-volume-plus"
                prepend-icon="mdi-volume-minus"
                :thumb-size="24"
                thumb-label="always"
                @change.passive="updateSoundCardOutputVolume($event)"
              ></v-slider>
            </div>

            <div style="margin-bottom: 10px;">
              <!-- 声卡输入设备 -->
              <v-radio
                style="margin-bottom: 24px;"
                :label="$t('dashboard.soundCardInputDevice')"
                value="input"
              ></v-radio>
              <v-slider
                :disabled="soundCardRecord.selectedDevice === 'output' || !soundCardRecord.soundCardInfo.input.isValid"
                v-model="soundCardRecord.soundCardInfo.input.volume"
                min="0"
                max="100"
                append-icon="mdi-volume-plus"
                prepend-icon="mdi-volume-minus"
                :thumb-size="24"
                thumb-label="always"
                @change.passive="updateSoundCardInputVolume($event)"
              ></v-slider>
            </div>
          </v-radio-group>
        </v-card-text>

        <v-card-text>
          <span class="font-weight-bold" style="font-size: 20px; margin-left: 4px;">{{ $t('dashboard.deviceVolume') }}</span>
        </v-card-text>
        <v-card-text>
          <v-slider
                v-model="soundCardRecord.deviceVolume"
                min="0"
                max="100"
                append-icon="mdi-volume-plus"
                prepend-icon="mdi-volume-minus"
                :thumb-size="24"
                thumb-label="always"
                @change.passive="updateSoundCardDeviceVolume($event)"
          ></v-slider>
        </v-card-text>

        <!-- 传输模式选择 -->
        <v-card-text>
          <span class="font-weight-bold" style="font-size: 20px; margin-left: 4px;">{{ $t('dashboard.transmissionMode') }}</span>
        </v-card-text>
        <v-card-text>
          <v-radio-group v-model="soundCardRecord.transmissionMode" row style="margin-top: -20px;" @change="onSoundCardRecordTransmissionModeChange">
            <v-radio
              :label="$t('dashboard.lowBandwidth')"
              :value="1"
              color="primary"
            ></v-radio>
            <v-radio
              :label="$t('dashboard.highQuality')"
              :value="2"
              color="primary"
            ></v-radio>
          </v-radio-group>
        </v-card-text>

        <!-- 控制按钮 -->
        <v-card-actions class="justify-center" style="padding-bottom: 15px;">
          <v-btn
            color="primary"
            @click="startSoundCardRecord"
          >
            {{ $t('dashboard.startRecord') }}
          </v-btn>
          <v-btn
            color="primary"
            @click="stopSoundCardRecord"
          >
            {{ $t('dashboard.stopRecord') }}
          </v-btn>
        </v-card-actions>
      </v-card>

    </v-dialog>

    <!-- TTS对话框   -->
    <v-dialog
      v-model="ttsDialog"
      max-width="800px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('tts.title') }}</span>
        </v-card-title>
        <v-form
          ref="ttsForm"
          v-model="ttsForm"
        >
          <v-card-text>
            <v-textarea
              v-model="ttsContent"
              filled
              :counter="ttsTextUTF8ByteLength(ttsContent)"
              clearable
              :rules="ttsTextLengthRules()"
              height="250"
              :label="$t('tts.inputText')"
            />
            <v-row dense>
              <v-col cols="2" class="mt-2">
                <span class="font-weight-bold">{{ $t('tts.fileName') }}</span>
              </v-col>
              <v-col cols="10">
                <v-text-field
                  v-model="ttsFileName"
                  :label="$t('tts.audioFileName')"
                  :placeholder="$t('tts.audioFileNamePlaceholder')"
                  class="mt-n3 ml-2"
                  :rules="[rules.required, rules.ttsFileName]"
                  counter="20"
                  clearable
                />
              </v-col>
              <v-col cols="12">
                <v-radio-group
                  v-model="ttsSound"
                  row
                  class="mt-n1"
                >
                  <span class="font-weight-bold">{{ $t('dashboard.speaker') }}</span>
                  <v-radio
                    :label="$t('dashboard.maleVoice')"
                    color="primary"
                    :value="Number(0)"
                    class="pl-16 ml-5"
                  />
                  <v-radio
                    :label="$t('dashboard.femaleVoice')"
                    color="primary"
                    :value="Number(1)"
                  />
                </v-radio-group>
              </v-col>
              <v-col cols="2" class="mt-1">
                <!--<span class="font-weight-bold" v-html="getTtsWordClarification('音量', '大')"></span>-->
                <span class="font-weight-bold">{{ $t('dashboard.volume') }}</span>
              </v-col>
              <v-col cols="10">
                <v-slider
                  :value="ttsVolume"
                  @change.passive="v => ttsVolume = v"
                  append-icon="mdi-volume-plus"
                  prepend-icon="mdi-volume-minus"
                  @click:append="plusTtsVolume"
                  @click:prepend="minusTtsVolume"
                  :thumb-size="24"
                  thumb-label="always"
                ></v-slider>
              </v-col>
              <v-col cols="2">
                <!--<span class="font-weight-bold" v-html="getTtsWordClarification('音调', '高')"></span>-->
                <span class="font-weight-bold">{{ $t('dashboard.pitch') }}</span>
              </v-col>
              <v-col cols="10" class="mt-n1">
                <v-slider
                  :value="ttsPitch"
                  @change="v => ttsPitch = v"
                  append-icon="mdi-plus-circle-outline"
                  prepend-icon="mdi-minus-circle-outline"
                  @click:append="plusTtsPitch"
                  @click:prepend="minusTtsPitch"
                  :thumb-size="24"
                  thumb-label="always"
                ></v-slider>
              </v-col>
              <v-col cols="2">
                <!--<span class="font-weight-bold" v-html="getTtsWordClarification('语速', '快')"></span>-->
                <span class="font-weight-bold">{{ $t('dashboard.speed') }}</span>
              </v-col>
              <v-col cols="10" class="mt-n1">
                <v-slider
                  :value="ttsSpeed"
                  @change="v => ttsSpeed = v"
                  append-icon="mdi-plus-circle-outline"
                  prepend-icon="mdi-minus-circle-outline"
                  @click:append="plusTtsSpeed"
                  @click:prepend="minusTtsSpeed"
                  :thumb-size="24"
                  thumb-label="always"
                ></v-slider>
              </v-col>
              <v-col cols="2">
                <span class="font-weight-bold">{{ $t('dashboard.importTxtFile') }}</span>
              </v-col>
              <v-col cols="4" class="mt-n5">
                <v-file-input
                  v-model="uploadTtsFile"
                  :label="$t('dashboard.browseTxtFile')"
                  clearable
                  accept=".txt"
                />
              </v-col>
              <v-col cols="6">
                <v-btn
                  color="primary"
                  width="20"
                  height="20"
                  @click="readTtsFile()"
                >
                  {{ $t('dashboard.load') }}
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer />

            <v-btn
              color="blue darken-1"
              text
              @click="ttsDialog = false"
            >
              {{ $t('common.cancel') }}
            </v-btn>
            <v-btn
              color="blue darken-1"
              text
              @click="resetTtsOption"
              :title="$t('dashboard.defaultParams')"
            >
              {{ $t('dashboard.defaultParams') }}
            </v-btn>
            <v-btn
              :loading="startTTSLoading"
              color="blue darken-1"
              text
              @click="startTTS"
            >
              {{ $t('dashboard.startSynthesis') }}
            </v-btn>
          </v-card-actions>
        </v-form>
      </v-card>
    </v-dialog>
    <!--系统注册对话框-->
    <v-dialog v-model="registrationDialog" max-width="500px" transition persistent>
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('dashboard.systemRegister') }}</span>
        </v-card-title>
        <v-card-subtitle class="pt-5 text-lg-h4">
          <span style="color: red">{{ $t('dashboard.systemNotRegistered') }}</span>
        </v-card-subtitle>

        <v-card-text class="mt-n4">
          <v-text-field
            :value="machineCode"
            :label="$t('common.machineCode')"
            class="pt-6"
            hide-details
            required
            disabled
            style="font-weight: 600"
          />
          <v-text-field
            v-model="registrationCode"
            :append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
            :type="showPassword ? 'text' : 'password'"
            :placeholder="$t('dashboard.enterRegistrationCode')"
            @click:append="showPassword = !showPassword"
            :label="$t('dashboard.registrationCode')"
            class="pt-6"
            hide-details
            required
            style="font-weight: 500"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="registrationDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="systemRegister"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--分组对话框-->
    <v-dialog
      v-model="deleteGroupDialog"
      max-width="320"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('dashboard.deleteGroup') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('dashboard.aboutToDelete') }} </span>
          <span class="font-weight-black text-decoration-underline">{{ selectedGroups.length }}</span>
          <span> {{ $t('dashboard.groups') }}, {{ $t('dashboard.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteGroupDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            :loading="commonLoading"
            :disabled="commonLoading"
            @click="deleteSelectedGroups"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="createGroupDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('dashboard.createGroup') }}</span>
        </v-card-title>
        <v-card-text>
          <v-text-field
            :value="newGroupName"
            :label="$t('dashboard.enterGroupName')"
            required
            hide-details
            class="mt-n3"
            @change="v => newGroupName=v"
          />
        </v-card-text>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="transfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('dashboard.enterZoneKeyword')"
            :data="decodeZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('dashboard.zoneList'), $t('dashboard.selectedZones')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="createGroupDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            :loading="createGroupLoading"
            :disabled="createGroupLoading"
            @click="createNewGroup"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="editGroupDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('dashboard.editGroup') }}</span>
        </v-card-title>
        <v-card-text>
          <v-text-field
            v-if="singleSelectedGroup != null"
            :value="replacedGroupName"
            :label="$t('dashboard.groupName')"
            placeholder=""
            required
            hide-details
            class="mt-n3"
            @change="v => replacedGroupName = v"
          />
        </v-card-text>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="editTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('dashboard.enterZoneKeyword')"
            :data="decodeZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('dashboard.zoneList'), $t('dashboard.selectedZones')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="editGroupDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            :loading="editGroupLoading"
            :disabled="editGroupLoading"
            @click="editGroup"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--停止任务-->
    <v-dialog
      v-model="stopManualTaskDialog"
      max-width="320"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('dashboard.stopTask') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('dashboard.aboutToStop') }} </span>
          <span class="font-weight-black text-decoration-underline">{{ manualTaskSelect.length }}</span>
          <span> {{ $t('dashboard.tasks') }}, {{ $t('dashboard.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="stopManualTaskDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="setManualTaskPlayStatus(4)"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
     <!--启动/停止一键告警-->
    <v-dialog
      v-model="startManualAlarmDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline font-weight-black">
          {{ $t('dashboard.oneKeyAlarm') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span class="red--text text--darken-4 font-weight-black" style="font-size: 1.15em">
            {{ $t('dashboard.confirmStartStopAlarm') }}
          </span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="startManualAlarmDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="startManualAlarmEvent"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--应用播放模式到所有手动任务-->
    <v-dialog
      v-model="applyPlayModeForAllManualTaskDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('dashboard.setPlayModeSuccess') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('dashboard.applyToAllManualTasks') }}</span>
          <!--<p class="red&#45;&#45;text text&#45;&#45;darken-4">提示：否则将在新的手动任务中生效</p>-->
          <!--<span class="font-weight-black text-decoration-underline">{{ manualTaskSelect.length }}</span>-->
          <!--<span> 个任务, 请确认</span>-->
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="applyPlayModeForAllManualTaskDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="applyPlayModeForAllManualTask"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="restartSingleTimePointDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('dashboard.restoreTodayTimer') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('dashboard.aboutToRestore') }}</span>
          <span class="font-weight-black text-decoration-underline">
            {{ restartSingleTimePointInTable ? restartSingleTimePointInTable.name : '' }}
          </span>
          <span>,{{ $t('dashboard.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="restartSingleTimePointDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="restartSingleTimePoint"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="cancelSingleTimePointDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('dashboard.cancelTodayTimer') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('dashboard.aboutToCancel') }}</span>
          <span class="font-weight-black text-decoration-underline">
            {{ cancelSingleTimePointInTable ? cancelSingleTimePointInTable.name : '' }}
          </span>
          <span>,{{ $t('dashboard.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="cancelSingleTimePointDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="cancelSingleTimePoint"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="stopSingleTimePointDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('dashboard.stopTimerTask') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('dashboard.aboutToStopTimer') }}</span>
          <span class="font-weight-black text-decoration-underline">
            {{ cancelSingleTimePointInTable ? cancelSingleTimePointInTable.name : '' }}
          </span>
          <span>,{{ $t('dashboard.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="stopSingleTimePointDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="cancelSingleTimePoint"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="applySelectedZonesContainsAudioMixer"
      max-width="500"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('dashboard.playSource') }}
        </v-card-title>
        <v-card-text class="text-center">
          <span>{{ $t('dashboard.audioMixerConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="applySelectedZonesContainsAudioMixer = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="ConfirmPlaySource"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--############################-->
    <!--通用errorSnackBar-->
    <v-snackbar
      v-model="commonErrorSnackBar"
      color="error"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="error" fab small class="ml-6" v-bind="attrs" @click="commonErrorSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--通用successSnackBar-->
    <v-snackbar
      v-model="commonSuccessSnackBar"
      color="primary"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ successMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="primary" fab small class="ml-6" v-bind="attrs" @click="commonSuccessSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--############################-->
    <!--音源列表-->
    <v-navigation-drawer
      floating
      fixed
      height="99%"
      right
      absolute
      width="240"
      mobile-breakpoint="960"
      style="margin-top: -16px; background-color: transparent; padding-bottom: 140px"
      v-show="currentTab !== TabTypes.TIMER_TASKS && currentTab !== TabTypes.MANUAL_TASKS"
    >
      <!--todo 增加类似左导航的收起功能-->
      <v-card>
        <v-toolbar color="primary">

          <v-toolbar-title>{{ $t('dashboard.audioSourceList') }}</v-toolbar-title>

          <v-spacer />

        </v-toolbar>
        <!-- 音源列表内容 -->
        <v-list>
          <v-list-group
            v-for="item in playList"
            :key="item.list_name"
            v-model="item.active"
            no-action
            @click.native="listChanged(item.list_id)"
          >

            <template v-slot:activator>
              <v-tooltip
                top
                open-on-hover
                open-delay="1000"
                nudge-left="10"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-list-item-content v-bind="attrs" v-on="on">
                    <v-list-item-title
                      v-text="item.list_name"
                    />
                  </v-list-item-content>
                </template>
                <span v-html="getListNameAndLengthTooltip(item)"/>
              </v-tooltip>
            </template>
            <v-list-item
              v-for="song in item.songs"
              :key="song.song_name"
              :input-value="currentSong === song.song_name"
              class="pl-8"
              @click.stop="songChanged(song.song_name, song.song_path_name)"
            >
              <v-tooltip
                top
                open-on-hover
                open-delay="1000"
                nudge-left="10"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-list-item-content v-bind="attrs" v-on="on">
                    <v-list-item-title
                      :style="songStyleInPlaylist(song.alive)"
                      v-text="song.song_name"
                    />
                  </v-list-item-content>
                </template>
                <span v-html="getSongNameAndDurationForSongTooltip(song)" />
              </v-tooltip>
            </v-list-item>
          </v-list-group>
        </v-list>
        <v-divider v-show="onlineAudioCollectors.length !== 0" />
        <!-- 音频采集卡相关 -->
        <v-list v-show="onlineAudioCollectors.length !== 0">
          <v-list-group v-model="expandCollector" no-action @click.native="clickCollector">
            <template v-slot:activator>
              <v-list-item-content>
                <v-list-item-title v-text="audioCollectorName" />
              </v-list-item-content>
            </template>

            <v-list-item
              v-for="(channelName, index) in audioCollectorChannels"
              :key="index"
              :input-value="currentAudioCollectorChannelName === channelName"
              class="pl-8"
              @click.stop="collectorChanged(channelName, index)"
            >
              <v-list-item-content>
                <v-list-item-title
                  class="text--primary"
                  v-text="channelName"
                />
              </v-list-item-content>
            </v-list-item>
          </v-list-group>
        </v-list>
      </v-card>
    </v-navigation-drawer>
    <!--主分区控制-->
    <v-container
      id="dashboard"
      fluid
      tag="section"
      style="padding-right: 260px; padding-bottom: 140px"
    >
      <v-row
        dense
      >
        <!--选项卡切换-->
        <v-col cols="12">
          <v-tabs
            v-model="tab"
            icons-and-text
            style="background-color: transparent"
          >
            <v-tabs-slider />

            <v-tab
              v-for="i in tabList"
              :key="i.id"
              :href="`#tab-${i.id}`"
              @click="setCurrentTab(i)"
            >
              {{ i.name }}
              <v-icon>{{ i.icon }}</v-icon>
            </v-tab>
            <!--如果当前未选中，则鼠标移入提示“显示在线分区”，否则提示"显示所有分区"。-->
            <div v-if="currentTab === TabTypes.ZONES">
              <v-tooltip top open-on-hover open-delay="500">
                <template v-slot:activator="{ on }">
                  <v-switch
                    v-model="onlineOnlySwitch"
                    :label="$t('dashboard.onlineZones')"
                    class="pt-2 pl-4"
                    v-on="on"
                  />
                </template>
                <span v-show="!onlineOnlySwitch">{{ $t('dashboard.showOnlineZones') }}</span>
                <span v-show="onlineOnlySwitch">{{ $t('dashboard.showAllZones') }}</span>
              </v-tooltip>
            </div>
            <!--<v-switch
              v-model="onlineOnlySwitch"
              :label="$t('dashboard.onlineZones')"
              class="pt-2 pl-4"
            />-->
            <div style="padding-top: 15px; padding-left: 40px">
              <v-btn
                v-show="currentTab === TabTypes.ZONES && !checkIfFilteredZones()"
                color="primary"
                @click="selectAllCards"
              >
                <span v-show="onlineZones.length !== selectedPartition.length">{{ $t('dashboard.selectAll') }}</span>
                <span v-show="onlineZones.length === selectedPartition.length">{{ $t('dashboard.deselectAll') }}</span>
              </v-btn>
              <v-btn
                v-show="currentTab === TabTypes.ZONES && checkIfFilteredZones()"
                color="primary"
                @click="selectAllFilteredCards"
              >
                <span v-if="selectedNumbers === 0 || selectedFilterNumber !== myZones.filter(zone => zone.source !== -1).length">{{ $t('dashboard.selectAll') }}</span>
                <span v-else>{{ $t('dashboard.deselectAll') }}</span>
              </v-btn>
              <v-btn
                v-show="currentTab === TabTypes.GROUPS"
                color="primary"
                @click="selectAllGroups(groupList)"
              >
                <span v-show="groupList.length !== selectedGroups.length">{{ $t('dashboard.selectAll') }}</span>
                <span v-show="groupList.length === selectedGroups.length">{{ $t('dashboard.deselectAll') }}</span>
              </v-btn>
              <span
                v-show="(currentTab === TabTypes.ZONES && !this.checkIfFilteredZones()) || currentTab === TabTypes.GROUPS"
                class="text-lg-h4"
                style="padding-left: 10px"
              >{{ $t('dashboard.selected') }}：{{ selectedNumbers }}</span>
              <span
                v-show="(currentTab === TabTypes.ZONES && this.checkIfFilteredZones())"
                class="text-lg-h4"
                style="padding-left: 10px"
              >{{ $t('dashboard.currentConditionSelected') }}：{{ selectedFilterNumber }}, {{ $t('dashboard.totalSelected') }}：{{ selectedNumbers }}</span>
            </div>
            <v-spacer />
            <v-text-field
              v-show="currentTab === TabTypes.ZONES"
              v-model="zoneSearch"
              append-icon="mdi-magnify"
              :label="$t('dashboard.search')"
              hide-details
              clearable
              class="mt-2"
              style="max-width: 165px;margin-right: 20px;margin-left: 25px"
            />
            <v-spacer />
            <!--分页插件-->
            <v-pagination
              v-if="currentTab === TabTypes.ZONES"
              v-model="page"
              :length="filterPage"
              :total-visible="5"
              class="text-lg-center pa-2 mt-2"
            />
            <v-text-field
              v-if="currentTab === TabTypes.ZONES"
              :label="$t('dashboard.itemsPerPage')"
              :rules="PartitionShowRules"
              :value="rowsPerPageOptions"
              class="shrink pt-6 pr-2 centered-input"
              style="width: 90px;"
              color="primary"
              append-icon="mdi-book-open-page-variant"
              @change.native="rowsPerPageChange($event)"
            />
            <div
              v-if="currentTab === TabTypes.GROUPS"
              style="padding-top: 15px; padding-right: 10px"
            >
              <v-btn
                color="primary"
                @click="createGroupDialog = true"
              >
                {{ $t('dashboard.newGroup') }}
              </v-btn>
              <v-btn
                color="primary"
                @click="preEditGroup"
              >
                {{ $t('dashboard.editGroup') }}
              </v-btn>
              <v-btn
                color="primary"
                @click="preDeleteGroup"
              >
                {{ $t('dashboard.deleteGroup') }}
              </v-btn>
            </div>
          </v-tabs>
        </v-col>
      </v-row>
      <v-tabs-items v-model="tab">
        <!--分区总控制-->
        <v-tab-item :value="'tab-' + 1">
          <v-row dense class="mt-2">
            <v-progress-linear
              :active="myZones.length <= 0"
              :indeterminate="true"
              absolute
              top
              color="primary"
            />
            <v-col
              v-for="(event,index) in filterZones"
              :key="event.mac"
              cols="auto"
              style="padding-top: 1px; background-color: transparent"
            >
              <v-card
                height="140"
                max-height="140"
                max-width="165"
                class="mx-auto d-flex flex-column ma-0"
                :color="getZoneColor(event.source, event.mac)"
                :disabled="event.source === -1"
                @click="selectableCards(event.mac)"
                outlined
              >
                <v-list-item>
                  <v-list-item-avatar
                    style="padding-left: 1px"
                    size="24"
                    color="indigo"
                    class="ml-n2"
                  >
                    <span class="white--text">{{ (index + 1) + (rowsPerPageOptions * (page - 1)) }}</span>
                  </v-list-item-avatar>
                  <v-list-item-content>
                    <v-list-item-title>
                      <v-tooltip
                        top
                        open-on-hover
                        open-delay="1000"
                      >
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on">{{ event.name }}</span>
                        </template>
                        <span>{{ event.name }}</span>
                      </v-tooltip>
                    </v-list-item-title>
                    <v-list-item-subtitle>
                      <v-tooltip
                        bottom
                        open-on-hover
                        open-delay="1000"
                      >
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on">{{ event.ip }}</span>
                        </template>
                        <span>{{ event.ip }}</span>
                      </v-tooltip>
                    </v-list-item-subtitle>
                  </v-list-item-content>

                  <!-- 分区摄像头显示 -->
                  <v-list-item-icon
                    v-show="event.monitor_mac && event.monitor_mac !== ''"
                    class="mr-n4"
                    :color="checkIfZoneHasOnlineMonitor(event.monitor_mac) ? 'primary' : ''">
                    <v-icon>mdi-webcam</v-icon>
                  </v-list-item-icon>
                </v-list-item>
                <v-card-text class="mx-0 px-1 one-line text-center" style="padding-bottom: 20px">
                  <v-tooltip
                    bottom
                    open-on-hover
                    open-delay="1000"
                  >
                    <!-- 当文件名过长时才添加动画 -->
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        :class="[ { animate : getStrLenGbk(getZoneSourceName(event)) > 19 } ]"
                        v-bind="attrs"
                        v-on="on"
                      >{{ getZoneSourceName(event) }}</span>
                    </template>
                    <span>{{ event.source_name }}</span>
                  </v-tooltip>
                </v-card-text>
                <v-card-actions>
                  <v-icon class="pt-1">
                    {{ getIconForZoneStatus(event.source, event.play_status) }}
                  </v-icon>
<!--                  <v-icon class="pt-1" :color="checkIfZoneHasOnlineMonitor(event.monitor_mac) ? 'primary' : ''">
                    mdi-webcam
                  </v-icon>-->
                  <span style="display: inline-block; width: 210px; padding-top: 3px" class="pl-1">{{ getStatus(event.source) }}</span>
                  <v-icon v-show="event.volume !== 0" class="ml-3 mt-1">
                    mdi-volume-high
                  </v-icon>
                  <v-icon v-show="event.volume === 0" class="ml-3 mt-1">
                    mdi-volume-off
                  </v-icon>
                  <!-- 分区音量显示 -->
                  <v-text-field
                    v-model="event.volume"
                    style="margin-bottom: 3px; margin-left: 1px"
                    class="mr-n9 volume-class"
                    single-line
                    dense
                    hide-details
                    disabled
                  />
                </v-card-actions>
              </v-card>
            </v-col>
          </v-row>
        </v-tab-item>
        <!--分组总控制-->
        <v-tab-item :value="'tab-' + 2">
          <v-row>
            <!--12.10 隐藏加载进度条
            <v-progress-linear
              :active="groupList.length <= 0"
              :indeterminate="true"
              absolute
              top
              color="primary"
            />-->
            <v-col
              v-for="(group, index) in groupList"
              :key="index"
              cols="auto"
              style="padding-top: 1px"
            >
              <v-card
                elevation="10"
                outlined
                tile
                height="180"
                width="180"
                :color="selectedGroups.indexOf(group.group_id) > -1 ? '#90BEC8' : ''"
                @click="selectableGroups(group.group_id)"
              >
                <v-tooltip
                  bottom
                  open-on-hover
                  open-delay="1000"
                  transition
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-list-item v-bind="attrs" v-on="on">
                      <v-list-item-avatar
                        style="padding-left: 1px"
                        size="24"
                        color="indigo"
                      >
                        <span class="white--text">{{ index + 1 }}</span>
                      </v-list-item-avatar>
                      <v-list-item-content style="font-size: 1.20em; font-weight: bold">
                        <span>{{ group.group_name }}</span>
                        <!--                    <v-list-item-subtitle>{{ group.group_name }}</v-list-item-subtitle>-->
                      </v-list-item-content>
                    </v-list-item>

                    <v-card-text class="ma-0 pa-1 one-line text-center" v-bind="attrs" v-on="on">
                      <span>
                        {{ $t('dashboard.zoneCount') }}：{{ group.zones.length }}
                      </span>
                      <v-img
                        :src='require("@/assets/mountain.jpg")'
                        max-height="95"
                        class="mt-2"
                      />
                    </v-card-text>
                  </template>
                  <div>
                    <p>{{ group.group_name }}</p>
                    <p class="mt-n3">{{ $t('dashboard.user') + '：' + group.group_account }}</p>
                    <div class="mt-n3" v-show="group.zone_names.length > 0">
                      <span>{{ $t('dashboard.containsZones') }}：</span>
                      <span
                        v-for="(name,group_index) in group.zone_names"
                        :key="group_index"
                        class="mx-2"
                        style="display: inline-block"
                      >{{ name }}</span>
                    </div>
                    <div class="mt-n3" v-show="group.zone_names.length === 0">
                      <span>{{ $t('dashboard.groupHasNoZones') }}</span>
                    </div>
                  </div>
                </v-tooltip>
              </v-card>
            </v-col>
          </v-row>
        </v-tab-item>
        <!-- 列表总控制 -->
        <v-tab-item :value="'tab-' + 3" v-if="isShowTreeListView">
          <v-card>
            <v-toolbar
              color="primary"
              dark
              flat
              class="mt-n5"
              height="45px"
            >
              <v-icon class="ml-n3">mdi-file-tree</v-icon>
              <v-toolbar-title class="pl-2">{{ $t('dashboard.listView') }}</v-toolbar-title>
            </v-toolbar>
          <v-row>
            <v-col cols="2" class="treeViewStyle">
              <v-treeview
                v-model="selectedGroupInTree"
                item-key="group_id"
                item-text="group_name"
                item-children="children"
                :items="treeViewGroupData"
                selectable
                selected-color="primary"
                return-object
                open-all
                activatable
                :active.sync="activeTreeNodes"
              ></v-treeview>
            </v-col>
            <v-col
              cols="10"
              class="pa-0"
            >
              <!--表格视图-->
              <v-data-table
                v-model="selectedZonesInTree"
                :headers="zonesHeadersInTree"
                :items="groupZonesInTree"
                :search="zoneSearchInTree"
                sort-by.sync="['source']"
                item-key="mac"
                :item-class="itemRowBackground"
                multi-sort
                show-select
                class="elevation-1 px-2 ml-2 mr-3"
                style="margin-top: -10px"
                :items-per-page="15"
                selectable-key="isSelectable"
                @click:row="rowZoneTreeClick"
                :page.sync="pageInTree"
                fixed-header
                height="55vh"
                :footer-props="{
                  itemsPerPageOptions: [15,50,100],
                  showFirstLastPage: true,
                  showCurrentPage: true,
                  firstIcon: 'mdi-arrow-collapse-left',
                  lastIcon: 'mdi-arrow-collapse-right',
                  prevIcon: 'mdi-minus',
                  nextIcon: 'mdi-plus'
                }"
              >
                <!--id，名称，ip，分区状态，音量-->
                <template v-slot:item.id="{ item }">
                  <span>{{ groupZonesInTree.map(function(x) {return x.mac; }).indexOf(item.mac) + 1 }}</span>
                </template>
                <template v-slot:item.source="{ item }">
                    <v-icon v-if="item.source === 2 && item.play_status === 2">
                      {{getIconForZoneStatus(item.source, item.play_status)}}
                    </v-icon>
                    <span> {{ getMonitorStatus(item) }}</span>
                  </template>
                  <template v-slot:item.volume="{ item }">
                    <span v-html="getVolumeByPresent(item.volume)"></span>
                  </template>
                  <!--格式化绑定摄像头格式-->
                <template v-slot:item.monitor_mac="{ item }">
                  <span v-html="getFormattedMonitor(item.monitor_mac)"></span>
                </template>
                <!--格式化没有数据时候的提示-->
                <template v-slot:no-data>
                  <span>{{ selectedGroupInTree.length === 0 ? $t('dashboard.noGroupSelected') : $t('dashboard.selectedGroupHasNoZones') }}</span>
                </template>
                <!--增加搜索功能-->
                <template
                  v-slot:top
                >
                  <v-text-field
                    v-model="zoneSearchInTree"
                    prepend-icon="mdi-magnify"
                    :label="$t('dashboard.search')"
                    hide-details
                    full-width
                    style="padding-bottom: 5px;"
                    :disabled="groupZonesInTree.length === 0"
                  />
                </template>
              </v-data-table>
            </v-col>
          </v-row>
          </v-card>
        </v-tab-item>
        <!--定时任务控制-->
        <v-tab-item :value="'tab-' + 4">
          <v-card class="mb-2">
            <v-card-title style="margin-top: -20px">
              <v-radio-group
                v-model="timerStatus"
                row
                dense
              >
                <v-radio
                  :label="todayTimerRunningCount"
                  color="primary"
                  value="2"
                />
                <v-radio
                  :label="todayTimerFinishedCount"
                  color="primary"
                  value="1"
                />
                <v-radio
                  :label="todayTimerWaitingCount"
                  color="primary"
                  value="0"
                />
                <v-radio
                  :label="todayTimerCancelledCount"
                  color="primary"
                  value="3"
                />
              </v-radio-group>
              <v-spacer />
              <v-spacer></v-spacer>
              <v-text-field
                v-model="todayTimerSearch"
                append-icon="mdi-magnify"
                :label="$t('dashboard.search')"
                single-line
                hide-details
                full-width
                style="padding-bottom: 35px"
              />
            </v-card-title>
            <v-data-table
              :headers="todayTimerHeaders"
              :items="getTodayTimersByStatus"
              :search="todayTimerSearch"
              item-key="id"
              class="elevation-1"
              :loading="getTodayTimersByStatus.length === 0"
              :loading-text="getTodayTimerLoadingText"
              style="margin-top: -20px"
              :items-per-page="10"
              :footer-props="{
                itemsPerPageOptions: [10,25,50,100],
                showFirstLastPage: true,
                showCurrentPage: true,
                firstIcon: 'mdi-arrow-collapse-left',
                lastIcon: 'mdi-arrow-collapse-right',
                prevIcon: 'mdi-minus',
                nextIcon: 'mdi-plus'
              }"
            >
              <template v-slot:item.port="{ item }">
                <span>{{ getTodayTimersByStatus.map(function(x) {return x.id; }).indexOf(item.id) + 1 }}</span>
              </template>
              <template v-slot:item.play_mode="{ item }">
                <span>{{ $util.getPlayMode(item.play_mode) }}</span>
              </template>
              <template v-slot:item.songs="{ item }">
                <v-tooltip bottom open-on-hover open-delay="500" v-show="item.source_type == null || item.source_type === 0">
                  <template v-slot:activator="{ attr, on }">
                    <span v-bind="attr" v-on="on">{{ $util.getTimerSongs(item.songs) }}</span>
                  </template>
                  <span>{{ $util.getSongNames(item.songs) }}</span>
                </v-tooltip>
                <span v-show="item.source_type === 1">
                  {{ getAudioCollectorDescription($t('dashboard.tooltips.audioCollection'),
                      item.audio_collector == null ? null : item.audio_collector.device_mac,
                      item.audio_collector == null ? null : item.audio_collector.channel) }}
                </span>
                <v-tooltip bottom open-on-hover open-delay="500" v-if="item.device_type === 1">
                  <template v-slot:activator="{ attr, on }">
                    <span v-bind="attr" v-on="on">{{ $util.getTimerSequencePowerNames($t('dashboard.tooltips.powerSequencer'), item.sequence_powers) }}</span>
                  </template>
                  <span>{{ $util.getSequencePowerNames($t('dashboard.tooltips.powerSequencer'), item.sequence_powers) }}</span>
                </v-tooltip>
              </template>
              <template v-slot:item.sections="{ item }">
                <v-tooltip bottom open-on-hover open-delay="500">
                  <template v-slot:activator="{ attr, on }">
                    <span v-bind="attr" v-on="on">{{ $util.getTimerSections(item.sections) }}</span>
                  </template>
                  <span>{{ $util.getTimerSectionsNames(item.sections) }}</span>
                </v-tooltip>
              </template>
              <template v-slot:item.groups="{ item }">
                <v-tooltip bottom open-on-hover open-delay="500">
                  <template v-slot:activator="{ attr, on }">
                    <span v-bind="attr" v-on="on">{{ $util.getTimerGroups(item.groups) }}</span>
                  </template>
                  <span>{{ $util.getTimerGroupsName(item.groups) }}</span>
                </v-tooltip>
              </template>
              <template v-slot:item.status="{ item }">
                <v-chip :color="getColorOfTimerStatus(item.status)" dark>{{ getTimerStatusDescription(item) }}</v-chip>
              </template>
              <template v-slot:item.operation="{ item }">
                <v-icon
                  v-show="timerStatus === '0' || timerStatus === '2'"
                  large
                  @click.stop.prevent="preCancelSingleTimePoint(item)"
                  class="mx-1"
                  color="red darken-2"
                  :title="getCancelSingleTimePointTitle()"
                >
                  mdi-close-box
                </v-icon>
                <v-icon
                  v-show="timerStatus === '3'"
                  large
                  @click.stop.prevent="preRestartSingleTimePoint(item)"
                  class="mx-1"
                  color="primary darken-2"
                  :title="$t('dashboard.tooltips.cancelTodayTimer')"
                >
                  mdi-restart
                </v-icon>
              </template>
            </v-data-table>
          </v-card>
        </v-tab-item>
        <!--手动任务控制-->
        <v-tab-item :value="'tab-' + 5">
          <v-card class="mb-2">
            <v-card-title style="margin-top: -20px">
              <!-- 播放模式 -->
              <v-menu
                right
                bottom
                nudge-bottom="50"
                :offset-x="true"
                :disabled="manualTaskSelect.length === 0"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    fab
                    large
                    color="primary"
                    class="mx-4 mb-6"
                    v-bind="attrs"
                    :title="manualTaskSelect.length > 0 ? getPlayModeName(manualTaskSelect[0].play_mode) : $t('dashboard.playMode')"
                    v-on="on"
                    :disabled="manualTaskSelect.length === 0"
                  >
                    <v-icon
                      v-show="manualTaskSelect.length === 0"
                      large
                    >
                      mdi-playlist-music
                    </v-icon><v-icon
                      v-show="manualTaskSelect.length > 0 && manualTaskSelect[0].play_mode === 1"
                      large
                    >
                      mdi-numeric-1-box-multiple
                    </v-icon>
                    <v-icon
                      v-show="manualTaskSelect.length > 0 && manualTaskSelect[0].play_mode === 2"
                      large
                    >
                      mdi-repeat-once
                    </v-icon>
                    <v-icon
                      v-show="manualTaskSelect.length > 0 && manualTaskSelect[0].play_mode === 3"
                      large
                    >
                      mdi-playlist-play
                    </v-icon>
                    <v-icon
                      v-show="manualTaskSelect.length > 0 && manualTaskSelect[0].play_mode === 4"
                      large
                    >
                      mdi-repeat
                    </v-icon>
                    <v-icon
                      v-show="manualTaskSelect.length > 0 && manualTaskSelect[0].play_mode === 5"
                      large
                    >
                      mdi-shuffle-variant
                    </v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="item in playModes"
                    :key="item.id"
                    @click="changeManualTaskPlayMode(item.id)"
                  >
                    <v-list-item-icon>
                      <v-icon v-text="item.icon" />
                    </v-list-item-icon>
                    <v-list-item-title style="margin-left: -15px">
                      {{ item.name }}
                    </v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
              <!--上一曲-->
              <v-btn
                fab
                large
                color="primary"
                class="mx-4 mb-6"
                :title="$t('dashboard.previousTrack')"
                @click="setManualTaskPlayPreNext(1)"
                :disabled="manualTaskSelect.length === 0"
              >
                <v-icon large>
                  mdi-skip-previous
                </v-icon>
              </v-btn>
              <!--播放/暂停-->
              <v-btn
                fab
                large
                color="primary"
                class="mx-4 mb-6"
                :title="$t('dashboard.play')"
                @click="setManualTaskPlayStatus(1)"
                :disabled="manualTaskSelect.length === 0"
                v-show="manualTaskSelect.length === 0 || manualTaskSelect[0].play_status !== 1"
              >
                <v-icon large>
                  mdi-play
                </v-icon>
              </v-btn>
              <v-btn
                fab
                large
                color="primary"
                class="mx-4 mb-6"
                :title="$t('dashboard.pause')"
                @click="setManualTaskPlayStatus(2)"
                :disabled="manualTaskSelect.length === 0"
                v-show="manualTaskSelect.length !== 0 && manualTaskSelect[0].play_status === 1"
              >
                <v-icon large>
                  mdi-pause
                </v-icon>
              </v-btn>
              <!--下一曲-->
              <v-btn
                fab
                large
                color="primary"
                class="mx-4 mb-6"
                :title="$t('dashboard.nextTrack')"
                @click="setManualTaskPlayPreNext(2)"
                :disabled="manualTaskSelect.length === 0"
              >
                <v-icon large>
                  mdi-skip-next
                </v-icon>
              </v-btn>
              <!--停止-->
              <v-btn
                fab
                large
                color="primary"
                class="mx-4 mb-6"
                :title="$t('dashboard.stop')"
                @click="preStopManualTask"
                :disabled="manualTaskSelect.length === 0"
              >
                <v-icon large>
                  mdi-stop
                </v-icon>
              </v-btn>
              <v-spacer />

              <span :style="{ marginRight: '10px', color: getColorOfManualTaskPlayTime() }" :class="{'primary--text': IsValidManualTaskProgress()}" v-if="IsShowManualTaskProgress()">
                {{getManualTaskPlayTimeFormat(manualTaskSelect[0].cur_play_time)}}</span>
              <v-slider
                :value="manualTaskSelect[0].cur_play_time"
                :max="manualTaskSelect[0].song_duration"
                track-color="grey lighten-6"
                class="pt-6"
                :style="{ width: '200px' }"
                @change.passive="updateManualTaskProgress($event)"
                @mouseup="handleManualTaskProgressSliderRelease"
                @mousedown="handleManualTaskProgressSliderPress"
                v-if="IsShowManualTaskProgress()"
                :disabled="!IsValidManualTaskProgress()"
                >
              </v-slider>
              <span :style="{ marginLeft: '10px', color: getColorOfManualTaskPlayTime() }" :class="{'primary--text': IsValidManualTaskProgress()}" v-if="IsShowManualTaskProgress()">
                {{getManualTaskPlayTimeFormat(manualTaskSelect[0].song_duration)}}</span>

              <v-spacer />
              <v-spacer></v-spacer>
              <v-text-field
                v-model="manualTaskSearch"
                append-icon="mdi-magnify"
                :label="$t('dashboard.search')"
                single-line
                hide-details
                full-width
                style="padding-bottom: 35px"
              />
            </v-card-title>
            <v-data-table
              v-model="manualTaskSelect"
              :headers="manualTaskHeaders"
              :items="myManualTaskInfo"
              :search="manualTaskSearch"
              item-key="play_id"
              show-select
              class="elevation-1"
              :loading="myManualTaskInfo.length === 0"
              :loading-text="myManualTaskInfoLoadingText"
              style="margin-top: -20px"
              :items-per-page="10"
              @click:row="rowManualTaskClick"
              :footer-props="{
                itemsPerPageOptions: [10,25,50,100],
                showFirstLastPage: true,
                showCurrentPage: true,
                firstIcon: 'mdi-arrow-collapse-left',
                lastIcon: 'mdi-arrow-collapse-right',
                prevIcon: 'mdi-minus',
                nextIcon: 'mdi-plus'
              }"
            >
              <template v-slot:item.port="{ item }">
                <span>{{ myManualTaskInfo.map(function(x) {return x.play_id; }).indexOf(item.play_id) + 1 }}</span>
              </template>
              <template v-slot:item.play_mode="{ item }">
                <span>{{ $util.getPlayMode(item.play_mode) }}</span>
              </template>
              <template v-slot:item.sections="{ item }">
                <v-tooltip bottom open-on-hover open-delay="500">
                  <template v-slot:activator="{ attr, on }">
                    <span v-bind="attr" v-on="on">{{ $util.getTimerSections(item.sections) }}</span>
                  </template>
                  <span>{{ $util.getTimerSectionsNames(item.sections) }}</span>
                </v-tooltip>
              </template>
              <template v-slot:item.play_status="{ item }">
                <v-chip :color="getColorOfManualTaskStatus(item.play_status)" dark>
                  {{ getManualTaskStatusDescription(item.play_status) }}
                </v-chip>
              </template>
            </v-data-table>
          </v-card>
        </v-tab-item>
      </v-tabs-items>
    </v-container>
    <!--播放控制栏-->
    <v-footer
      class="justify-center"
      light
      absolute
      height="140px"
      max-height="140px"
      v-show="currentTab !== TabTypes.MANUAL_TASKS"
    >
      <!--一键告警-->
      <v-btn
          fab
          large
          :color="getManualAlarmBkgColor()"
          class="mx-3"
          :title="$t('dashboard.oneKeyAlarmBtn')"
          @click="preStartStopAlarm"
          v-if="checkIfShowManualAlarm"
      >
        <v-icon x-large>
          mdi-alarm-light-outline
        </v-icon>
      </v-btn>
      <!--监控控制-->
      <v-btn
          :disabled="!checkIfSelectedZonesHaveOnlineMonitor"
          fab
          large
          color="primary"
          class="mx-3"
          :title="getMonitorBtnTips()"
          @click="openMonitorRtsp"
          v-if="checkIfShowMonitor"
      >
        <v-icon large>
          mdi-webcam
        </v-icon>
      </v-btn>
      <!--开启监听控制-->
      <v-btn
        :disabled="!isAudioMonitorZoneOnline"
        fab
        large
        color="primary"
        class="mx-3"
        :title="getAudioMonitorBtnTips(true)"
        @click="setAudioMonitorSource(true)"
      >
        <v-icon large>
          mdi-headphones
        </v-icon>
      </v-btn>
      <!--关闭监听控制-->
      <v-btn
        :disabled="!isAudioMonitorZoneOnline"
        fab
        large
        color="primary"
        class="mx-3"
        :title="getAudioMonitorBtnTips(false)"
        @click="setAudioMonitorSource(false)"
      >
        <v-icon large>
          mdi-headphones-off
        </v-icon>
      </v-btn>

      <v-btn
        fab
        large
        color="primary"
        class="mx-3"
        :title="$t('dashboard.soundCardRecordBtn')"
        @click="preStartSoundCardRecord"
        v-if="checkIfShowSoundCardRecord"
      >
        <v-icon large>
          mdi-microphone
        </v-icon>
      </v-btn>
<!--
      <v-btn
        fab
        large
        color="primary"
        class="mx-3"
        :title="$t('dashboard.closeSoundCardRecord')"
        @click="preEndSoundCardRecord"
      >
        <v-icon large>
          mdi-microphone-off
        </v-icon>
      </v-btn>
-->
      <!--TTS语音-->
      <v-btn
        fab
        large
        color="primary"
        class="mx-3"
        :title="$t('dashboard.textToSpeech')"
        @click="preTextToSpeech"
      >
        <v-icon large>
          mdi-text-to-speech
        </v-icon>
      </v-btn>
      <v-menu
        left
        top
        :offset-x="true"
      >
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            fab
            large
            color="primary"
            class="mx-3"
            v-bind="attrs"
            :title="getPlayModeName(playMode)"
            v-on="on"
          >
            <v-icon
              v-show="playMode === 1"
              large
            >
              mdi-numeric-1-box-multiple
            </v-icon>
            <v-icon
              v-show="playMode === 2"
              large
            >
              mdi-repeat-once
            </v-icon>
            <v-icon
              v-show="playMode === 3"
              large
            >
              mdi-playlist-play
            </v-icon>
            <v-icon
              v-show="playMode === 4"
              large
            >
              mdi-repeat
            </v-icon>
            <v-icon
              v-show="playMode === 5"
              large
            >
              mdi-shuffle-variant
            </v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item
            v-for="item in playModes"
            :key="item.id"
            @click="changePlayMode(item.id)"
          >
            <v-list-item-icon>
              <v-icon v-text="item.icon" />
            </v-list-item-icon>
            <v-list-item-title style="margin-left: -15px">
              {{ item.name }}
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
      <v-btn
        fab
        large
        color="primary"
        class="mx-3"
        :title="$t('dashboard.playSourceBtn')"
        @click="prePlaySource"
      >
        <v-icon large>
          mdi-play
        </v-icon>
      </v-btn>
      <v-btn
        fab
        large
        color="primary"
        class="mx-3"
        :title="$t('dashboard.setIdleStatus')"
        @click="preSetIdleStatus"
      >
        <v-icon large>
          mdi-stop
        </v-icon>
      </v-btn>
      <v-col
        cols="2"
        class="volumeBar"
      >
        <!--todo 此处的css高度调整需要重新处理-->
        <v-slider
          :value="volume"
          prepend-icon="mdi-volume-high"
          thumb-label="always"
          class="pt-6"
          @change.passive="updateVolume($event)"
        />
      </v-col>
      <span>&copy; 2025</span>
      <span v-if="!isServerRegistered" class="pl-2">({{ $t('status.unregistered') }})</span>
    </v-footer>
    <v-dialog v-model="monitorDialog" max-width="1366" eager>
      <canvas id="video-canvas" style="width: 1366px;height: 768px;" />
    </v-dialog>
  </div>
</template>

<style>

</style>

<style scoped>
  /deep/ tr.v-data-table__selected {
    background: #c2c9f3 !important;
  }
  /* 调整被选中音源列表的背景颜色深度 */
  .theme--light.v-list-item--active:hover::before, .theme--light.v-list-item--active::before {
    opacity: 0.45;
  }
  .v-application .v-navigation-drawer .v-navigation-drawer__content .v-list .v-list-group .v-list-group__header .v-list-item__content .v-list-item__title {
    font-size: 16px;
    font-weight: 300;
  }
  .v-list-group__items .v-list-item .v-list-item__title {
    font-size: 14px;
  }
  /*修改文本框label的字体大小*/
  .v-radio >>> .v-label{
    font-size: 1.35em !important;
    font-weight: bold;
  }
  .volume-class >>> .v-input__slot:before {
    display: none;
  }
  /* 分区左上角序号与分区名称之间的间隔 */
  .v-application--is-ltr .v-list-item__avatar:first-child {
    margin-right: 6px;
  }
    /* 设置treeView的最大高度和滚动 */
  /deep/ .treeViewStyle {
    max-height: 68vh;
    overflow-y: auto;
  }
</style>

<script>
  import { mapState, mapGetters } from 'vuex'
  import axios from 'axios'
  import { getStringLenForGbk } from '../../plugins/utils'
  import { getDevicePlayStatusMap, getIconOfZoneStatus, isShowListView, customerVersion} from '../../plugins/websocket'
  import { deviceModelEnum } from '@/store'

  const devicePlayStatusIcon = getIconOfZoneStatus()

  const TabTypes = { // 选项卡类型
    ZONES: 1,
    GROUPS: 2,
    LIST_VIEW: 3,
    TIMER_TASKS: 4,
    MANUAL_TASKS: 5
  }

  // 定时器状态映射将在组件中动态创建

  export default {
    data: () => ({
      /* data for show */
      TabTypes, // Make TabTypes available to the template
      startTTSLoading: false,
      oldPage: 1,
      onlineOnlySwitch: false,
      createGroupUuid: null, // 创建分组的uuid
      uploadLoading: false,
      createGroupLoading: false,
      editGroupLoading: false,
      commonLoading: false, // 通用loading用于按钮
      isVolumeUpdating: false,
      textToSpeechRules: [],
      rules: {},
      commonErrorSnackBar: false,
      successMessages: '',
      commonSuccessSnackBar: false,
      errorMessages: '',
      /* 表单校验end */
      // 每页显示分区数量规则 todo
      PartitionShowRules: [
      ],
      // transfer 中包含了移到右边的分区数据
      editTransfer: [], // 编辑分组时使用，表示已选择的分区
      transfer: [], // 新建分组时使用，表示已选择的分区
      snackbarTimeout: 1500,
      tab: null,
      defaultColor: 'white',
      partitions: 2,
      volume: 50,
      showVolume: false,
      page: 1, // 当前页码
      rowsPerPageOptions: 40, // 每页显示数量
      // 音源列表相关
      currentList: null,
      currentSong: null,
      currentSongPathName: null,
      currentAudioCollectorChannelName: null,
      currentAudioCollectorChannelIndex: null,
      editGroupDialog: false,
      createGroupDialog: false,
      deleteGroupDialog: false,
      newGroupName: '',
      replacedGroupName: null,
      audioCollectorName: '',
      timerStatusMap: null,
      expandSongList: false,
      expandCollector: false,
      // 播放模式 1为单曲播放，2单曲循环播放，3顺序播放当前列表，4循环播放，5随机播放
      playMode: 1,
      currentTab: '',
      // TTS相关
      ttsDialog: false,
      ttsForm: false,
      ttsFileName: null,
      ttsSound: 0,
      ttsReadFromFile: null,
      uploadTtsFile: null,
      ttsContent: null,
      ttsVolume: 100,
      ttsPitch: 50,
      ttsSpeed: 50,
      ttsRdn: 0,
      // 监控相关
      monitorDialog: false,
      monitorPlayer: null,
      // 手动任务
      manualTaskHeaders: [],
      timerStatus: "2",
      todayTimerSearch: null,
      manualTaskSearch: null,
      manualTaskSelect: [],
      manuTaskProgressTimer: null,
      manuTaskProgressFirstTaskId: null,
      manuTaskProgressFirstTaskPlayStatus: null,
      manuTaskisDragging: false,
      zoneSearch: null,
      // 系统注册
      registrationDialog: false, // 系统注册对话框
      registrationCode: null, //注册码
      showPassword: false, // 注册密码是否显示
      // 列表视图相关
      selectedGroupInTree: [], // 视图左侧选中的分区
      selectedZonesInTree: [], // 视图右侧选中的分区
      zoneSearchInTree: null,
      pageInTree: 1,
      lastActiveTreeNode: null,
      activeTreeNodes: [],
      // 手动任务参数
      manualTaskPlayStatus: null,
      manualTaskPlayOrder: null,
      stopManualTaskDialog: false, // 停止任务对话框
      startManualAlarmDialog: false,
      applyPlayModeForAllManualTaskDialog: false,
      cancelSingleTimePointInTable: null,
      cancelSingleTimePointDialog: false,
      stopSingleTimePointDialog: false,
      restartSingleTimePointInTable: null,
      restartSingleTimePointDialog: false,
      applySelectedZonesContainsAudioMixer: false,  // 选中的分区包含音频协处理器，确认是否继续

      soundCardRecord: {
        dialog: false,
        httpBaseUrl: 'http://localhost:9997',
        isConnect: false,
        selectedDevice: 'output',
        deviceVolume: 50,
        transmissionMode: 1, // 默认低带宽
        soundCardInfo: {
          output: {
            isValid:	false,
            volume: 0
          },
          input: {
            isValid:	false,
            volume: 0
          }
        }
      }
    }),
    computed: {
      ...mapState(['selectedPartition', 'selectedGroups', 'playList', 'uuid', 'openAudioMonitorResult',
        'createNewGroupResult', 'setGroupZoneResult', 'removeGroupResult', 'playSourceResult', 'setVolumeResult',
        'requestPagingResult','requestPagingEvent',
        'setIdleStatusResult', 'playModeResult', 'editGroupResult', 'serverPlayMode',
        'playCollectResult', 'errorId', 'errorWsMessage', 'audioMonitorDeviceMac', 'textToSpeechResult', 'monitors',
        'timestamp', 'machineCode', 'serverRegisterResult', 'commandName', 'isCloudServer',
        'errorDuration', 'setTaskPlayModeResult', 'setTaskPlayStatusResult', 'setTaskPlayPreNextResult', 'audioCollectorInfo',
        'singleCancelTimePointResult', 'singleRestartTimePointResult', 'startManualAlarmResult', 'startManualAlarmStatus', 'currentSchemaId', 'user']),
      ...mapGetters(['isAdmin', 'apiSuccessMsg', 'isAudioMonitorZoneOnline', 'todayTimerInfoList',
        'isWindowsServer', 'isServerRegistered', 'isAudioCollectorAuthorizedUser', 'myManualTaskInfo']),
      // 响应式设备状态映射，会随语言变化而更新
      deviceStatus() {
        return getDevicePlayStatusMap()
      },
      // 响应式播放模式列表，会随语言变化而更新
      playModes() {
        return [
          { id: 1, name: this.$t('playMode.single'), icon: 'mdi-numeric-1-box-multiple' },
          { id: 2, name: this.$t('playMode.singleLoop'), icon: 'mdi-repeat-once' },
          { id: 3, name: this.$t('playMode.sequential'), icon: 'mdi-playlist-play' },
          { id: 4, name: this.$t('playMode.loop'), icon: 'mdi-repeat' },
          { id: 5, name: this.$t('playMode.random'), icon: 'mdi-shuffle-variant' }
        ]
      },
      // 定时任务（今日定时点相关）
      todayTimerHeaders() {
        const headers = [
          {text: this.$t('table.number'), align: 'center', value: 'port', sortable: false},
          {text: this.$t('table.name'), value: 'name', align: 'center', sortable: false},
          {text: this.$t('table.startTime'), value: 'start_time', align: 'center', sortable: false},
          {text: this.$t('table.endTime'), value: 'end_time', align: 'center', sortable: false},
          {text: this.$t('table.playMode'), value: 'play_mode', align: 'center', sortable: false},
          {text: this.$t('table.volume'), value: 'volume', align: 'center', sortable: false},
          {text: this.$t('table.selectedZones'), value: 'sections', align: 'center', sortable: false},
          {text: this.$t('table.selectedGroups'), value: 'groups', align: 'center', sortable: false},
          {text: this.$t('table.audioSource'), value: 'songs', align: 'center', sortable: false},
          {text: this.$t('table.status'), value: 'status', align: 'center', sortable: false},
        ]
        if (this.timerStatus === '0' || this.timerStatus === '3' || this.timerStatus === '2') {
          headers.push(
            {text: this.$t('table.operation'), value: 'operation', align: 'center', sortable: false}
          )
        }
        return headers
      },
      isShowTreeListView() {
        return isShowListView
      },
      // 选项卡
      tabList() {
        const tempTabList = [{ id: TabTypes.ZONES, name: this.$t('dashboard.tabs.zones'), icon: 'mdi-laptop' },
          { id: TabTypes.GROUPS, name: this.$t('dashboard.tabs.groups'), icon: 'mdi-heart' },
          { id: TabTypes.LIST_VIEW, name: this.$t('dashboard.tabs.listView'), icon: 'mdi-file-tree' },
          { id: TabTypes.TIMER_TASKS, name: this.$t('dashboard.tabs.timerTasks'), icon: 'mdi-calendar-clock' },
          { id: TabTypes.MANUAL_TASKS, name: this.$t('dashboard.tabs.manualTasks'), icon: 'mdi-calendar-check-outline' }]
        return this.isShowTreeListView ? tempTabList : tempTabList.filter(tab => tab.id !== TabTypes.LIST_VIEW)
      },
      zonesHeadersInTree() {
        /*设定表格列的宽度*/
        const header = [
          {text: this.$t('table.number'), align: 'center', value: 'id', sortable: true, width: '14%'},
          {text: this.$t('table.name'), value: 'name', align: 'center', sortable: true, width: '14%'},
          {text: this.$t('table.deviceIP'), value: 'ip', align: 'center', sortable: true, width: '14%'},
        ]
        // 运行于云端或Windows时不显示绑定摄像头列
        if (!this.isWindowsServer && !this.isCloudServer) {
          header.push({text: this.$t('table.bindingCamera'), value: 'monitor_mac', align: 'center', sortable: false, width: '14%'})
        }
        header.push({text: this.$t('table.volume'), value: 'volume', align: 'center', sortable: false, width: '14%'},
          {text: this.$t('table.runningStatus'), value: 'source', align: 'center', sortable: false, width: '30%'})
        return header
      },
      // 获取不同状态的任务
      getTodayTimersByStatus() {
        return this.todayTimerInfoList.filter(timer => timer.status + '' === this.timerStatus)
      },
      getTodayTimerLoadingText() {
        return this.$t('dashboard.messages.todayTimerCount', {
          status: this.timerStatusMap ? this.timerStatusMap.get(this.timerStatus) : ''
        })
      },
      myManualTaskInfoLoadingText() {
        return this.$t('dashboard.messages.manualTaskCountZero')
      },
      todayTimerRunningCount() {
        return this.$t('dashboard.status.executing') + '(' + this.todayTimerInfoList.filter(timer => timer.status === 2).length + ')'
      },
      todayTimerFinishedCount() {
        return this.$t('dashboard.status.executed') + '(' + this.todayTimerInfoList.filter(timer => timer.status === 1).length + ')'
      },
      todayTimerWaitingCount() {
        return this.$t('dashboard.status.notExecuted') + '(' + this.todayTimerInfoList.filter(timer => timer.status === 0).length + ')'
      },
      todayTimerCancelledCount() {
        return this.$t('dashboard.status.cancelled') + '(' + this.todayTimerInfoList.filter(timer => timer.status === 3).length + ')'
      },
      // 监听音箱是否在线
      isAudioMonitorOnline: function () {
        if (this.audioMonitorDeviceMac === '') {
          return false
        }
        return this.$ws.checkZoneOnlineByZoneMac(this.audioMonitorDeviceMac)
      },
      // 通用方法，获取已选择分组的分组信息
      singleSelectedGroup: function () {
        let groupInfo = null
        if (this.selectedGroups.length === 1) {
          this.groupList.forEach((group) => {
            if (group.group_id === this.selectedGroups[0]) {
              groupInfo = group
            }
          })
        }
        return groupInfo
      },
      // 已选择的分区或者分组或者任务的数量
      selectedNumbers: function () {
        let number = 0
        if (this.currentTab === TabTypes.ZONES) {
          number = this.selectedPartition.length
        } else if (this.currentTab === TabTypes.GROUPS) {
          number = this.selectedGroups.length
        }
        return number
      },
      selectedFilterNumber () {
        return this.myZones.filter(zone => this.selectedPartition.indexOf(zone.mac) !== -1).length
      },
      // 获取所有在线的分区
      onlineZones() {
        return this.$store.getters.onlineZones
      },
      // 所有解码设备
      decodeZones() {
        return this.$store.getters.decodeZones
      },
      // 在线分区和所有解码分区切换
      myZones() {
        const zones = this.onlineOnlySwitch ? this.$store.getters.onlineZones : this.$store.getters.decodeZones
        if (this.checkIfFilteredZones()) {
          return zones.filter(zone => (zone.ip.indexOf(this.zoneSearch) !== -1 || zone.name.toLowerCase().indexOf(this.zoneSearch.toLowerCase())) !== -1)
        }
        return zones
      },
      filterZones() {
        return this.myZones.slice(this.rowsPerPageOptions * (this.page - 1), this.rowsPerPageOptions * this.page)
      },
      filterPage() {
        if (this.myZones.length === 0) {
          return 1;
        }
        return Math.ceil(this.myZones.length / this.rowsPerPageOptions)
      },
      // 未离线的搜索分区
      selectedFilteredZoneMacs() {
        return this.myZones.reduce(function (res, option) {
          if (option.source !== -1) {
            res.push(option.mac)
          }
          return res
        }, []);
      },
      groupList() {
        return this.$store.getters.myGroupList
      },
      // 获取所有在线的音频采集卡
      onlineAudioCollectors() {
        return this.$store.getters.onlineAudioCollectors
      },
      audioCollectorChannels() {
        if (this.onlineAudioCollectors.length === 0) {
          return []
        }
        const channels = []
        this.onlineAudioCollectors.forEach(collector => {

          const audioCollector = this.audioCollectorInfo.filter(info => info.device_mac === collector.mac)
          if (Array.isArray(audioCollector) && audioCollector.length !== 0) {
            for (let i = 0; i < 4; i++) {
              channels.push(collector.name + ' (' + audioCollector[0].channels[i].channel_name + ')')
            }
          }
          else {
            channels.push(collector.name + ' (CH1)')
            channels.push(collector.name + ' (CH2)')
            channels.push(collector.name + ' (CH3)')
            channels.push(collector.name + ' (CH4)')
          }
        })
        return channels
      },
      checkIfShowManualAlarm() {
          return (this.isAdmin && this.isNotCloudIpSystem() && customerVersion !== 'C4A2')
      },
      checkIfShowSoundCardRecord() {
          return (!this.isCloudServer && customerVersion !== 'C0B0')
      },
      checkIfShowMonitor() {
          return (this.isAdmin && !this.isWindowsServer && !this.isCloudServer)
      },
      checkIfSelectedZonesHaveOnlineMonitor() {
        if (this.selectedPartition.length === 0) {
          return false
        }
        if (this.selectedPartition.length === 1) {
          const zone = this.$ws.getZoneByZoneMac(this.selectedPartition[0])
          return this.checkIfZoneHasOnlineMonitor(zone.monitor_mac)
        }
        const zones = this.$ws.getZonesByZoneMacs(this.selectedPartition)
        // 使用some方法检查是否有在线监控
        return zones.some(this.checkIfZoneHasOnlineMonitorByZone)
      },
      /**
       * 组装列表视图需要的分组数据
       * mandatory fields:
       * 1. id
       * 2. name
       * disabled字段，当所有分区都不在线时，设置为true,表示不可选中 （已去除）
       */
      treeViewGroupData() {
        let myGroupList = JSON.parse(JSON.stringify(this.groupList))
        const zoneMacs = this.decodeZones.length === 0 ? [] : this.decodeZones.map(zone => zone.mac)
        const zoneNames = this.$ws.getZoneNamesByZoneMacs(zoneMacs)
        const allZonesGroup = {
          group_account: this.user,
          group_id: this.$ws.getUuid(),
          group_name: this.$t('dashboard.allZones'),
          zone_names: zoneNames,
          zones: zoneMacs
        }
        // 将'所有分区'分组置于最前
        myGroupList.unshift(allZonesGroup)
        const treeGroup = []
        treeGroup.push({
          group_id: '0',
          group_name: this.$t('dashboard.allGroups'),
          children: myGroupList
        })
        return treeGroup
      },
      /**
       * 列表视图中，选中的分组内的分区数据
       */
      groupZonesInTree() {
        if (this.selectedGroupInTree.length === 0) {
          return []
        }
        const zoneArray = []
        this.selectedGroupInTree.forEach(group => zoneArray.push(group.zones))
        if (zoneArray.length === 0) {
          return []
        }
        const zoneMacs = [...new Set([].concat(...Object.values(zoneArray)))];
        if (zoneMacs.length === 0) {
          return []
        }
        const zonesInfo = this.$ws.getZonesByZoneMacs(zoneMacs)
        return zonesInfo.map(x => ({ ...x, isSelectable: x.source !== -1 }));
      },
    },
    /** ************** watch *****************/
    watch: {
      activeTreeNodes() {
        if (this.activeTreeNodes.length !== 0) {
          this.lastActiveTreeNode = this.activeTreeNodes[0];
        }
        const group = this.activeTreeNodes.length !== 0 ? this.activeTreeNodes[0] : this.lastActiveTreeNode
        if (group == null || group.disabled) {
          return
        }
        if (group.group_name === this.$t('dashboard.allGroups')) {
          const allSelectedGroups = this.treeViewGroupData[0].children.filter(g => !g.disabled);
          // if already all clicked, cancel the clicked status
          if (this.selectedGroupInTree.length === allSelectedGroups.length) {
            this.selectedGroupInTree = []
          } else {
            // else add all group into selected array
            this.selectedGroupInTree = allSelectedGroups
          }
        } else {
          if (this.selectedGroupInTree.some(g => g.group_id === group.group_id)) {
            // if already selected, cancel the clicked status
            this.selectedGroupInTree = this.selectedGroupInTree.filter(g => g.group_id !== group.group_id);
          } else {
            // else push the group
            this.selectedGroupInTree.push(group)
          }
        }
      },
      // 当选项卡切换时，清空选中分区及分组
      currentTab() {
        this.$store.commit('resetSelectedZones')
        this.$store.commit('resetSelectedGroups')
        // 分区视图清空
        this.page = 1
        this.zoneSearch = null
        // 列表视图清空
        this.pageInTree = 1
        this.selectedGroupInTree = []
        this.selectedZonesInTree = []
        this.activeTreeNodes = []
        this.lastActiveTreeNode = null
        this.zoneSearchInTree = null
      },
      // 监听列表选中分区
      selectedZonesInTree() {
        if (this.selectedZonesInTree.length === 0) {
          this.$store.commit('resetSelectedZones')
          return
        }
        // 设置音量为第一个分区的音量
        this.volume = this.selectedZonesInTree[0].volume
        const macs = this.selectedZonesInTree.map(x => x.mac)
        this.$store.commit('setSpecificSelectedZones', macs)
      },
      errorDuration () {
        this.snackbarTimeout = this.errorDuration
      },
      // 统一错误处理
      errorId: function () {
        if (this.$route.fullPath !== '/dashboard/dashboard') {
          return;
        }
        if (this.$store.state.errorId !== null) {
          this.errorMessages = this.$store.state.errorWsMessage
          this.commonErrorSnackBar = true
          // 关闭相关loading
          switch (this.commandName) {
            case 'add_group': this.createGroupLoading = false; break;
            case 'edit_group': this.editGroupLoading = false; break;
            case 'remove_group': this.commonLoading = false; break;
            default: break;
          }
        }
      },
      zoneSearch() {
        this.page = 1
      },
      // 当播放列表发生变化，清空所有播放相关变量
      playList: function () {
        this.currentSong = null
        this.currentList = null
        this.currentSongPathName = null
      },
      // 设置选中分区
      selectedPartition: {
        handler: function () {
          const store = this.$store
          const state = store.state
          if (state.selectedGroups.length > 0 && state.selectedPartition.length > 0) {
            store.commit('resetSelectedGroups')
          }
          // 先获取所有选中id
          if (state.selectedGroups.length !== 0) {
            return
          }
          const sendData = {
            command: 'set_select_zones',
            uuid: state.uuid,
            page: 1,
            page_count: 1,
            zone_count: state.selectedPartition.length,
            selected_id: state.selectIdTime,
            zones_mac: state.selectedPartition,
          }
          store.commit('WEBSOCKET_SEND', sendData)
        },
        deep: true
      },
      selectedGroups: {
        handler: function () {
          const store = this.$store
          const state = store.state
          if (state.selectedPartition.length > 0 && state.selectedGroups.length > 0) {
            store.commit('resetSelectedZones')
          }
          if (state.selectedPartition.length !== 0) {
            return
          }
          const macs = this.$ws.getZoneMacsByGroupId(state.selectedGroups)
          const sendData = {
            command: 'set_select_zones',
            uuid: state.uuid,
            page: 1,
            page_count: 1,
            zone_count: macs.length,
            selected_id: state.selectIdTime,
            zones_mac: macs,
          }
          store.commit('WEBSOCKET_SEND', sendData)
        },
        deep: true
      },
      // 根据创建分组结果进行更新
      createNewGroupResult: function () {
        if (this.createNewGroupResult === 0) {
          this.createGroupLoading = false;
          this.successMessages = this.$t('dashboard.messages.createGroupSuccess')
          this.commonSuccessSnackBar = true
          this.createGroupDialog = false
          this.newGroupName = null
          this.transfer = []
          this.$store.commit('updateCreateGroupResult', null)
        }
      },
      // 根据编辑分组结果进行更新
      editGroupResult: function () {
        if (this.editGroupResult === 0) {
          this.editGroupLoading = false
          this.successMessages = this.$t('dashboard.messages.editGroupSuccess')
          this.commonSuccessSnackBar = true
          this.editGroupDialog = false
          this.replacedGroupName = null
          this.$store.commit('updateEditGroupResult', null)
          setTimeout(() => {
            this.editTransfer = []
          }, 200)
        }
      },
      // 删除分组结果
      removeGroupResult: function () {
        if (this.removeGroupResult === 0) {
          this.commonLoading = false
          this.successMessages = this.$t('dashboard.messages.deleteGroupSuccess')
          this.commonSuccessSnackBar = true
          this.deleteGroupDialog = false
          this.$store.commit('updateRemoveGroupResult')
        }
      },
      // 根据调节音量结果进行更新
      setVolumeResult: function () {
        if (this.$store.state.setVolumeResult === 0) {
          if(!this.soundCardRecord.dialog) {
            this.successMessages = this.$t('dashboard.messages.adjustVolumeSuccess')
            this.commonSuccessSnackBar = true
          }
          this.$store.commit('updateSetVolumeResult')
        }
      },
      requestPagingResult: function () {
        if (this.$store.state.requestPagingResult === 0) {
          // console.log("this.$store.state.requestPagingEvent="+this.$store.state.requestPagingEvent)
          if(this.$store.state.requestPagingEvent === 1) {
            this.NotifySoundCardClientStart()
          }
          else if(this.$store.state.requestPagingEvent === 0) {
            this.successMessages = this.$t('dashboard.messages.stopRecording')
            this.commonSuccessSnackBar = true
          }
        } else if (this.$store.state.requestPagingResult === this.$store.state.deviceNotSupportFunction) {
          // 设备不支持该功能
          this.errorMessages = this.$t('dashboard.messages.deviceNotSupportFunction')
          this.commonErrorSnackBar = true
        }
        this.$store.commit('updateRequestPagingResult')
      },
      // 根据停止播放结果进行更新
      setIdleStatusResult: function () {
        if (this.$store.state.setIdleStatusResult === 0) {
          this.successMessages = this.$t('dashboard.messages.stopPlaySuccess')
          this.commonSuccessSnackBar = true
          this.$store.commit('updateSetIdleStatusResult')
        }
      },
      // 根据修改播放模式结果进行更新
      playModeResult: function () {
        if (this.playModeResult === 0) {
          this.playMode = this.serverPlayMode
          // 20220112 如果账户手动数量大于0，则需手动确认是否应用于所有手动任务
          if (this.myManualTaskInfo.length > 0) {
            this.applyPlayModeForAllManualTaskDialog = true
          } else {
            // 否则直接提示'设置播放模式成功'
            this.successMessages = this.$t('dashboard.setPlayModeSuccess')
            this.commonSuccessSnackBar = true
          }
          this.$store.commit('updatePlayModeResult')
        }
      },
      // 根据播放节目源结果进行更新
      playSourceResult: function () {
        if (this.$store.state.playSourceResult === 0) {
          this.successMessages = this.$t('dashboard.messages.playSourceSuccess')
          this.commonSuccessSnackBar = true
          this.$store.commit('updatePlaySourceResult')
        }
      },
      // 根据音频采集结果进行更新
      playCollectResult: function () {
        if (this.$store.state.playCollectResult === 0) {
          this.successMessages = this.$t('dashboard.messages.playCollectSuccess')
          this.commonSuccessSnackBar = true
          this.$store.commit('updatePlayCollectResult')
        }
      },
      // 切换只显示在线后，页码回到1
      onlineOnlySwitch: function () {
        const tempPage = this.page
        this.page = this.oldPage
        this.oldPage = tempPage
      },
      // 设置监听结果
      openAudioMonitorResult: function () {
        if (this.openAudioMonitorResult === 0) {
          this.successMessages = this.apiSuccessMsg
          this.commonSuccessSnackBar = true
          this.$store.commit('updateOpenAudioMonitorResult', null)
        }
      },
      // TTS结果
      textToSpeechResult: function () {
        if (this.textToSpeechResult === 0) {
          this.successMessages = this.apiSuccessMsg
          this.commonSuccessSnackBar = true
          this.ttsDialog = false
          // 重置文本框及其他状态
          this.ttsContent = null
          this.ttsFileName = null
          this.ttsSound = 0
          // this.ttsVolume = 50
          // this.ttsPitch = 50
          // this.ttsSpeed = 50
          this.ttsRdn = 0
          // this.$refs.ttsForm.reset()
          this.$store.commit('updateTextToSpeechResult', null)
        }
      },
      // 视频监控页面
      monitorDialog() {
        if (this.monitorDialog === true) {
          const canvas = document.getElementById('video-canvas')
          // console.log(canvas)
          this.monitorPlayer = new JSMpeg.Player(this.$cameraHost, {
            canvas: canvas,
            autoplay: true,
            disableGl: true,
            videoBufferSize: 1500 * 1024,
            audio: false
          });
          // console.log(this.monitorPlayer)
        } else {
          const canvas = document.getElementById('video-canvas')
          // console.log('关闭监控画面123123123123')
          // console.log(this.monitorPlayer)
          // console.log(canvas)
          if (this.monitorPlayer != null && canvas != null) {
            // console.log('关闭监控画面')
            this.monitorPlayer.pause()
            this.monitorPlayer.destroy()
            const zone = this.$ws.getZoneByZoneMac(this.selectedPartition[0])
            this.$ws.monitorRtsp(2, zone.monitor_mac)
          }
        }
      },
      // 系统注册结果
      serverRegisterResult () {
        if (this.$route.fullPath !== '/dashboard/dashboard') {
          return;
        }
        if (this.serverRegisterResult === 0) {
          this.successMessages = this.$t('dashboard.messages.registerSuccess')
          this.commonSuccessSnackBar = true
          this.registrationDialog = false
          this.$store.commit('updateLogoutBySelf', true)
          // this.systemData[2].data = '已注册'
          // // 刷新系统注册状态
          // this.$store.commit('updateSystemRegistrationType', 2)
          // 回退到登录界面
          setTimeout(() => {
            this.$router.push('/')
          }, 1000)
          this.$store.commit('updateServerRegisterResult')
        }
      },
      // 设置手动任务播放模式
      setTaskPlayModeResult() {
        if (this.setTaskPlayModeResult === 0) {
          this.successMessages = this.$t('dashboard.messages.setPlayModeSuccess')
          this.applyPlayModeForAllManualTaskDialog = false
          this.commonSuccessSnackBar = true
          this.$store.commit('updateSetTaskPlayModeResult', null);
        }
      },
      // 控制手动任务播放状态
      setTaskPlayStatusResult() {
        if (this.setTaskPlayStatusResult === 0) {
          if (this.manualTaskPlayStatus === 4) {
            this.stopManualTaskDialog = false
            // 停止播放，清空选中对象
            this.manualTaskSelect = []
            this.successMessages = this.$t('dashboard.messages.taskStopped');
          } else {
            this.successMessages = this.$t('dashboard.messages.setPlayStatusSuccess');
          }
          this.commonSuccessSnackBar = true
          this.$store.commit('updateSetTaskPlayStatusResult', null)
        }
      },
      // 控制手动任务播放上一曲/下一曲
      setTaskPlayPreNextResult() {
        if (this.setTaskPlayPreNextResult === 0) {
          const word = this.manualTaskPlayOrder === 1 ? this.$t('dashboard.previousTrack') : this.$t('dashboard.nextTrack')
          this.successMessages = this.$t('dashboard.messages.controlPrevNextSuccess', { word })
          this.commonSuccessSnackBar = true
          this.$store.commit('updateSetTaskPlayPreNextResult', null)
        }
      },
      // 启动/停止手动告警
      startManualAlarmResult() {
        if(this.startManualAlarmResult === null)
          return
        this.startManualAlarmDialog = false;
        if (this.startManualAlarmResult === 0) {
          const status = this.startManualAlarmStatus == 1 ? this.$t('dashboard.status.started') : this.$t('dashboard.status.stopped')
          this.successMessages = this.$t('dashboard.messages.alarmTaskStatus', { status })
          this.commonSuccessSnackBar = true
          this.$store.commit('UpdateStartManualAlarmResult', null);
        }
      },
      // 服务端通知更新手动任务后，更新本地选中的手动任务视图
      myManualTaskInfo: {
        handler: function () {
          if (this.manualTaskSelect.length === 0) {
            return
          }
          if (this.manualTaskPlayStatus === 4) {
            this.manualTaskSelect = []
            return
          }
          for (let i = 0; i < this.manualTaskSelect.length; i++) {
            const task = this.myManualTaskInfo.find(t => t.play_id === this.manualTaskSelect[i].play_id);
            if (task == null) {
              // 有任务被停止，清空选中列表
              this.manualTaskSelect = []
              break
            } else {
              // 更新选中任务信息
              this.manualTaskSelect.splice(i, 1, task);
            }
          }
        },
        deep: true
      },
      manualTaskSelect: {
        handler: function (newVal) {
          if (newVal.length === 0) {
            this.manuTaskProgressFirstTaskId = null
            this.manuTaskProgressFirstTaskPlayStatus = null
            if (this.manuTaskProgressTimer) {
              clearInterval(this.manuTaskProgressTimer);
              this.manuTaskProgressTimer = null; // 添加这一行代码
            }
            return;
          }
          if (this.manuTaskProgressFirstTaskId !== newVal[0].play_id || this.manuTaskProgressFirstTaskPlayStatus !== newVal[0].play_status) {
            this.manuTaskProgressFirstTaskId = newVal[0].play_id
            this.manuTaskProgressFirstTaskPlayStatus = newVal[0].play_status
            this.manualTaskPlayStatus = newVal[0].play_status
            this.queryManualTaskProgress()
            this.resumeManualTaskProgressTimer()
          }
        }
      },
      singleCancelTimePointResult() {
        if (this.singleCancelTimePointResult === 0) {
          if(this.cancelSingleTimePointDialog === true)
            this.successMessages = this.$t('dashboard.messages.cancelTimerSuccess')
          else if(this.stopSingleTimePointDialog === true)
            this.successMessages = this.$t('dashboard.messages.timerTaskStopped')
          this.commonSuccessSnackBar = true
          this.cancelSingleTimePointInTable = null
          this.cancelSingleTimePointDialog = false
          this.stopSingleTimePointDialog = false
          this.$store.commit('updateSingleCancelTimePointResult', null);
        }
      },
      singleRestartTimePointResult() {
        if (this.singleRestartTimePointResult === 0) {
          this.successMessages = this.$t('dashboard.messages.restoreTimerSuccess');
          this.commonSuccessSnackBar = true
          this.restartSingleTimePointInTable = null
          this.restartSingleTimePointDialog = false
          this.$store.commit('updateSingleRestartTimePointResult', null)
        }
      },
    },
    /** **************封装*****************/
    created () {
      this.playMode = this.$store.state.serverPlayMode
    },
    mounted() {
      // 20210716 管理员登录成功，如果系统是未注册版本，进入控制中心后显示注册框。
      // if (this.isAdmin && !this.isServerRegistered) {
      //   this.registrationDialog = true;
      // }

      // 从本地存储恢复每页显示数量设置
      const savedRowsPerPageOptions = localStorage.getItem('dashboardRowsPerPageOptions')
      if (savedRowsPerPageOptions && !isNaN(savedRowsPerPageOptions) && parseInt(savedRowsPerPageOptions) > 0) {
        this.rowsPerPageOptions = parseInt(savedRowsPerPageOptions)
      }

      // 初始化当前选项卡
      this.currentTab = TabTypes.ZONES

      // 初始化手动任务表格标题
      this.manualTaskHeaders = [
        { text: this.$t('table.number'), align: 'center', value: 'port', sortable: false },
        { text: this.$t('table.user'), value: 'account', align: 'center', sortable: false },
        { text: this.$t('table.taskName'), value: 'task_name', align: 'center', sortable: false },
        { text: this.$t('table.zones'), value: 'sections', align: 'center', sortable: false },
        { text: this.$t('table.playingSong'), value: 'song_name', align: 'center', sortable: false },
        { text: this.$t('table.playMode'), value: 'play_mode', align: 'center', sortable: false },
        { text: this.$t('table.status'), value: 'play_status', align: 'center', sortable: false },
      ]

      // 初始化音频采集器名称
      this.audioCollectorName = this.$t('dashboard.audioCollection')

      // 初始化表单验证规则
      this.textToSpeechRules = [
        v => v.length <= 5000 || this.$t('validation.maxCharacters', { max: 5000 }),
        v => !!v || this.$t('validation.textRequired'),
      ]

      this.rules = {
        required: v => !!v || this.$t('validation.textRequired'),
        ttsContent: v => (v || '').length <= 5000 || this.$t('validation.ttsContentMaxLength'),
        ttsFileName: v => (v || '').length <= 20 || this.$t('validation.fileNameMaxLength'),
        length: len => v => (v || '').length >= len || this.$t('validation.invalidLength', { len }),
        password: v => !!(v || '').match(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*(_|[^\w])).+$/) ||
          this.$t('validation.passwordRequirements'),
      }

      // 初始化默认消息
      this.successMessages = this.$t('common.success')
      this.errorMessages = this.$t('validation.invalidInput')

      // 初始化定时器状态映射
      this.timerStatusMap = new Map([
        ['0', this.$t('dashboard.status.notExecuted')],
        ['1', this.$t('dashboard.status.executed')],
        ['2', this.$t('dashboard.status.executing')],
        ['3', this.$t('dashboard.status.cancelled')],
      ])
    },
    methods: {
      ttsTextUTF8ByteLength(ttsContent) {
        return new TextEncoder().encode(ttsContent || '').length;
      },
      ttsTextLengthRules() {
        return [
          (value) => {
            const maxBytes = 6000;
            const utf8Bytes = new TextEncoder().encode(value || '').length;
            return utf8Bytes < maxBytes || this.$t('tts.textTooLong', { maxBytes });
          }
        ];
      },
      getManualAlarmBkgColor()
      {
        // 客制化：红色系的不改变颜色，其他采用红色
        if(customerVersion !== 'C1A0' && customerVersion !== 'C3A0' && customerVersion !== 'C6A0')
          return "red"
        else
          return "primary"
      },
      getAudioCollectorDescription(prefix, mac, channel) {
        if (prefix == null || mac == null || channel == null) {
          return ''
        }
        const zone = this.$ws.getZoneByZoneMac(mac);
        const channelDescription = '（CH' + channel + '）'
        const tip = zone == null ? mac + channelDescription :zone.name + channelDescription
        return prefix + tip
      },
      preRestartSingleTimePoint(item) {
        if (item == null) {
          return
        }
        const currentTime = this.formatTime(new Date())
        if (currentTime >= item.end_time) {
          this.errorMessages = this.$t('dashboard.messages.timerTimePassed')
          this.commonErrorSnackBar = true
          return;
        }
        this.restartSingleTimePointInTable = item;
        this.restartSingleTimePointDialog = true
      },
      getCancelSingleTimePointTitle() {
        if(this.timerStatus === '0')
          return this.$t('dashboard.tooltips.cancelTodayTimer')
        else if(this.timerStatus === '2')
          return this.$t('dashboard.tooltips.stopTimer')
      },
      preCancelSingleTimePoint(item) {
        if (item == null) {
          return
        }
        this.cancelSingleTimePointInTable = item
        if(this.timerStatus === '0')
          this.cancelSingleTimePointDialog = true
        else if(this.timerStatus === '2')
          this.stopSingleTimePointDialog = true
      },
      restartSingleTimePoint() {
        this.$ws.singleCancelTimePoint(this.currentSchemaId, this.restartSingleTimePointInTable.timer_id,
          this.restartSingleTimePointInTable.name, false)
      },
      cancelSingleTimePoint() {
        this.$ws.singleCancelTimePoint(this.currentSchemaId, this.cancelSingleTimePointInTable.timer_id,
          this.cancelSingleTimePointInTable.name, true)
      },
      formatTime (time) {
        const hour = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
        const minute = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
        const second = time.getSeconds() < 10 ? '0' + time.getSeconds() : time.getSeconds()
        return hour + ':' + minute + ':' + second
      },
      // 选中手动任务的分区
      rowManualTaskClick: function (item, row) {
        if (row.isSelected) {
          row.select(false)
        } else {
          row.select(true)
        }
      },
      // 选中列表视图的分区
      rowZoneTreeClick: function (item, row) {
        if (this.deviceStatus.get(item.source + '') === this.$t('status.offline')) {
          return
        }
        if (row.isSelected) {
          row.select(false)
        } else {
          row.select(true)
        }
      },
      // 获取运行状态
      getMonitorStatus: function (item) {
        const statusString = this.deviceStatus.get(item.source + '')
        if (item.source_name == null || item.source_name.trim() === '') {
          return statusString
        }
        return statusString + '：' + this.getZoneSourceName(item)
      },
      getVolumeByPresent(volume) {
        return volume + '%'
      },
      getFormattedMonitor(monitorMac) {
        if (monitorMac == null || monitorMac.trim() === '') {
          return ''
        }
        const name = this.getMonitorNameByMac(monitorMac)
        return name !== '' ? name : monitorMac.toUpperCase()
      },
      // 列表视图分区背景颜色设置
      itemRowBackground: function (item) {
        if (this.$vuetify.theme.dark) {
          return item.source === -1 ? 'darkOfflineZone' : '';
        }
        return item.source === -1 ? 'offlineZone' : 'onlineZone'
      },
      rowsPerPageChange: function (e) {
        const value = e.target.value
        if (value <= 0) {
          this.errorMessages = this.$t('dashboard.messages.itemsPerPageCannotBeZero')
          this.commonErrorSnackBar = true
          e.target.value = this.rowsPerPageOptions
        } else {
          this.rowsPerPageOptions = value
          this.page = 1
          // 保存到本地存储
          localStorage.setItem('dashboardRowsPerPageOptions', value)
        }
      },
      // 修改单分区音量
      updateVolume: function (vol) {
        this.volume = vol
        // 音量调节
        if (this.selectedPartition.length === 0 && this.selectedGroups.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.selectZoneOrGroupForVolume')
          this.commonErrorSnackBar = true
          return
        }
        if (vol < 0 || vol > 100) {
          return
        }
        this.$ws.setZoneVolume(vol)
      },
      // 选择分区
      selectableCards: function (mac) {
        this.$store.commit('updateSelectedPartition', mac)
        // 设置音量为第一个分区的音量
        if (this.selectedPartition.length === 1) {
          this.volume = this.$ws.getZoneByZoneMac(this.selectedPartition[0]).volume
        }
      },
      // 全选和反全选所有分区
      selectAllCards: function () {
        // 如果全选，则跳到只显示在线分区
        if (!this.onlineOnlySwitch) {
          this.onlineOnlySwitch = true
        }
        this.$store.commit('updateAllSelectedPartition')
      },
      // 全选搜索分区
      selectAllFilteredCards: function () {
        // 如果全选，则跳到只显示在线分区
        if (!this.onlineOnlySwitch) {
          this.onlineOnlySwitch = true
        }
        // console.log('测试： ' + this.selectedFilteredZoneMacs)
        this.$store.commit('updateAllFilteredCards', this.selectedFilteredZoneMacs)
      },
      // 选择分组
      selectableGroups: function (id) {
        this.$store.commit('updateSelectedGroups', id)
      },
      // 全选和反全选所有分组
      selectAllGroups: function (groupList) {
        this.$store.commit('updateAllSelectedGroups', groupList)
      },
      // 监听选中歌曲的更改
      songChanged: function (name, pathName) {
        if (this.currentSong == null || this.currentSong !== name) {
          this.currentSong = name
          this.currentSongPathName = pathName.substring(pathName.lastIndexOf('/') + 1)
        }
        this.currentAudioCollectorChannelName = null
        this.currentAudioCollectorChannelIndex = null
      },
      // 监听选中列表的修改
      listChanged: function (listId) {
        // todo 对于多个音频采集器的情况需要使用子属性active
        this.expandCollector = false
        this.currentAudioCollectorChannelName = null
        this.currentAudioCollectorChannelIndex = null
        // this.playList.forEach((list, index) => {
        //   if (list.list_id === listId) {
        //     list.active = !list.active
        //   }
        // })
        if (this.currentList !== listId) {
          // 未点选或者切换列表时，更新currentList
          this.currentList = listId
          this.currentSong = null
          this.currentSongPathName = null
        } else {
          // 点选同一列表，收起列表
          this.currentList = null
          this.currentSong = null
          this.currentSongPathName = null
        }
      },
      // 获取播放歌曲名称
      getZoneSourceName: function (event) {
        // 处于网络点播、定时、监控事件、消防告警音源时不显示歌曲后缀名
        return (event.source === 2 || event.source === 3 || event.source === 6 || event.source === 8) ? event.source_name.substring(0, event.source_name.lastIndexOf('.') != -1 ? event.source_name.lastIndexOf('.') : event.source_name.length) : event.source_name
      },
      // 获取播放歌曲名称长度
      // 监听选中音源列表的更改
      collectorChanged: function (channelName, index) {
        if (this.currentAudioCollectorChannelName == null || this.currentAudioCollectorChannelName !== channelName) {
          this.currentAudioCollectorChannelName = channelName
        }
        this.currentAudioCollectorChannelIndex = index
      },
      clickCollector: function () {
        this.$store.commit('collapseAllSongList')
        this.currentList = null
        this.currentSong = null
        this.currentSongPathName = null
      },
      // 更改播放模式（顺序播放，单曲循环等）
      changePlayMode: function (id) {
        // 发送请求更新播放模式
        this.$ws.setPlayMode(id)
      },
      //更新手动任务播放进度
      updateManualTaskProgress: function (playTime) {
          if(!this.IsShowManualTaskProgress()) {
            return
          }
          if (this.manualTaskSelect.length === 0) {
            return
          }
          const id = this.manualTaskSelect[0].play_id;
          //const ids = this.manualTaskSelect.map(task => task.play_id)
          //const ids = [id];
          this.$ws.setTaskProgress(true, id, playTime)
      },
      queryManualTaskProgress: function () {
        if(!this.IsShowManualTaskProgress()) {
          return
        }
        if (this.manualTaskSelect.length === 0) {
          return
        }
        const id = this.manualTaskSelect[0].play_id;
        this.$ws.setTaskProgress(false, id, 0)
      },
      handleManualTaskProgressSliderPress() {
        this.manuTaskisDragging = true;
        if (this.manuTaskProgressTimer) {
          clearInterval(this.manuTaskProgressTimer);
          this.manuTaskProgressTimer = null; // 添加这一行代码
        }
      },
      handleManualTaskProgressSliderRelease() {
        this.manuTaskisDragging = false;
        this.resumeManualTaskProgressTimer();
      },
      resumeManualTaskProgressTimer() {
        if(!this.IsShowManualTaskProgress()) {
          return
        }
        if(this.manuTaskisDragging || this.manualTaskSelect.length === 0) {
          return
        }
        if (this.manualTaskSelect[0].play_status === 1) {
          if (!this.manuTaskProgressTimer) {
            this.manuTaskProgressTimer = setInterval(() => {
              if (this.manualTaskSelect.length > 0) {
                this.manualTaskSelect[0].cur_play_time++;
              }
            }, 1000);
          }
        }
        else {
          if (this.manuTaskProgressTimer) {
            clearInterval(this.manuTaskProgressTimer);
            this.manuTaskProgressTimer = null; // 添加这一行代码
          }
        }
      },
      // 手动任务进度控制是否显示
      IsShowManualTaskProgress() {
        if(this.manualTaskSelect.length === 0) {
          return false
        }
        return this.manualTaskSelect[0].cur_play_time !== undefined
      },
      // 手动任务进度控制是否有效
      IsValidManualTaskProgress() {
        if(this.manualTaskSelect.length === 0) {
          return false
        }
        return this.manualTaskSelect[0].play_status === 1
      },
      // 获取手动任务当前播放时间格式，需使用v-html消费
      getManualTaskPlayTimeFormat(playTime) {
        const songDurationNumber = playTime != null ? playTime : 0
        let duration = new Date(songDurationNumber * 1000).toISOString();
        if (duration.substr(11, 2) !== '00') {
          // 超过1h的歌曲，显示小时
          duration = duration.substr(11, 8)
        } else {
          // 不超过1h的歌曲，不显示小时
          duration = duration.substr(14, 5)
        }
        return duration
      },
      // 设置手动任务当前播放时间的颜色显示
      getColorOfManualTaskPlayTime() {
        if (!this.IsValidManualTaskProgress()) {
          return 'grey'
        }
        else {
          return 'primary--text'
        }
      },
      // 设置手动任务不同状态的颜色显示
      getColorOfManualTaskStatus (status) {
        // 播放
        if (status === 1) {
          return 'primary'
        }
        // 暂停
        if (status === 2) {
          return 'grey'
        }
        // 停止
        return 'grey'
      },
      // 更改手动任务播放模式（顺序播放，单曲循环等）
      changeManualTaskPlayMode: function (playMode) {
        const ids = this.manualTaskSelect.map(task => task.play_id)
        this.$ws.setTaskPlayMode(ids, playMode, false)
      },
      setManualTaskPlayPreNext(action) {
        this.manualTaskPlayOrder = action
        const ids = this.manualTaskSelect.map(task => task.play_id)
        this.$ws.setTaskPlayPreNext(ids, action)
      },
      // 20220106 增加停止任务确认对话框
      preStopManualTask() {
        this.stopManualTaskDialog = true
      },
      setManualTaskPlayStatus(playStatus) {
        this.manualTaskPlayStatus = playStatus
        const ids = this.manualTaskSelect.map(task => task.play_id)
        this.$ws.setTaskPlayStatus(ids, playStatus)
      },
      // 获取播放模式名称
      getPlayModeName (id) {
        for (let i = 0; i < this.playModes.length; i++) {
          if (this.playModes[i].id === id) {
            return this.playModes[i].name
          }
        }
      },
      // 更改当前选项卡
      setCurrentTab: function (tab) {
        this.currentTab = tab.id
      },
      // 检查是否有过滤条件
      checkIfFilteredZones() {
        return this.zoneSearch != null && this.zoneSearch.trim() !== ''
      },
      /**
       * 分组相关
       */
      // 删除分组
      deleteSelectedGroups: function () {
        // this.deleteGroupDialog = false
        this.selectedGroups.forEach((groupId) => {
          const sendData = {
            uuid: this.uuid,
            command: 'remove_group',
            group_id: groupId,
          }
          this.commonLoading = true
          this.$store.commit('WEBSOCKET_SEND', sendData)
        })
      },
      /* ************************** 分组相关 ************************** */
      // 新建分组
      createNewGroup: function () {
        // 未输入新分组名字，弹出提示框
        if (!this.newGroupName) {
          this.errorMessages = this.$t('dashboard.messages.groupNameEmpty')
          this.commonErrorSnackBar = true
          return
        }
        // 未选择分区，弹出提示框
        if (this.transfer.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.selectAtLeastOneZone')
          this.commonErrorSnackBar = true
          return
        }
        this.createGroupLoading = true
        const sendData = {
          command: 'add_group',
          uuid: this.uuid,
          command_uuid: this.$ws.getUuid(),
          zone_count: this.transfer.length, // 设置添加分组数量为0，这样可以直接添加分组成功,然后再设置分区分组 2020/08/29
          group_name: this.newGroupName,
        }
        this.createGroupUuid = sendData.command_uuid
        this.$store.commit('updateSetGroupZoneResult', null)
        this.$store.commit('WEBSOCKET_SEND', sendData)
        this.$ws.setGroupZone(this.createGroupUuid, this.createGroupUuid, this.transfer)
      },
      preEditGroup: function () {
        if (this.selectedGroups.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.selectOneGroupToEdit')
          this.commonErrorSnackBar = true
          return
        }
        if (this.selectedGroups.length > 1) {
          this.errorMessages = this.$t('dashboard.messages.canOnlyEditOneGroup')
          this.commonErrorSnackBar = true
          return
        }
        this.editTransfer = this.singleSelectedGroup.zones
        this.replacedGroupName = this.singleSelectedGroup.group_name
        this.editGroupDialog = true
      },
      editGroup: function () {
        // 1. 如果修改了分组名，则发送rename指令
        if (this.singleSelectedGroup.group_name !== this.replacedGroupName) {
          // 如果分组名已使用，则报错提示
          const groupNames = []
          this.groupList.forEach((group) => {
            groupNames.push(group.group_name)
          })
          if (groupNames.indexOf(this.replacedGroupName) > -1) {
            // 重复则报错
            this.errorMessages = this.$t('dashboard.messages.groupNameAlreadyUsed')
            this.commonErrorSnackBar = true
            return
          } else {
            const renameData = {
              command: 'rename_group',
              uuid: this.uuid,
              group_id: this.singleSelectedGroup.group_id,
              group_name: this.replacedGroupName,
            }
            this.$store.commit('WEBSOCKET_SEND', renameData)
          }
        }
        this.editGroupLoading = true
        // 2. 发送set_group指令
        const sendData = {
          command: 'edit_group',
          uuid: this.uuid,
          command_uuid: this.$ws.getUuid(),
          group_id: this.singleSelectedGroup.group_id,
          group_name: this.replacedGroupName,
          zone_count: this.editTransfer.length,
        }
        this.$store.commit('updateSetGroupZoneResult', null)
        this.$store.commit('WEBSOCKET_SEND', sendData)
        // 3. 发送set_group_zone指令
        this.$ws.setGroupZone(this.singleSelectedGroup.group_id, sendData.command_uuid, this.editTransfer)
      },
      preDeleteGroup: function () {
        if (this.selectedGroups.length < 1) {
          this.errorMessages = this.$t('dashboard.messages.selectAtLeastOneGroup')
          this.commonErrorSnackBar = true
          return
        }
        this.deleteGroupDialog = true
      },
      // 播放节目源
      prePlaySource: function () {
        if (this.selectedPartition.length === 0 && this.selectedGroups.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.selectZoneOrGroup')
          this.commonErrorSnackBar = true
          return
        }
        // 暂时只设定为支持播放歌曲 2020.10.23 支持音频采集卡
        if (this.currentSong === null && this.currentAudioCollectorChannelName === null) {
          this.errorMessages = this.$t('dashboard.messages.selectSourceFromList')
          this.commonErrorSnackBar = true
          return
        }
        //20230526 判断选中的分区是否包含音频协处理器-解码器，如果存在，需要用户确认
        let found_audioMixer = this.selectedPartition.some(zone => this.$ws.getZoneByZoneMac(zone).device_model === deviceModelEnum.NetAudioMixerDecoder)
        if(!found_audioMixer) {
          this.ConfirmPlaySource()
        }
        else {
          this.applySelectedZonesContainsAudioMixer=true
        }
      },
      ConfirmPlaySource: function () {
        this.applySelectedZonesContainsAudioMixer=false
        let sendData
        if (this.currentSong !== null) {
          sendData = {
            uuid: this.uuid,
            command: 'play_source',
            selected_id: this.$store.state.selectIdTime,
            source: 1,
            list_id: this.currentList,
            source_name: this.currentSong,
          }
        } else if (this.currentAudioCollectorChannelName !== null) {
          sendData = {
            uuid: this.uuid,
            command: 'play_audiocollect_source',
            selected_id: this.$store.state.selectIdTime,
            device_mac: this.getMacFromCollectorName(),
            channel: this.getChannelIdOfCollector(),
          }
        }
        // console.log('播放音频采集卡 ' + JSON.stringify(sendData))
        this.$store.commit('WEBSOCKET_SEND', sendData)
      },
      // 停止播放
      preSetIdleStatus: function () {
        if (this.selectedPartition.length === 0 && this.selectedGroups.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.selectZoneOrGroup')
          this.commonErrorSnackBar = true
          return
        }
        const sendData = {
          uuid: this.uuid,
          command: 'set_idle_status',
          selected_id: this.$store.state.selectIdTime,
        }
        this.$store.commit('WEBSOCKET_SEND', sendData)
      },
      /* preSetVolume: function (e) {
        if (this.selectedPartition.length === 0 && this.selectedGroups.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.selectZoneOrGroup')
          this.commonErrorSnackBar = true
        }
      }, */
      getStrLenGbk: function (str) {
        return getStringLenForGbk(str)
      },
      // 获取分区状态
      getStatus: function (source) {
        return this.deviceStatus.get(source + '')
      },
      // 获取分区的颜色
      getZoneColor: function (source, mac) {
        if (source === -1) {
          return '#d3d5d5'
        } else if (this.selectedPartition.indexOf(mac) > -1) {
          return '#90bec8'
        } else {
          return ''
        }
      },
      // 获取音频采集卡的mac
      getMacFromCollectorName: function () {
        let mac = ''
        let audioCollectorIndex = Math.floor(this.currentAudioCollectorChannelIndex/4)
        mac = this.onlineAudioCollectors[audioCollectorIndex].mac
        // console.log('当前音频采集卡的mac是' + mac)
        return mac
      },
      // 获取音频采集卡的channel
      getChannelIdOfCollector: function () {
        return Number(this.currentAudioCollectorChannelIndex%4+1)
      },
      // 启动/停止一键告警
      preStartStopAlarm: function () {
        this.startManualAlarmDialog = true
      },
      // 开启监控rtsp
      openMonitorRtsp () {
        if (this.selectedPartition.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.selectOneZoneForMonitor')
          this.commonErrorSnackBar = true
          return
        } else if (this.selectedPartition.length !== 1) {
          this.errorMessages = this.$t('dashboard.messages.canOnlySelectOneZoneForMonitor')
          this.commonErrorSnackBar = true
          return
        }
        // console.log('开启视频监控')
        const zone = this.$ws.getZoneByZoneMac(this.selectedPartition[0])
        this.$ws.monitorRtsp(1, zone.monitor_mac)
        setTimeout(() => {
          this.monitorDialog = true
        }, 500)
      },
      setAudioMonitorSource: function (isOpen) {
        if (this.audioMonitorDeviceMac === '') {
          this.errorMessages = this.$t('dashboard.messages.noMonitorSpeakerSet')
          this.commonErrorSnackBar = true
          return
        }
        if (this.isAudioMonitorZoneOnline === false) {
          this.errorMessages = this.$t('dashboard.messages.monitorSpeakerOffline')
          this.commonErrorSnackBar = true
          return
        }
        /*关闭*/
        if (!isOpen) {
          this.$ws.audioMonitorSource(0, 0)
          return;
        }

        /*开启*/
        if (this.selectedPartition.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.selectOneZoneForListening');
          this.commonErrorSnackBar = true;
          return;
        } else if (this.selectedPartition.length !== 1) {
          this.errorMessages = this.$t('dashboard.messages.canOnlySelectOneZoneForListening');
          this.commonErrorSnackBar = true;
          return;
        }
        this.$ws.audioMonitorSource(1, this.$store.state.selectIdTime)
      },
      // 监听按钮提示
      getAudioMonitorBtnTips: function (isOpen) {
        const audioMonitorZone = this.$ws.getZoneByZoneMac(this.audioMonitorDeviceMac)
        const template = isOpen ? 'openListeningFor' : 'stopListeningFor'
        return this.$t(`dashboard.tooltips.${template}`, { name: audioMonitorZone.name })
      },
      // 监控按钮提示
      getMonitorBtnTips: function () {
        return this.$t('dashboard.tooltips.openVideoMonitor')
      },

      onSoundCardRecordDialogUpdate(newValue) {
        if (!newValue) {
          this.soundCardRecord.dialog = false; // 确保关闭对话框时设置为 false
        }
      },
      onSoundCardRecordTransmissionModeChange(newValue) {
        // 保存传输模式到本地存储
        localStorage.setItem('soundCardTransmissionMode', this.soundCardRecord.transmissionMode.toString());
        //console.log('保存传输模式到本地存储: ' + this.soundCardRecord.transmissionMode)
      },

      //  开启声卡采集
      preStartSoundCardRecord: function () {
        if (this.selectedPartition.length === 0 && this.selectedGroups.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.selectZoneOrGroup')
          this.commonErrorSnackBar = true
          return
        }
        // 构造要发送的数据
        const postData = {
          command: "get_soundcard_info",
        };
        axios.post(`${this.soundCardRecord.httpBaseUrl}/v1/api`, JSON.stringify(postData), {
        /*
        headers: {
          'Content-Type': 'application/json'
        },
        */
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        },
        cache: 'no-cache',
        timeout: 1000 // 设置超时时间为 1 秒
      })
      .then(response => {
          //  有返回
          if (response.data.result === 0) {
            // 更新数据
            this.soundCardRecord.soundCardInfo.output.isValid = response.data.output.isValid
            this.soundCardRecord.soundCardInfo.output.volume = response.data.output.volume
            this.soundCardRecord.soundCardInfo.input.isValid = response.data.input.isValid
            this.soundCardRecord.soundCardInfo.input.volume = response.data.input.volume
            // 打开对话框

            if( this.soundCardRecord.selectedDevice === 'input') {
                if (this.soundCardRecord.soundCardInfo.input.isValid) {
                    this.soundCardRecord.selectedDevice = 'output'
                }
            }

            // 从本地存储读取传输模式
            const savedTransmissionMode = localStorage.getItem('soundCardTransmissionMode');
            if (savedTransmissionMode) {
              this.soundCardRecord.transmissionMode = parseInt(savedTransmissionMode);
            }

            this.soundCardRecord.dialog = true
          }else {
            this.errorMessages = response.data.msg
            this.commonErrorSnackBar = true
            return
          }
        })
        .catch(error => {
            console.error('请求出错:', error);
            this.errorMessages = this.$t('dashboard.messages.runSoundCardClient')
            this.commonErrorSnackBar = true
            return
        });

      },
      //  关闭声卡采集
      preEndSoundCardRecord: function () {

      },

      // 开始声卡采集
      startSoundCardRecord () {
        //先发送WS指令给服务器，收到应答后再发送指令给声卡采集客户端
        // this.$ws.setZoneVolume(vol)
        this.$ws.requesetWebPaging(true,this.soundCardRecord.deviceVolume,this.soundCardRecord.transmissionMode)
      },
      // 停止声卡采集
      stopSoundCardRecord () {
        this.$ws.requesetWebPaging(false,this.soundCardRecord.deviceVolume,this.soundCardRecord.transmissionMode)
      },

      // 修改声卡采集输出音量
      updateSoundCardOutputVolume: function (vol) {
        this.soundCardRecord.soundCardInfo.output.volume = vol
        //发送给客户端
         // 构造要发送的数据
        const postData = {
          command: "set_soundcard_volume",
          device_type: "output",
          volume: vol
        };
        axios.post(`${this.soundCardRecord.httpBaseUrl}/v1/api`, JSON.stringify(postData), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        },
        cache: 'no-cache',
        timeout: 1000 // 设置超时时间为 1 秒
      })
      .then(response => {
          //  有返回
          if (response.data.result === 0) {
            // 更新数据

          }else {
            this.errorMessages = this.$t('dashboard.messages.setVolumeNotSuccessful')
            this.commonErrorSnackBar = true
            return
          }
        })
        .catch(error => {
            console.error('请求出错:', error);
            this.errorMessages = this.$t('dashboard.messages.runSoundCardClient')
            this.commonErrorSnackBar = true
            return
        });
      },

      // 修改声卡采集输入音量
      updateSoundCardInputVolume: function (vol) {
        this.soundCardRecord.soundCardInfo.input.volume = vol
        //发送给客户端
         // 构造要发送的数据
        const postData = {
          command: "set_soundcard_volume",
          device_type: "input",
          volume: vol
        };
        axios.post(`${this.soundCardRecord.httpBaseUrl}/v1/api`, JSON.stringify(postData), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        },
        cache: 'no-cache',
        timeout: 1000 // 设置超时时间为 1 秒
      })
      .then(response => {
          //  有返回
          if (response.data.result === 0) {
            // 更新数据

          }else {
            this.errorMessages = this.$t('dashboard.messages.setVolumeNotSuccessful')
            this.commonErrorSnackBar = true
            return
          }
        })
        .catch(error => {
            console.error('请求出错:', error);
            this.errorMessages = this.$t('dashboard.messages.runSoundCardClient')
            this.commonErrorSnackBar = true
            return
        });
      },

      // 通知声卡采集客户端开始采集
      NotifySoundCardClientStart: function () {
        //发送给客户端
        // 构造要发送的数据
        const postData = {
          uuid: this.$store.state.uuid,
          command: 'request_paging',
          event: 1,
          paging_src: 1,
          transmission_mode: this.soundCardRecord.transmissionMode,
          volume: this.soundCardRecord.deviceVolume,
          selected_id: this.$store.state.selectIdTime,
          device_type: this.soundCardRecord.selectedDevice
        }

        axios.post(`${this.soundCardRecord.httpBaseUrl}/v1/api`, JSON.stringify(postData), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        },
        cache: 'no-cache',
        timeout: 1000 // 设置超时时间为 1 秒
      })
      .then(response => {
          //  有返回
          if (response.data.result === 0) {
            // 更新数据
            this.successMessages = this.$t('dashboard.messages.startRecording')
            this.commonSuccessSnackBar = true
          }else {
            this.errorMessages = this.$t('dashboard.messages.startRecordingFailed')
            this.commonErrorSnackBar = true
            return
          }
        })
        .catch(error => {
            console.error('请求出错:', error);
            this.errorMessages = this.$t('dashboard.messages.runSoundCardClient')
            this.commonErrorSnackBar = true
            return
        });
      },

      updateSoundCardDeviceVolume: function (vol) {
        this.soundCardRecord.deviceVolume = vol
        // 设置音量
        this.$ws.setZoneVolume(vol)
      },

      // TTS语音模块
      preTextToSpeech: function () {
        this.uploadTtsFile = null
        this.ttsDialog = true
        this.startTTSLoading = false
      },
      // 加载并读取tts文件
      readTtsFile: function () {
        if (this.uploadTtsFile == null) {
          this.errorMessages = this.$t('dashboard.messages.noTxtFileSelected')
          this.commonErrorSnackBar = true
          return
        }
        const file = this.uploadTtsFile
        const reader = new FileReader()
        reader.onload = () => {
          this.ttsContent = reader.result
        }
        reader.readAsText(file)
        this.successMessages = this.$t('dashboard.messages.loadTxtFileSuccess')
        this.commonSuccessSnackBar = true
      },
      // 开始合成
      startTTS: function () {
        if (this.ttsContent == null || this.ttsContent.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.textContentEmpty')
          this.commonErrorSnackBar = true
          return
        }
        if (this.ttsFileName == null || this.ttsFileName.length === 0) {
          this.errorMessages = this.$t('dashboard.messages.audioFileNameEmpty')
          this.commonErrorSnackBar = true
          return
        }
        // 输入字符限制校验
        if (!this.ttsForm) {
          return
        }
        let voiceName
        switch (this.ttsSound) {
          case 0: voiceName = 'xiaofeng'; break
          case 1: voiceName = 'xiaoyan'; break
          default: break
        }
        this.startTTSLoading=true
        this.$ws.textToSpeech(this.ttsVolume, this.ttsPitch, this.ttsSpeed, voiceName, this.ttsFileName, this.ttsContent, this.ttsRdn)
      },
      // 分区在线且绑定的摄像头处于已登录状态时可点亮
      checkIfZoneHasOnlineMonitor (mac) {
        if (this.monitors.length === 0 || mac == null || mac === '') {
          return false
        }
        let isZoneHasOnlineMonitor = false
        this.monitors.forEach(monitor => {
          if (monitor.monitor_mac === mac && (monitor.monitor_status === 0 || monitor.monitor_status === 2)) {
            isZoneHasOnlineMonitor = true
          }
        })
        return isZoneHasOnlineMonitor
      },
      checkIfZoneHasOnlineMonitorByZone (zone) {
        return this.checkIfZoneHasOnlineMonitor(zone.monitor_mac)
      },
      getMonitorNameByMac (mac) {
        if (this.monitors.length === 0 || mac === '') {
          return ''
        }
        let monitorName = ''
        this.monitors.forEach(monitor => {
          if (monitor.monitor_mac === mac) {
            monitorName = monitor.name
          }
        })
        return monitorName
      },
      getMonitorMacByName (name) {
        if (this.monitors.length === 0 || name === '') {
          return
        }
        let monitorMac = ''
        this.monitors.forEach(monitor => {
          if (monitor.name === name) {
            monitorMac = monitor.monitor_mac
          }
        })
        return monitorMac
      },
      // 设置今日定时点不同状态的颜色显示
      getColorOfTimerStatus (status) {
        if (status === 1) {
          return 'grey'
        }
        if (status === 2) {
          return 'primary'
        }
        return 'blue'
      },
      // 今日定时点状态描述
      getTimerStatusDescription (item) {
        if (item.status === 1) {
          return this.$t('dashboard.status.executed')
        }
        if (item.status === 2) {
          return this.$t('dashboard.status.executing')
        }
        // 计算离开始执行的时间
        const time_start = new Date();
        const time_end = new Date();
        const value_start = this.timestamp.substring(this.timestamp.length - 8, this.timestamp.length).split(":")
        const value_end = item.start_time.split(":")
        time_start.setHours(value_start[0], value_start[1], value_start[2], 0)
        time_end.setHours(value_end[0], value_end[1], value_end[2], 0)
        const time = new Date(time_end - time_start).toISOString().slice(11, -1).substr(0, 8)
        return this.$t('dashboard.tooltips.executeAfterTime', { time })
        // todo 检查到点执行，是否更新列表
      },
      // 今日定时点状态描述
      getManualTaskStatusDescription (status) {
        // 播放
        if (status === 1) {
          return this.$t('dashboard.status.playing')
        }
        // 暂停
        if (status === 2) {
          return this.$t('dashboard.status.paused')
        }
        // 停止
        return this.$t('dashboard.status.stopped')
      },
      // 获取分区不同状态的图标
      getIconForZoneStatus(source, playStatus) {
        // 如果是网络点播且播放状态为暂停，显示暂停图标
        if (source === 2 && playStatus === 2) {
          return 'mdi-pause'
        }
        let icon = devicePlayStatusIcon.get(source + '')
        // 如果找不到图标，默认使用网络点播的图标
        return icon == null ? 'mdi-music-clef-treble' : icon
      },
      // 音源列表不存在歌曲使用红色字体表示
      songStyleInPlaylist(alive) {
        if (alive) {
          return {}
        }
        return {
          color: 'red',
        }
      },
      // 获取音源列表歌曲提示（歌名+时长）, html格式，需使用v-html消费
      getSongNameAndDurationForSongTooltip(song) {
        const songDurationNumber = song.duration != null ? song.duration : 0
        let duration = new Date(songDurationNumber * 1000).toISOString();
        if (duration.substr(11, 2) !== '00') {
          // 超过1h的歌曲，显示小时
          duration = duration.substr(11, 8)
        } else {
          // 不超过1h的歌曲，不显示小时
          duration = duration.substr(14, 5)
        }
        return song.song_name + '</br>' + this.$t('dashboard.tooltips.songDuration', { duration })
      },
      // 获取音源列表列表提示（列表名+歌曲数）, html格式，需使用v-html消费
      getListNameAndLengthTooltip(list) {
        if (list.list_account == null || list.list_account.trim() === '') {
          return list.list_name + '</br>' + this.$t('dashboard.tooltips.listInfo', { count: list.songs.length });
        }
        return list.list_name + '</br>' + this.$t('dashboard.tooltips.userListInfo', { user: list.list_account, count: list.songs.length });
      },
      // 系统注册
      systemRegister () {
        if (!this.registrationCode || this.registrationCode === '') {
          this.errorMessages = this.$t('dashboard.messages.registrationCodeEmpty')
          this.commonErrorSnackBar = true
          return
        }
        this.$ws.systemRegister(this.registrationCode);
      },
      // tts滑动模块相关
      minusTtsVolume() {
        this.ttsVolume = (this.ttsVolume - 10) >= 0 ? (this.ttsVolume - 10) : 0
      },
      minusTtsPitch() {
        this.ttsPitch = (this.ttsPitch - 1) >= 0 ? (this.ttsPitch - 1) : 0
      },
      minusTtsSpeed() {
        this.ttsSpeed = (this.ttsSpeed - 1) >= 0 ? (this.ttsSpeed - 1) : 0
      },
      plusTtsVolume() {
        this.ttsVolume = (this.ttsVolume + 10) <= 100 ? (this.ttsVolume + 10) : 100
      },
      plusTtsPitch() {
        this.ttsPitch = (this.ttsPitch + 1) <= 100 ? (this.ttsPitch + 1) : 100
      },
      plusTtsSpeed() {
        this.ttsSpeed = (this.ttsSpeed + 1) <= 100 ? (this.ttsSpeed + 1) : 100
      },
      // getTtsWordClarification(type, higherWord) {
      //   return type + '</br>' + '(数字越大' + type + '越'+ higherWord + ')'
      // }
      resetTtsOption() {
        this.ttsVolume = 100
        this.ttsPitch = 50
        this.ttsSpeed = 50
      },
      // 应用播放模式到所有手动任务
      applyPlayModeForAllManualTask() {
        this.$ws.setTaskPlayMode(null, this.playMode, true)
      },
      // 启动/停止一键告警
      startManualAlarmEvent() {
        this.$ws.startManualAlarm(0)
      }
    }
    /** **************封装******************/
  }
</script>
