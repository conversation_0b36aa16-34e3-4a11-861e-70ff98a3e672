<template>
  <v-app class="mt-n10">
    <v-container
      class="fill-height text-center"
      fluid
    >
      <v-row
        align="center"
        justify="center"
      >
        <v-col cols="12">
          <h1 class="text-h1" style="color: #2E2E2E;">{{ $t('information.title') }}</h1>
          <v-btn
            elevation="0"
            class="text-h2 v-btn--outlined mt-10 ml-n10"
            style="color: red;"
            @click="openInformationApp"
            >
              {{ $t('information.openClient') }}
          </v-btn>
        </v-col>
      </v-row>
    </v-container>
  </v-app>
</template>

<script>
    export default {
      name: "Information",
      methods: {
        openInformationApp() {
          window.open('xdsc://');
        }
      },
    }

</script>

<style scoped>
  .primary--text {
    color: #1867c0 !important;
    caret-color: #1867c0 !important;
  }
</style>
