import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import * as ws from './plugins/websocket'
import * as utils from './plugins/utils'
import i18n from './i18n'

Vue.use(Vuex)

// 存储默认的vuex state数据
const getDefaultState = () => {
  return {
    drawerSelectedItemName: i18n.t('routes.controlCenter'),
    // refactor error message
    apiName: i18n.t('store.operation'), // 默认提示'操作成功'/'操作失败'
    commandName: null, //
    // end
    // ****************** new for login data
    wsStatus: null,
    loginResult: null, // 登录结果
    noLoginRedirect: false, // 未登录进行跳转
    uuid: null, // 登录uuid
    user: null, // 用户名
    user_uuid: null, // 用户uuid
    serverId: null, // 服务器id
    cloud_control_switch: false, // 云控制
    cloud_isConnected: false, // 云控制是否连接
    cloud_control_valid: false, // 云控制是否有效
    pwd: null, // 密码
    type: null, // 用户类型 1为试用版，2为正式版
    expiration_date: null, // 软件授权到期时间
    authority: null, // 用户权限
    user_type: null, // 用户类型
    systemType: null, // 系统类型 1为Linux  2为Windows
    machineCode: null, // 注册机器码
    cloudControlMachineCode: null, // 云控制机器码
    mac: null, // 系统MAC
    ip: null, // 系统ip
    statusCode: null, // 重登状态码
    isMiniDrawer: false, // 左侧导航栏是否为小图标显示
    // ****************** new for real data
    fileUpdate: {}, // 监控文件更新
    selectIdTime: 0, // 选中分区的次数，每次选中+1
    uploadSongPath: null, // 上传歌曲的绝对路径
    uploadPath: null, // 上传歌曲的相对路径
    uploadSongResult: false, // 上传歌曲结果
    uploadSongProgress: 0, // 上传歌曲进度
    uploadSongArrayProgress: [], // 上传歌曲进度数组
    uploadFirmwareProgress: 0, // 上传固件进度
    uploadFirmwareResult: null, // 上传固件结果
    // volume: 50, // 音量
    serverPlayMode: 1, // 播放模式
    createNewGroupResult: null, // 新建分组结果
    editGroupResult: null, // 编辑分组结果
    setGroupZoneResult: null, // 设置分区分组结果
    removeGroupResult: null, // 移除分组结果
    playSourceResult: null, // 播放节目源结果，0为成功，非null为失败
    playCollectResult: null, // 音频采集结果，0为成功，非null为失败
    setVolumeResult: null, // 调节音量结果，0为成功，非null为失败
    requestPagingResult: null, // 请求网页寻呼结果
    requestPagingEvent: null, //请求网页寻呼事件
    setIdleStatusResult: null, // 停止播放结果，0为成功，非null为失败
    playModeResult: null, // 设置播放模式结果，0为成功，非null为失败
    removeSongFromListResult: null, // 移除列表歌曲结果，0为成功，非null为失败
    auditSongForMediaResult: null, // 审核曲库歌曲，0为成功，非null为失败
    // ****************** end new for real data
    // ****************** new for real device setting data
    setDeviceInfoResult: null, // 修改设备名称结果 -- 区分于监控绑定
    setMonitorBindingResult: null, // 监控绑定结果
    upgradeDeviceResult: null, // 请求升级设备结果
    getDeviceIpInfo: {}, // 查询设置ip结果
    setDeviceIpResult: null, // 设置设备ip结果
    resetDeviceDataResult: null, // 重置设备数据结果
    rebootDeviceResult: null, // 重启设备结果
    fireCollectorInfo: [], // 消防采集器详细信息
    audioCollectorInfo: [], // 音频采集器详细信息
    // ****************** end for real device setting data
    // ****************** new for real device update data
    existFirmwareCount: 0, // 服务器已有固件数量
    existFirmwareNames: [], // 服务器已有固件名称
    deviceUpgradeProgress: 0, // 升级进度百分比
    upgradeStatus: 0, // 更新状态：1为开始下载，2为已是最新版本，3为连接服务器超时，4为下载失败，5为下载完成，正在启动
    // ****************** 定时相关
    addSchemaResult: null, // 增加定时方案结果
    editSchemaResult: null, // 编辑定时方案结果
    copySchemaResult: null, // 复制定时方案结果
    copyTimerResult: null, // 复制定时点结果
    sortTimerResult: null, // 定时点排序结果
    deleteSchemaResult: null, // 删除定时方案结果
    addTimePointResult: null, // 增加定时点结果
    editTimerResult: null, // 编辑定时点结果
    deleteTimerResult: null, // 删除定时点结果
    setActiveSchemaResult: null, // 删除定时点结果
    setTimePointValidResult: null, // 更新定时点状态结果
    // ****************** 定时相关
    // ****************** 系统维护相关
    resetDataResult: null, // 数据重置结果
    rebootResult: null, // 服务器重启结果
    resetDefaultResult: null, // 恢复出厂设置结果
    rebootBySelf: false, // 主动重连
    logoutBySelf: false, // 主动回到登录界面，如果为true，则不触发断线重连  目前实现:如果要退回主界面(push('/')),需要手动设置该值为true todo enhance
    uploadServePackageProcess: 0, // 上传固件进度
    uploadServePackageResult: null, // 上传固件结果
    upgradeServeResult: null, // 服务器升级结果
    version: null, // 服务器版本
    systemBackupNames: [], // 系统配置文件名称数组
    uploadBackupProcess: 0, // 上传备份进度
    uploadBackupResult: null, // 上传备份结果
    backupServerResult: null, // 服务器备份结果
    removeBackupFileResult: null, // 删除配置文件结果
    restoreServerDataResult: null, // 服务器还原数据结果
    // ****************** 系统维护相关
    // ****************** 系统设置相关
    systemDateTime: null, // 当前系统日期时间
    systemBootTime: null, // 系统启动日期时间
    systemNetwork: null, // 系统网络配置
    systemStorage: null, // 系统存储信息
    setSystemNetworkResult: null,
    setSystemDateTimeResult: null,
    // ****************** 系统设置相关
    // ****************** 账户管理相关
    userInfoData: null, // 用户信息
    userAccountSendRequest: [], //用户请求标志（避免大批量账户时重复请求）
    zonesMacObject: Object.create(null), // 用户分区mac对象
    zonesMacKeyAndAccountValueObject: Object.create(null), // mac-用户对象(单个mac为key，value为数组（可能有多个用户拥有））
    addUserResult: null,
    deleteUserResult: null, // 移除用户结果
    editUserResult: null, // 编辑用户结果
    // ****************** 账户管理相关
    // ****************** 监听相关
    audioMonitorDeviceMac: null, // 监听的设备mac
    openAudioMonitorResult: null, // 开启监听结果
    setAudioMonitorSpeakerResult: null, // 设置监听音箱结果
    // ****************** 监听相关
    setFireChannelResult: null, // 设置消防通道结果
    textToSpeechResult: null, // TTS结果
    queryBluetoothResult: null,
    setBluetoothResult: null,
    bluetoothName: null,
    bluetoothEncryption: null,
    bluetoothPin: null,
    // ****************** 音效相关
    queryEffectResult: null,
    setEffectResult: null,
    effectGainInfo: null, // 查询设置ip结果
    // ****************** 日志相关
    exportRunLogResult: null,
    runLogPath: null, // 运行日志导出路径
    // 通话录音相关
    callRecordSwitchResult: null, // 查询/设置自动通话录音开关结果
    callRecordSwitch: false, // 自动通话录音开关状态
    queryCallLogResult: null, // 查询录音日志结果
    callLogs: [], // 录音日志列表
    // ****************** 日志相关
// ****************** end for real device update data
    barColor: 'rgba(0, 0, 0, .8), rgba(0, 0, 0, .8)',
    barImage: 'sidebar1.jpg',
    showBarImage: true,
    drawer: null,
    event: null,
    websocket: null,
    // todo 修改变量名，此变量为存储的分区信息
    eventList: [],
    newEventList: [],
    needRefreshZoneList: false, // 当收个单个分区的修改时，统一发生变更
    groupList: [],
    playList: [],
    // 定时方案
    timerList: [],
    // 定时点
    timers: [],
    timestamp: '',
    selectedPartition: [], // 2021.1.10修正： 存储分区mac而非id
    selectedGroups: [],
    selectSong: null,
    currentSchemaId: null,
    todayTimerList: [], // 今日定时点
    // 统一错误处理
    errorId: null,
    errorWsMessage: i18n.t('store.operationFailed'),
    errorDuration: 1500,
    // 监控相关
    monitors: [],
    setMonitorInfoResult: null,
    addCustomMonitorResult: null,
    delMonitorInfoResult: null,
    serverRegisterResult: null,
    isCloudServer: false,
    // 分区自定义排序
    sortDeviceCustomResult: null,
    zoneMacSelectedForSort: null,
    songMd5SelectedForSort: null, // 歌曲排序
    // 删除设备
    deleteDeviceResult: null,
    // 手动任务
    manualTaskInfo: [],
    setTaskPlayModeResult: null,
    setTaskPlayStatusResult: null,
    setTaskPlayPreNextResult: null,
    // 本地歌曲
    localSongListInfo: [],
    // 媒体管理
    addSongListResult: null,
    renameSongListResult: null,
    removeSongListResult: null,
    addSongToListResult: null,
    sortSongListResult: null,
    // 取消恢复单个今日定时点
    singleCancelTimePointResult: null,
    singleRestartTimePointResult: null,
    // 电源管理相关
    sequencePowerInfo: [],
    setSequencePowerDeviceResult: null,
    // 网络模式
    queryDeviceNetworkModeResult: null,
    setDeviceNetworkModeResult: null,
    singleDeviceNetworkMode: null,
    singleDeviceNetworkServerIp: null,
    singleDeviceNetworkServerPort: null,
    singleDeviceNetworkServerIp2: null,
    singleDeviceNetworkServerPort2: null,

    //SIP信息
    queryDeviceSipInfoResult: null,
    setDeviceSipInfoResult: null,
    singleDeviceSipEnable: null,
    singleDeviceSipProtocol: null,
    singleDeviceSipOutPutVolume: null,
    singleDeviceSipStatus: null,
    singleDeviceSipServerIp: null,
    singleDeviceSipServerPort: null,
    singleDeviceSipAccount: null,
    singleDeviceSipPassword: null,

    //信息发布
    queryDeviceInformationPubResult: null,
    setDeviceInformationPubResult: null,
    singleDeviceInformationPubDisplayEnable: null,
    singleDeviceInformationPubText: null,
    singleDeviceInformationEffects: null,
    singleDeviceInformationSpeed: null,
    singleDeviceInformationStayTime: null,

    // 子音量
    setSubVolumeResult: null,
    subVolumeFromServer: null,
    auxVolumeFromServer: null,
    // 视频监控功能
    isEnableMonitor: false,
    switchMonitorResult: null,
    switchMonitorType: null,
    // 任务管理
    remoteControllerInfos: [], // 包含任务tasks和按键keys
    setRemoteControllerTaskResult: null,
    setRemoteControllerKeyResult: null,

    // 手动告警
    startManualAlarmResult: null,
    startManualAlarmStatus: null,

    // 对讲设置
    intercomSetResult: null,
    intercomQueryResult: null,
    intercomDeviceMac: null,
    intercomKey1Mac: null,
    intercomKey2Mac: null,
    intercomMicVolume: null,
    intercomFarOutPutVolume: null,

    // 触发设置
    triggerSwitch: null,
    triggerMode: null,
    triggerSongPathName: null,
    triggerVolume: null,
    setTriggerResult: null,
    queryTriggerResult: null,
    triggerDeviceMac: null,

    // 考试模式
    isEnableExaminationMode: false,

    // 存储空间设置
    setUserStorageCapacityResult: false,
    setUserAuthorityResult: false,

    // 主备服务器设置
    isEnableServerSync: null,
    isBackupServer: null,
    server_sync_dest_ip: null,
    server_sync_status: null,
    setBackupServerResult: null,

    //采播设置
    setAudioCollectorParmResult: null,
    queryAudioCollectorParmResult: null,
    audioCollectorPriority: null,          //音源优先级：1-默认，低于定时音源 2-高优先级，高于定时音源
    audioCollectorTriggerSwitch: null,      //触发主开关：0-关闭，1-开启
    audioCollectorTriggerChannelId: null,   //触发通道Id（1~4），数值越大，优先级越高
    audioCollectorTriggerZoneVolume: null,  //触发分区音量(0~100,255)
    audioCollectorDeviceMac: null,
    audioCollectorTriggerZoneMacs: null,

    // 混音设置
    setAudioMixerParmResult: null,
    queryAudioMixerParmResult: null,
    audioMixerMasterSwitch: null,  //混音主开关：0-关闭，1-开启
    audioMixerPriority: null,      //混音器优先级（1~9），数值越大，优先级越高
    audioMixerTriggerType: null,   //触发类型（1~3），1-混合触发，2-MIC，3-AUX，默认为1
    audioMixerVolumeFadeLevel: null, //存在MIC信号时，其他信号的淡化级别（0~9）
    audioMixerZoneVolume: null,  //触发分区音量（0~100,255）
    audioMixerDeviceMac: null,
    audioMixerZoneMacs: null,

    // 电话网关设置
    setPhoneGatewayParmResult: null,
    queryPhoneGatewayParmResult: null,
    phoneGatewayMasterSwitch: null,  //网关主开关：0-关闭，1-开启
    phoneGatewayZoneVolume: null,  //触发分区音量（0~100,255）
    phoneGatewayTelWhitelist: null, //电话网关白名单
    phoneGatewayDeviceMac: null,
    phoneGatewayZoneMacs: null,

    // 功放控制器设置
    queryAmpControlerParmResult: null,
    ampControlerMasterStatus: null,
    ampControlerBackupStatus: null,
    ampControlerBackupId: null,
    ampControlerDeviceMac: null,

    // 噪声自适应器设置
    queryNoiseDetectorParmResult: null, // 查询结果
    noiseDetectorSwitch: false, // 开关
    noiseDetectorDeviceMac: null, // 设备mac
    noiseDetectorChannelArray: [], // 通道数组
    noiseDetectorVolumeArray: [], // 音量数组
    noiseDetectorZoneMacs: null, // 关联分区
    setNoiseDetectorParmResult: null, // 设置结果
    
    // 电台管理相关
    radioGroupListResult: null, // 获取电台分组结果
    radioDetailsResult: null, // 获取电台详情结果
    radioGroupList: [], // 电台分组列表
    radioList: [], // 电台列表
    playRadioResult: null, // 播放电台结果
    stopRadioResult: null, // 停止电台结果
    addRadioResult: null, // 添加电台结果
    editRadioResult: null, // 编辑电台结果
    deleteRadioResult: null // 删除电台结果
  }
}

// 定义websocket交互错误代码
const ERROR_UNKNOWN_MISTAKE = () => i18n.t('store.errors.unknownMistake')
const ERROR_NORMAL_OPERATION = () => i18n.t('store.errors.normalOperation')
const ERROR_INADEQUATE_USER_RIGHTS = () => i18n.t('store.errors.inadequateUserRights')
const ERROR_USER_ALREADY_EXISTS = () => i18n.t('store.errors.userAlreadyExists')
const ERROR_USER_DOES_NOT_EXIST = () => i18n.t('store.errors.userDoesNotExist')
const ERROR_USER_PASSWORD_ERROR = () => i18n.t('store.errors.userPasswordError')
const ERROR_USER_NOT_LOGGED_IN = () => i18n.t('store.errors.userNotLoggedIn')
const ERROR_USER_IS_LOGGED_IN = () => i18n.t('store.errors.userIsLoggedIn')
const ERROR_TARGET_DOES_NOT_EXIST = () => i18n.t('store.errors.targetDoesNotExist')
const ERROR_TARGET_DOES_EXIST = () => i18n.t('store.errors.targetDoesExist')
const ERROR_THE_NUMBER_OF_TARGETS_DOES_NOT_MATCH = () => i18n.t('store.errors.numberOfTargetsDoesNotMatch')
const ERROR_TARGET_FORMAT_DOES_NOT_MATCH = () => i18n.t('store.errors.targetFormatDoesNotMatch')
const ERROR_TARGET_PARAMETER_DOES_NOT_MATCH = () => i18n.t('store.errors.targetParameterDoesNotMatch')
const ERROR_BUSY_TARGET = () => i18n.t('store.errors.busyTarget')
const ERROR_GOAL_OFFLINE = () => i18n.t('store.errors.goalOffline')
const ERROR_CHECK_THE_PARTITION_ID_DOES_NOT_MATCH = () => i18n.t('store.errors.partitionIdDoesNotMatch')
// const ERROR_CHECK_THE_PARTITION_ID_DOES_NOT_MATCH = () => i18n.t('store.errors.pleaseSelectPartition')
const ERROR_QUANTITATIVE_LIMIT = () => i18n.t('store.errors.quantitativeLimit')
const ERROR_SIP_BUSY = () => i18n.t('store.errors.sipBusy')
const ERROR_SIP_NUMBER_OFFLINE = () => i18n.t('store.errors.sipNumberOffline')
const ERROR_SIP_UNKNOWN_ERROR = () => i18n.t('store.errors.sipUnknownError')
const ERROR_SIP_ACCOUNT_DOES_NOT_EXIST = () => i18n.t('store.errors.sipAccountDoesNotExist')
const ERROR_SIP_DIALING_RULES_DO_NOT_EXIST = () => i18n.t('store.errors.sipDialingRulesDoNotExist')
const ERROR_SIP_DIALING_RULES_ARE_NOT_UNIFORM = () => i18n.t('store.errors.sipDialingRulesAreNotUniform')
// 定时点相关errorCode
const ERROR_TIMING_SCHEME_ID_ERROR = () => i18n.t('store.errors.timingSchemeIdError')
const ERROR_TIMING_POINT_ID_ERROR = () => i18n.t('store.errors.timingPointIdError')
const ERROR_TIMING_SCHEME_NAME_IS_INCORRECT = () => i18n.t('store.errors.timingSchemeNameIncorrect')
const ERROR_TIMING_SCHEME_POINT_NAME_IS_INCORRECT = () => i18n.t('store.errors.timingPointNameIncorrect')
const ERROR_TIMING_POINT_NAME_ERROR = () => i18n.t('store.errors.timingPointNameError')
const ERROR_THE_SPECIFIED_DATE_IS_EARLIER_THAN_TODAY = () => i18n.t('store.errors.specifiedDateEarlierThanToday')
const ERROR_START_DATE_IS_LATER_THAN_END_DATE = () => i18n.t('store.errors.startDateLaterThanEndDate')
const ERROR_START_DATE_IS_TODAY_AND_START_TIME_IS_EARLIER_THAN_NOW = () => i18n.t('store.errors.startDateTodayStartTimeEarlier')
const ERROR_START_TIME_IS_LATER_THAN_END_TIME = () => i18n.t('store.errors.startTimeLaterThanEndTime')
const ERROR_NO_EQUIPMENT_SELECTED = () => i18n.t('store.errors.noEquipmentSelected')
const ERROR_NO_SONG_SELECTED = () => i18n.t('store.errors.noSongSelected')
const ERROR_TIMING_POINT_EXCEEDS_THE_NUMBER_LIMIT = () => i18n.t('store.errors.timingPointExceedsLimit')
const ERROR_TIMING_SCHEME_EXCEEDS_QUANTITY_LIMIT = () => i18n.t('store.errors.timingSchemeExceedsLimit')
const ERROR_WAITING_TO_RECEIVE_PARTITION_PACKET_DATA = () => i18n.t('store.errors.waitingToReceivePartitionData')
const ERROR_UPGRADE_FILE_DOES_NOT_EXIST = () => i18n.t('store.errors.upgradeFileDoesNotExist')
// TTS
const ERROR_SYNTHETIC_PARAMETER_ERROR = () => i18n.t('store.errors.syntheticParameterError')
const ERROR_THE_SPEAKER_DOES_NOT_EXIST = () => i18n.t('store.errors.speakerDoesNotExist')
const ERROR_SYNTHETIC_TEXT_IS_TOO_LONG = () => i18n.t('store.errors.syntheticTextTooLong')
const ERROR_SYNTHESIS_FAILURE = () => i18n.t('store.errors.synthesisFailure')
const ERROR_AUDIO_IS_TOO_SHORT = () => i18n.t('store.errors.audioTooShort')
// LOG
const ERROR_WORKING_MODE_DOES_NOT_MATCH = () => i18n.t('store.errors.workingModeDoesNotMatch')
const ERROR_THE_CURRENT_SOURCE_CANNOT_BE_MONITORED = () => i18n.t('store.errors.currentSourceCannotBeMonitored')
const ERROR_DEVICE_DOES_NOT_EXIST = () => i18n.t('store.errors.deviceDoesNotExist')
const ERROR_DEVICE_OFFLINE = () => i18n.t('store.errors.deviceOffline')
const ERROR_LOG_FILE_DOWNLOAD = () => i18n.t('store.errors.logFileDownload')
const ERROR_LOG_FILE_DOES_NOT_EXIST = () => i18n.t('store.errors.logFileDoesNotExist')
// Other
var ERROR_REGISTER_FAIXL = () => i18n.t('store.errors.registerFail')
var ERROR_OPERATION_TIMEOUT = () => i18n.t('store.errors.operationTimeout')
var ERROR_NETWORK_NOT_MATCH = () => i18n.t('store.errors.networkNotMatch')

const ERROR_TIMING_POINT_CONFLICT = () => i18n.t('store.errors.timingPointConflict')

const ERROR_MANUAL_ALARM_SONG_NOT_EXIST = () => i18n.t('store.errors.manualAlarmSongNotExist')

const ERROR_EXIST_SUB_USER_CAN_NOT_DELETED = () => i18n.t('store.errors.existSubUserCanNotDeleted')
const ERROR_EXCEEDED_STORAGE_CAPACITY = () => i18n.t('store.errors.exceededStorageCapacity')
const ERROR_DEVICE_NOT_SUPPORT_FUNCTION = () => i18n.t('store.errors.deviceNotSupportFunction')

// 用户权限划分
export var USER_LIMITS_NONE = 0              // 无权限
export var USER_LIMITS_PLAYLIST = 1          // 播放列表管理
export var USER_LIMITS_TIMER = 2             // 定时管理
export var USER_LIMITS_AUDIO_COLLECTOR = 4   // 使用音频采集器
export var USER_LIMITS_REPEATED_LOGIN = 8   // 重复登录
export var USER_LIMITS_CREATE_SUB_USER = 16   // 创建子用户
export var USER_LIMITS_ALL = 65535           // 全部权限

const errorCode = new Map([
  [-1, ERROR_UNKNOWN_MISTAKE],
  [0, ERROR_NORMAL_OPERATION],
  [1, ERROR_INADEQUATE_USER_RIGHTS],
  [2, ERROR_USER_ALREADY_EXISTS],
  [3, ERROR_USER_DOES_NOT_EXIST],
  [4, ERROR_USER_PASSWORD_ERROR],
  [5, ERROR_USER_NOT_LOGGED_IN],
  [6, ERROR_USER_IS_LOGGED_IN],
  [7, ERROR_TARGET_DOES_NOT_EXIST],
  [8, ERROR_TARGET_DOES_EXIST],
  [9, ERROR_THE_NUMBER_OF_TARGETS_DOES_NOT_MATCH],
  [10, ERROR_TARGET_FORMAT_DOES_NOT_MATCH],
  [11, ERROR_TARGET_PARAMETER_DOES_NOT_MATCH],
  [12, ERROR_BUSY_TARGET],
  [13, ERROR_GOAL_OFFLINE],
  [14, ERROR_CHECK_THE_PARTITION_ID_DOES_NOT_MATCH],
  [15, ERROR_QUANTITATIVE_LIMIT],
  [16, ERROR_SIP_BUSY],
  [17, ERROR_SIP_NUMBER_OFFLINE],
  [18, ERROR_SIP_UNKNOWN_ERROR],
  [19, ERROR_SIP_ACCOUNT_DOES_NOT_EXIST],
  [20, ERROR_SIP_DIALING_RULES_DO_NOT_EXIST],
  [21, ERROR_SIP_DIALING_RULES_ARE_NOT_UNIFORM],
  [22, ERROR_TIMING_SCHEME_ID_ERROR],
  [23, ERROR_TIMING_POINT_ID_ERROR],
  [24, ERROR_TIMING_SCHEME_NAME_IS_INCORRECT],
  [25, ERROR_TIMING_SCHEME_POINT_NAME_IS_INCORRECT],
  [26, ERROR_TIMING_POINT_NAME_ERROR],
  [27, ERROR_THE_SPECIFIED_DATE_IS_EARLIER_THAN_TODAY],
  [28, ERROR_START_DATE_IS_LATER_THAN_END_DATE],
  [29, ERROR_START_DATE_IS_TODAY_AND_START_TIME_IS_EARLIER_THAN_NOW],
  [30, ERROR_START_TIME_IS_LATER_THAN_END_TIME],
  [31, ERROR_NO_EQUIPMENT_SELECTED],
  [32, ERROR_NO_SONG_SELECTED],
  [33, ERROR_TIMING_POINT_EXCEEDS_THE_NUMBER_LIMIT],
  [34, ERROR_TIMING_SCHEME_EXCEEDS_QUANTITY_LIMIT],
  [35, ERROR_WAITING_TO_RECEIVE_PARTITION_PACKET_DATA],
  [36, ERROR_UPGRADE_FILE_DOES_NOT_EXIST],
  [37, ERROR_SYNTHETIC_PARAMETER_ERROR],
  [38, ERROR_THE_SPEAKER_DOES_NOT_EXIST],
  [39, ERROR_SYNTHETIC_TEXT_IS_TOO_LONG],
  [40, ERROR_SYNTHESIS_FAILURE],
  [41, ERROR_AUDIO_IS_TOO_SHORT],
  [42, ERROR_WORKING_MODE_DOES_NOT_MATCH],
  [43, ERROR_THE_CURRENT_SOURCE_CANNOT_BE_MONITORED],
  [44, ERROR_DEVICE_DOES_NOT_EXIST],
  [45, ERROR_DEVICE_OFFLINE],
  [46, ERROR_LOG_FILE_DOWNLOAD],
  [47, ERROR_LOG_FILE_DOES_NOT_EXIST],
  [48, ERROR_REGISTER_FAIXL],
  [49, ERROR_OPERATION_TIMEOUT],
  [50, ERROR_NETWORK_NOT_MATCH],
  [51, ERROR_TIMING_POINT_CONFLICT],
  [52, ERROR_MANUAL_ALARM_SONG_NOT_EXIST],
  [53, ERROR_EXIST_SUB_USER_CAN_NOT_DELETED],
  [54, ERROR_EXCEEDED_STORAGE_CAPACITY],
  [55, ERROR_DEVICE_NOT_SUPPORT_FUNCTION]
])


//20220302 增加设备型号枚举
export const deviceModelEnum = {
  NetSpeakerA: 5,
  NetSpeakerB: 6,
  NetSpeakerC: 7,
  NetSpeakerD: 10,
  NetSpeakerE: 16,
  NetPagerA: 3,
  NetPagerB: 4,
  NetPagerC: 20,
  NetFireCollectorA: 8,
  NetAudioCollectorA: 9,
  NetSequencePowerA: 11,
  NetFireCollectorB: 12,
  NetAudioCollectorB: 13,
  NetSequencePowerB: 14,
  NetRemoteControler: 17,
  NetAudioMixerDecoder: 18,
  NetAudioMixerEncoder: 19,
  NetPhoneGateway: 21,

  NetFireCollectorC: 22,
  NetAudioCollectorC: 23,
  NetSequencePowerC: 24,
  NetRemoteControlerC: 25,
  NetAudioMixerDecoderC: 26,
  NetAudioMixerEncoderC: 27,

  NetSpeakerF: 28,
  NetFireCollectorF: 29,
  NetAudioCollectorF: 30,
  NetSequencePowerF: 31,
  NetRemoteControlerF: 48,

  NetSpeakerG: 49,
  NetAmpControler: 50,
  NetNoiseDetector: 51
};


//20221107 增加设备特性枚举
export const deviceFeatureEnum = {
  BlueTooth:    1,
  LiveMonitor:  2,
  Intercom:     4,
  VideoCall:    8,
  Sip:          16,
  InformationPublish: 32
}

//2021.3.3 增加deviceType和DeviceModel的mapping
export function getDeviceModelMappingList(deviceType) {
  let mappingList = []
  switch (deviceType) {
    case 0: // 分区设备
      mappingList = [deviceModelEnum.NetSpeakerA, deviceModelEnum.NetSpeakerB, deviceModelEnum.NetSpeakerC, deviceModelEnum.NetSpeakerD, deviceModelEnum.NetSpeakerE, deviceModelEnum.NetSpeakerF, deviceModelEnum.NetSpeakerG, deviceModelEnum.NetAudioMixerDecoder,deviceModelEnum.NetAudioMixerDecoderC]
      break
    case 1: // 寻呼台设备
      mappingList = [deviceModelEnum.NetPagerA, deviceModelEnum.NetPagerB, deviceModelEnum.NetPagerC]
      break
    case 3: // 音频采集器设备
      mappingList = [deviceModelEnum.NetAudioCollectorA,deviceModelEnum.NetAudioCollectorB,deviceModelEnum.NetAudioCollectorC,deviceModelEnum.NetAudioCollectorF]
      break
    case 4: // 消防采集器设备
      mappingList = [deviceModelEnum.NetFireCollectorA,deviceModelEnum.NetFireCollectorB,deviceModelEnum.NetFireCollectorC,deviceModelEnum.NetFireCollectorF]
      break
    case 5: // 网络时序器
      mappingList = [deviceModelEnum.NetSequencePowerA,deviceModelEnum.NetSequencePowerB,deviceModelEnum.NetSequencePowerC,deviceModelEnum.NetSequencePowerF]
      break
    case 6: // 音频协处理器
        mappingList = [deviceModelEnum.NetAudioMixerEncoder,deviceModelEnum.NetAudioMixerEncoderC]
      break
    case 7: // 远程遥控器
      mappingList = [deviceModelEnum.NetRemoteControler,deviceModelEnum.NetRemoteControlerC,deviceModelEnum.NetRemoteControlerF]
      break
    case 8: // 电话网关
      mappingList = [deviceModelEnum.NetPhoneGateway]
    break
    case 9: // 功放控制器
      mappingList = [deviceModelEnum.NetAmpControler]
      break
    case 10: // 噪声检测器
      mappingList = [deviceModelEnum.NetNoiseDetector]
      break
  }
  return mappingList
}

export function getAllTypeList() {
  return [0, 1, 3, 4, 5, 6, 7, 8, 9, 10]
}

export function getAllTypeDeviceMappingList() {
  const list = []
  const typeList = getAllTypeList();
  typeList.forEach(n => {
    const mappingList = getDeviceModelMappingList(n)
    mappingList.forEach(l => list.push(l))
  })
  return list
}

export default new Vuex.Store({
  plugins: [
    createPersistedState({
      storage: window.sessionStorage,
    }),
  ],
  state: getDefaultState(),
  mutations: {
    setDrawerSelectedItemName(state, name) {
      state.drawerSelectedItemName = name;
    },
    SET_BAR_IMAGE (state, payload) {
      state.barImage = payload
    },
    SET_SHOW_BAR_IMAGE (state, payload) {
      state.showBarImage = payload
    },
    SET_DRAWER (state, payload) {
      state.drawer = payload
    },
    SET_MSG (state, payload) {
      state.event = payload
    },
    // for websocket
    WEBSOCKET_INIT (state, needReLogin) {
      //console.log('ws地址是：' + Vue.prototype.$wsHost)
      
      // 防止重复初始化WebSocket连接
      if (state.websocket != null && (state.websocket.readyState === WebSocket.CONNECTING || state.websocket.readyState === WebSocket.OPEN)) {
        //console.log('WebSocket连接已存在，跳过重复初始化')
        return
      }
      
      // 如果存在旧连接，先关闭它
      if (state.websocket != null) {
        // 确保websocket对象有close方法再调用
        if (typeof state.websocket.close === 'function') {
          state.websocket.close()
        }
        state.websocket = null
      }
      
      state.websocket = new WebSocket(Vue.prototype.$wsHost)
      state.websocket.onopen = function () {
        state.wsStatus = 'open'
        if (needReLogin) {
          const auth = {
            command: 'user_login',
            account: state.user,
            password: utils.decrypt(state.pwd),
          }
          state.websocket.send(JSON.stringify(auth))
        }
      }
      state.websocket.onmessage = function (callBack) {
        let data
        try {
          data = JSON.parse(callBack.data)
        } catch (e) {
          // console.log('接受到的信息不是json ' + callBack)
          return
        }

        if (data.command !== 'get_group_info' && data.command !== 'get_playlist_info' &&
          data.command !== 'get_timer_info' && data.command !== 'get_device_info') {
          if (process.env.VUE_APP_SHOW_WS_RESPONSE === 'true') {
            console.log('接收到服务器返回命令: ' + JSON.stringify(data))
          }

        }
        // console.log('接收到服务器返回命令: ' + JSON.stringify(data))
        // 2021/01/23 收到用户未登录错误提示需重新登录
        if (data.result === 5) {
            // console.log('未登录' + data.command)
          if (state.loginResult === 0) {
            // 已登录则自动请求重新登录
            ws.init(true)
          } else {
            // 未登录则跳转到登录页面
            alert(i18n.t('store.serverDisconnected'))
            if (state.websocket != null) {
              state.websocket.close();
            }
          }
          return
        }

        /**
         * specialHandingCommand
         * 该数组内的命令的结果需要特殊处理，不能根据服务器的result返回结果，返回对应的错误信息
         * @link {errorCode}
         * "request_user_relogin": 结果无result返回,故特殊处理
         */
        const specialHandingCommand = ["request_user_relogin"]
        if (!specialHandingCommand.indexOf(data.command) !== -1 && data.result != null && data.result !== 0) {
          state.errorDuration = (data.result === 50 || data.result === 51 || data.result === 52) ? 4000 : 1500
          state.commandName = data.command
          const errorFunction = errorCode.get(data.result)
          state.errorWsMessage = errorFunction ? errorFunction() : i18n.t('store.errors.unknownMistake')
          if(data.result == 51 && data.conflict_timing != null) {
            state.errorWsMessage=state.errorWsMessage + i18n.t('store.conflictTimingPoints') + data.conflict_timing
          }
          state.errorId = ws.getUuid()
          return;
        }

        state.errorDuration = 1500
        // 20210814优化后，以下逻辑的data.result均为0，即代表成功
        switch (data.command) {
          case 'user_login':
            // console.log('检测到用户登录 ' + JSON.stringify(data))
            state.loginResult = data.result
            if (data.result === 0) {
              state.loginResult = 0
              state.noLoginRedirect = false
              state.selectedPartition = []
              state.selectedGroups = []
              //如果data.uuid不存在，会不会报错？
              // 20210814 增加判断，避免报错
              state.uuid = data.uuid
              if (data.server_uuid) {   //等同于if (data.server_uuid !== null && data.server_uuid !== undefined) {
                state.serverId = data.server_uuid
              }
              if (data.user_uuid) {
                state.user_uuid = data.user_uuid
              }
              if(data.cloud_control_valid) {
                state.cloud_control_valid = data.cloud_control_valid
              }
              state.user = data.user_authority.account
              state.machineCode = data.machine_code
              if(data.cloud_control_machine_code) {
                state.cloudControlMachineCode = data.cloud_control_machine_code
              }
              state.type = data.type
              state.expiration_date = data.expiration_date
              state.ip = data.ip
              state.mac = data.mac
              state.version = data.version
              state.systemType = data.system_type
              state.isCloudServer = data.cloud_server
              state.authority = data.user_authority.authority
              state.user_type = data.user_type
              state.isEnableMonitor = data.enable_monitor
              state.isEnableExaminationMode = (typeof(data.examination_mode) !== 'undefined')?data.examination_mode:false
            }
            break
          case 'get_device_info': // 获取所有分区和设备信息 todo update for single query, todo 重构
            // console.log('获取到分区信息 ' + JSON.stringify(data))
            // 2020/12/16 使用另一变量统一修改
            // 重写获取方法 1. 如果是all_zone，filter掉对应的那部分,然后重新加入并排序 2. 如果不是all_zone，则修改对应id的
            if (data.all_zone) {
              state.eventList = state.eventList.filter(item => !(getDeviceModelMappingList(data.device_type).includes(item.device_model)))
              state.eventList.push.apply(state.eventList, data.zones)
              state.newEventList = JSON.parse(JSON.stringify(state.eventList))
              // 排序
              // state.newEventList.sort((a, b) => {
              //   if (a.device_model === b.device_model) {
              //     return a.id - b.id
              //   } else {
              //     return a.device_model - b.device_model
              //   }
              // })
            } else {
              for (let zoneIndex = 0; zoneIndex < data.zones.length; zoneIndex++) {
                const zone = JSON.parse(JSON.stringify(data.zones[zoneIndex]))
                let needInsert = true
                for (let i = 0; i < state.newEventList.length; i++) {
                  // 修改为采用唯一性属性来标识，比如mac 10.23
                  if (state.newEventList[i].mac === zone.mac && state.newEventList[i].device_model === zone.device_model) {
                    needInsert = false
                    // 20200131 当接收到某一分区从非离线变为离线的情况，做如下操作：
                    if (zone.source === -1 && state.newEventList[i].source !== -1) {
                      // 1. 取消选中状态（从已选分区中移除）
                      const index = state.selectedPartition.indexOf(zone.mac)
                      if (index > -1) {
                        state.selectedPartition.splice(index, 1)
                      }
                      // 2. 监听状态修改（监听音箱中途离线后，监听按钮图标颜色变灰）

                    }

                    // 更新总eventList
                    state.newEventList.splice(i, 1, zone)
                    break
                  }
                }
                if (needInsert) {
                  state.newEventList.splice(zone.id - 1, 0, zone)
                  // 此处需要立即更新
                  state.eventList = state.newEventList
                }
                else {
                  state.needRefreshZoneList = true
                }
              }
            }
            break
          case 'get_group_info': // 获取分组信息
            if (data.result === 0) {
              // console.log('获取分组信息成功' + JSON.stringify(data));
              // 添加zone_names
              if (data.groups.length > 0) {
                data.groups.forEach((group) => {
                  const zoneNames = []
                  if (group.zones.length > 0) {
                    for (let i = 0; i < group.zones.length; i++) {
                      for (let j = 0; j < state.eventList.length; j++) {
                        if (state.eventList[j].mac === group.zones[i]) {
                          zoneNames.push(state.eventList[j].name)
                          break
                        }
                      }
                    }
                  }
                  group.zone_names = zoneNames
                })
                state.groupList = data.groups
              }
            }
            break
          case 'get_playlist_info': // 获取列表信息
            if (data.result === 0) {
              // console.log(JSON.stringify(data.playlists))
              // ******** 非管理员播放列表只显示管理员及自己创建的列表
              // ******** 增加权限控制，如果有播放列表管理权限，可显示/管理所有用户的播放列表
              let playlistArray = []
              if (state.user === 'admin' || checkIfPlaylistAllowed(state.authority)) {
                playlistArray = data.playlists
              } else {
                playlistArray = data.playlists.filter(list => list.list_account === 'admin' || list.list_account === state.user);
              }
              if (playlistArray.length > 0) {
                playlistArray.forEach(list => {
                  list.active = false
                  // ******** 增加song_account字段，暂时只对TTS列表做过滤处理
                  const songs = list.songs.map(obj=> ({ ...obj, type: getSongType(obj.song_path_name), song_account: getSongAccount(obj.song_account)}))
                  list.songs = filterListSongsBySongAccount(state.user, state.authority, list.list_name, songs)
                })
              }
              state.playList = playlistArray
            }
            break
          case 'add_song_list':
            // console.log('新增列表结果:' + data.result)
            state.addSongListResult = data.result
            if (data.result === 0) {
              state.playList.push({
                list_id: data.list_id,
                list_name: data.list_name,
                songs: [],
                active: false,
                list_account: state.user
              })
            }
            break
          case 'rename_song_list':
            // console.log('重命名列表结果:' + data.result)
            state.renameSongListResult = data.result
            if (data.result === 0) {
              const list = ws.getListByListId(data.list_id)
              if (list !== null) {
                list.list_name = data.list_name
              }
            }
            break
          case 'remove_song_list':
            // console.log('删除列表结果:' + data.result)
            state.removeSongListResult = data.result
            if (data.result === 0) {
              for (let i = 0; i < state.playList.length; i++) {
                if (state.playList[i].list_id === data.list_id) {
                  state.playList.splice(i, 1)
                }
              }
            }
            break
          case 'request_upload_song_to_server':
            // console.log('预上传结果:' + data.result)
            if (data.result === 0) {
              state.uploadPath = data.upload_path
              state.uploadSongPath = 'http://' + data.server_ip + ':' + data.server_port + '/' + data.upload_path
            }
            break
          case 'add_song_to_list':
            // console.log('增加歌曲到列表结果:' + JSON.stringify(data))
            // 无需手动更新vuex，自动更新playList.xml
            // 20210814 uploadSongResult未使用
            // if (data.result === 0 || data.result === 8) {
            //   state.uploadSongResult = true
            // }
              state.addSongToListResult = data.result
            break
          case 'add_group':
            // console.log('增加分组结果:' + data.result)
            state.createNewGroupResult = data.result
            break
          case 'edit_group':
            // console.log('编辑分组结果:' + data.result)
            state.editGroupResult = data.result
            break
          case 'set_group_zone':
            // console.log('设置分组分区结果:' + data.result)
            // 20210814 该结果未使用
            state.setGroupZoneResult = data.result
            break
          case 'remove_group':
            // console.log('移除分组结果: ' + data.result)
            state.removeGroupResult = data.result
            if (data.result === 0) {
              // remove group info from vuex
              for (let i = 0; i < state.groupList.length; i++) {
                if (state.groupList[i].group_id === data.group_id) {
                  state.groupList.splice(i, 1)
                  break
                }
              }
              for (let i = 0; i < state.selectedGroups.length; i++) {
                if (state.selectedGroups[i] === data.group_id) {
                  state.selectedGroups.splice(i, 1)
                  break
                }
              }
            }
            break
          case 'update_file':
            // console.log('检测到文件修改' + data.file_type)
            if (data.result === 0) {
              state.fileUpdate = data
            }
            break
          case 'play_source':
            // console.log('检测到节目源播放 ' + data.result)
            state.playSourceResult = data.result
            break
          case 'play_audiocollect_source':
            // console.log('检测到节目源播放 ' + data.result)
            state.playCollectResult = data.result
            break
          case 'set_idle_status':
            // console.log('检测到停止播放 ' + data.result)
            state.setIdleStatusResult = data.result
            break
          case 'play_mode':
            // console.log('检测到播放模式请求 ' + JSON.stringify(data))
            if (data.result === 0) {
              // 当查询时不会提示'设置播放模式成功'
              state.serverPlayMode = data.play_mode
              if (data.set === 1) {
                state.playModeResult = 0
              }
            }
            break
          case 'set_volume':
            state.setVolumeResult = data.result
            break
          case 'request_paging':
            if (data.event !== null && data.event !== undefined) {
              state.requestPagingEvent = data.event
            }
            state.requestPagingResult = data.result
            break
          case 'remove_song_from_list':
            // console.log('删除歌曲' + data.result)
            state.removeSongFromListResult = data.result
            break
          case 'audit_local_song':
            state.auditSongForMediaResult = data.result
            break
          case 'set_select_zones':
            // console.log('检测到设置选中分区 ' + data.result)
            state.selectIdTime = data.selected_id
            break
          case 'set_device_info':
            // console.log('检测到设置分区信息 ' + JSON.stringify(data))
            if (data.set_type === 1) {
              state.setDeviceInfoResult = data.result
            } else if (data.set_type === 2) {
              state.setMonitorBindingResult = data.result
            }
            break
          case 'upgrade_device':
            // console.log('检测到升级固件 ' + JSON.stringify(data))
            state.upgradeStatus = data.upgrade_status
            state.deviceUpgradeProgress = data.percentage
            break
          case 'device_ip':
            // console.log('检测到设置分ip ' + JSON.stringify(data))
            if (data.set === 1) {
              state.setDeviceIpResult = data.result
            } else if (data.set === 0) {
              state.getDeviceIpInfo = data
            }
            break
          case 'get_server_upload_firmwares':
            // console.log('检测到获取固件 ' + data.result)
            if (data.result === 0) {
              state.existFirmwareCount = data.firmware_count
              state.existFirmwareNames = data.firmware_names
            }
            break
          case 'reboot_device':
            // console.log('检测到重启设备 ' + JSON.stringify(data))
            state.rebootDeviceResult = data.result
            break
          case 'reset_device_data':
            // console.log('检测到重置设备数据 ' + JSON.stringify(data))
            state.resetDeviceDataResult = data.result
            break
          case 'get_timer_info':
            // console.log('检测到获取定时点信息 ' + JSON.stringify(data))
            if (data.result === 0) {
              state.timerList = data.time_schemes
              state.currentSchemaId = data.current_scheme_id
            }
            break
          case 'set_current_time_scheme':
            state.setActiveSchemaResult = data.result
            break
          case 'add_time_scheme':
            // console.log('检测到新增定时方案 ' + JSON.stringify(data))
            state.addSchemaResult = data.result
            break
          case 'remove_time_scheme':
            // console.log('检测到删除定时方案 ' + JSON.stringify(data))
            state.deleteSchemaResult = data.result
            break
          case 'edit_time_scheme':
            // console.log('检测到编辑定时方案 ' + JSON.stringify(data))
            state.editSchemaResult = data.result
            break
          /* 主机请求用户重新登录 */
          case 'request_user_relogin':
            // console.log('主机请求用户重新登录, status_code: ' + JSON.stringify(data))
            state.statusCode = data.status_code
            break
          case 'reset_server_data':
            // console.log('请求数据重置 ' + JSON.stringify(data))
            state.resetDataResult = data.result
            break
          case 'reboot_server':
            // console.log('服务器重启 ' + JSON.stringify(data))
            state.rebootResult = data.result
            if (data.result === 0) {
              state.rebootBySelf = true
            }
            break;
          case 'factory_reset':
            // console.log('恢复出厂设置 ' + JSON.stringify(data))
            state.resetDefaultResult = data.result
            break
          case 'upgrade_server':
            // console.log('服务器升级 ' + JSON.stringify(data))
            state.upgradeServeResult = data.result
            if (data.result === 0) {
              state.rebootBySelf = true
            }
            break
          case 'add_time_point':
            // console.log('添加定时点 ' + JSON.stringify(data))
            state.addTimePointResult = data.result
            break
          case 'edit_time_point':
            // console.log('添加定时点 ' + JSON.stringify(data))
            state.editTimerResult = data.result
            break
          case 'remove_time_point':
            // console.log('移除定时点 ' + JSON.stringify(data))
            state.deleteTimerResult = data.result
            break
          case 'set_system_date_time':
            // console.log('时间改变' + JSON.stringify(data))
            state.systemDateTime = data.system_date_time
            state.systemBootTime = data.system_boot_time
            state.timestamp = data.system_date_time
            if (data.set === 1) {
              state.setSystemDateTimeResult = data.result
            }
            break
          case 'set_system_network':
            state.systemNetwork = {
              ipAddress: data.ip_address,
              subnetMask: data.subnet_mask,
              gateway: data.gateway,
              dnsServer: data.dns_server,
            }
            if(data.ip_address.length > 0) {
              state.ip = data.ip_address// 同步更新state.ip,避免特殊情况下IP变化了但是用户没有重新登录,系统信息界面显示旧的IP地址的问题。
            }
            if (data.set === 1) {
              state.setSystemNetworkResult = data.result
            }
            break
          case 'get_system_storage':
            state.systemStorage = {
              hardDiskTotal: data.hard_disk_total,
              hardDiskRemain: data.hard_disk_remain,
              memoryTotal: data.memory_total,
              momeoryRemain: data.momeory_remain,
            }
            break
          case 'time_point_valid':
            // console.log('接收到改变定时点状态' + JSON.stringify(data))
            state.setTimePointValidResult = data.result
            state.apiName = data.isvalid ? i18n.t('store.enableTimingPoint') : i18n.t('store.disableTimingPoint')
            break
          case 'get_user_info':
            // console.log(JSON.stringify(data))
            // 兼容旧程序，即登陆时获取的user_type为空
            state.userAccountSendRequest[data.dest_account] = false
            if (state.user_type == null) {
              if (data.dest_account === 'admin') {
                state.userInfoData = data;
              } else {
                let found_user = false;
                for (let i = 0; i < state.userInfoData.sub_users.length; i++) {
                  if(state.userInfoData.sub_users[i].account === data.dest_account) {
                    state.userInfoData.sub_users[i].password = data.dest_password
                    state.userInfoData.sub_users[i].user_authority = data.user_authority
                    if(state.user === 'admin') {
                      ws.getUserZone(data.dest_account)
                    }
                    found_user=true
                    break
                  }
                }
                if(!found_user) {
                  state.userInfoData.sub_users.push({
                    account: data.dest_account,
                    password: data.dest_password,
                    user_authority: data.user_authority,
                  })
                }
              }
              break
            }

            // 如果接收的目标账户名称是当前登录的账户名称，那么需要更新全部
            // 多级账户
            if (data.dest_account === state.user) {
              state.userInfoData = data
              //******** 当没有子账户的时候，先将zonesMacObject重置，解决当前账户删除唯一一个子账户后页面不刷新的问题
              if(state.userInfoData.sub_users.length === 0) {
                Vue.set(state, 'zonesMacObject', {})
              }
              if(data.dest_account !== 'admin') {
                Vue.set(state.zonesMacObject, data.dest_account, data.user_authority.zones_mac)
              }
              state.userInfoData.sub_users.forEach(subuser => {
                Vue.set(state.zonesMacObject, subuser.account, subuser.user_authority.zones_mac)
              })
            } else {
              // zonesMac信息放入zonesMacObject对象中（key为用户，value为mac的对象）
              Vue.set(state.zonesMacObject, data.dest_account, data.user_authority.zones_mac)
              // 更新对应的账户信息
              let user_type = data.user_type
              let sub_users = null
              //console.log("user_type="+user_type)
              if (state.user_type === 2) {
                user_type -= 1
              }
              if (state.user_type === 3) {
                user_type -= 2
              }
              if (state.user_type === 4)  //此处不会进入，只是预留更多级时使用
              {
                user_type -= 3
              }
              if (user_type === 2) {
                if (state.userInfoData) {
                  sub_users = state.userInfoData.sub_users
                }
              } else if (user_type === 3) {
                if (state.userInfoData.sub_users) {
                  //查找发下来的三级账户属于哪一个二级账户
                  let isFound = false;
                  for (let i = 0; i < state.userInfoData.sub_users.length; i++) {
                    if (state.userInfoData.sub_users[i].sub_users) {
                      for (let j = 0; j < state.userInfoData.sub_users[i].sub_users.length; j++) {
                        if (state.userInfoData.sub_users[i].sub_users[j].account === data.dest_account) {
                          sub_users = state.userInfoData.sub_users[i].sub_users
                          isFound = true
                          break
                        }
                      }
                      if (isFound) {
                        break
                      }
                    }
                  }
                }
              } else if (user_type === 4) {
                //查找发下来的四级账户属于哪一个三级账户
                if (state.userInfoData.sub_users) {
                  let isFound = false;
                  for (let i = 0; i < state.userInfoData.sub_users.length; i++) {
                    if (state.userInfoData.sub_users[i].sub_users) {
                      for (let j = 0; j < state.userInfoData.sub_users[i].sub_users.length; j++) {
                        if (state.userInfoData.sub_users[i].sub_users[j].sub_users) {
                          for (let k = 0; k < state.userInfoData.sub_users[i].sub_users[j].sub_users.length; k++) {
                            if (state.userInfoData.sub_users[i].sub_users[j].sub_users[k].account === data.dest_account) {
                              sub_users = state.userInfoData.sub_users[i].sub_users[j].sub_users
                              isFound = true
                              break
                            }
                          }
                          if (isFound) {
                            break
                          }
                        }
                        if (isFound) {
                          break
                        }
                      }
                      if (isFound) {
                        break
                      }
                    }
                  }
                }
              }
              if(sub_users!=null) {
                for (let i = 0; i < sub_users.length; i++) {
                  let user = sub_users[i]
                  if(user.account === data.dest_account) {
                    user.password = data.dest_password
                    user.user_type = data.user_type
                    user.user_name = data.user_name
                    user.sub_user_count = data.sub_user_count
                    //console.log("user.sub_user_count:"+user.sub_user_count)
                    user.user_authority = data.user_authority
                    user.sub_users      = data.sub_users

                    user.sub_users.forEach(subuser => {
                      Vue.set(state.zonesMacObject, subuser.account, subuser.user_authority.zones_mac)
                    })
                    //console.log("sub_users:"+JSON.stringify(data.sub_users))
                    break
                  }
                }
              }
              // console.log('get_user_info' + JSON.stringify(state.userInfoData))
            }
            state.userInfoData.account = state.userInfoData.dest_account
            state.userInfoData.password = state.userInfoData.dest_password
            // console.log('get_user_info' + JSON.stringify(state.userInfoData))
            // console.log('state.zonesMacObject: '+ JSON.stringify(state.zonesMacObject))

              // ******** 更新mac用户信息，// 将key为用户-value为mac的对象 ----> 转换成key为mac，value为用户名对象
              // 每次用户分区数据发生修改，先清空后重新加载数据
              //******** 待优化
              state.zonesMacKeyAndAccountValueObject = Object.create(null)
              const accounts = Object.keys(state.zonesMacObject)
              if (accounts.length === 0) {
                return
              }
              accounts.forEach(account => {
                const macs = state.zonesMacObject[account]
                if (macs == null || macs.length === 0) {
                  return
                }
                macs.forEach(mac => {
                  const accountArray = state.zonesMacKeyAndAccountValueObject[mac]
                  if (accountArray == null || accountArray.length === 0) {
                    Vue.set(state.zonesMacKeyAndAccountValueObject, mac, [account])
                  } else if (!accountArray.includes(account)) {
                    accountArray.push(account)
                    Vue.set(state.zonesMacKeyAndAccountValueObject, mac, accountArray)
                  }
                })
              })
            break
          case 'get_user_zone':
            /*
            // zonesMac信息放入zonesMacObject对象中（key为用户，value为mac的对象）
            Vue.set(state.zonesMacObject, data.dest_account, data.zones_mac)

            // ******** 更新mac用户信息，// 将key为用户-value为mac的对象 ----> 转换成key为mac，value为用户名对象
            // 每次用户分区数据发生修改，先清空后重新加载数据
            state.zonesMacKeyAndAccountValueObject = Object.create(null)
            const accounts = Object.keys(state.zonesMacObject)
            if (accounts.length === 0) {
              return
            }
            accounts.forEach(account => {
              const macs = state.zonesMacObject[account]
              if (macs == null || macs.length === 0) {
                return
              }
              macs.forEach(mac => {
                const accountArray = state.zonesMacKeyAndAccountValueObject[mac]
                if (accountArray == null || accountArray.length === 0) {
                  Vue.set(state.zonesMacKeyAndAccountValueObject, mac, [account])
                } else if (!accountArray.includes(account)) {
                  accountArray.push(account)
                  Vue.set(state.zonesMacKeyAndAccountValueObject, mac, accountArray)
                }
              });
            })
            */
            break;
          case 'add_user':
            // console.log('add_user' + JSON.stringify(data))
            state.addUserResult = data.result
            state.apiName = i18n.t('store.addUser')
            break
          case 'remove_user':
            state.deleteUserResult = data.result
            state.apiName = i18n.t('store.removeUser')
            break
          case 'edit_user':
            // console.log('edit_user' + JSON.stringify(data))
            state.editUserResult = data.result
            state.apiName = i18n.t('store.editUser')
            break
          case 'get_fire_collector_info':
            state.fireCollectorInfo = data.fire_collectors
            break
          case 'get_audio_collector_info':
            state.audioCollectorInfo = data.audio_collectors
            break
          /* 监听相关 */
          case 'set_audio_monitor_speaker':
            if (data.result === 0) {
              if (data.set === 0) {
                state.audioMonitorDeviceMac = data.device_mac // 如果没有绑定音箱，将应答device_mac为""（空字符串)
              } else {
                state.setAudioMonitorSpeakerResult = data.result
                state.audioMonitorDeviceMac = data.device_mac
                if (data.device_mac !== '') {
                  state.apiName = i18n.t('store.setMonitorSpeaker')
                } else {
                  state.apiName = i18n.t('store.resetMonitorFunction')
                }
              }
            }
            break
          case 'audio_monitor_source':
            state.openAudioMonitorResult = data.result
            state.apiName = data.oper === 1 ? i18n.t('store.setSourceMonitor') : i18n.t('store.stopMonitorSpeaker')
            break
          case 'set_fire_channel':
            state.setFireChannelResult = data.result
            state.apiName = i18n.t('store.setFireChannel')
            break
          case 'text_to_speech':
            state.textToSpeechResult = data.result
            state.apiName = i18n.t('store.textToSpeech')
            break
          // 蓝牙设置
          case 'set_bluetooth':
            if (data.set === 0) {
              state.queryBluetoothResult = data.result
              state.bluetoothName = data.bluetooth_name
              state.bluetoothEncryption = data.bluetooth_encryption
              state.bluetoothPin = data.bluetooth_pin
            } else if (data.set === 1) {
              state.setBluetoothResult = data.result
            }
            break
          case 'device_eq_mode':
            // console.log('音效设置 ' + JSON.stringify(data))
            if (data.set === 0) {
              state.effectGainInfo = {
                eqMode: data.eq_mode,
                gain1: data.gain1 >= 246 ? data.gain1 - 256 : data.gain1,
                gain2: data.gain2 >= 246 ? data.gain2 - 256 : data.gain2,
                gain3: data.gain3 >= 246 ? data.gain3 - 256 : data.gain3,
                gain4: data.gain4 >= 246 ? data.gain4 - 256 : data.gain4,
                gain5: data.gain5 >= 246 ? data.gain5 - 256 : data.gain5,
                gain6: data.gain6 >= 246 ? data.gain6 - 256 : data.gain6,
                gain7: data.gain7 >= 246 ? data.gain7 - 256 : data.gain7,
                gain8: data.gain8 >= 246 ? data.gain8 - 256 : data.gain8,
                gain9: data.gain9 >= 246 ? data.gain9 - 256 : data.gain9,
                gain10: data.gain10 >= 246 ? data.gain10 - 256 : data.gain10,
              }
              state.queryEffectResult = data.result
            } else if (data.set === 1) {
              state.setEffectResult = data.result
            }
            break
          case 'export_run_log':
            // result 不需要监听
            state.exportRunLogResult = data.result
            state.runLogPath = data.file_path
            break
          case 'get_monitor_info':
            if (!data.monitors || data.monitors.length === 0) {
              return
            }
            if (data.monitors.length > 1) {
              state.monitors = data.monitors
            } else if (data.monitors.length === 1) {
              if(state.monitors.length == 1) {
                state.monitors = data.monitors
              } else {
                // 更新单个监控设置
                const monitor = JSON.parse(JSON.stringify(data.monitors[0]))
                let needInsert = true
                for (let i = 0; i < state.monitors.length; i++) {
                  if (state.monitors[i].monitor_mac === monitor.monitor_mac) {
                    needInsert = false
                    state.monitors.splice(i, 1, monitor)
                    break
                  }
                }
                if (needInsert) {
                  state.monitors.splice(state.monitors.length, 0, monitor)
                }
              }
            }
            break
          case 'set_monitor_info':
            // console.log('设置监控信息 ' + JSON.stringify(data))
            state.setMonitorInfoResult = data.result
            break
          case 'add_custom_monitor':
            state.addCustomMonitorResult = data.result
            break
          case 'delete_monitor':
              // console.log('删除监控 ' + JSON.stringify(data))
              state.delMonitorInfoResult = data.result
              break
          case 'server_register':
            // console.log('设备注册 ' + JSON.stringify(data))
            state.serverRegisterResult = data.result
            break
          case 'get_today_timer_info':
            // console.log('获取今日定时点信息 ' + JSON.stringify(data))
            // currentSchemaId已在get_timer_info中获取到
            state.currentSchemaId = data.current_scheme_id
            state.todayTimerList = data.timerlists
            break
          case 'get_backup_file_names':
            // console.log('获取配置文件名称 ' + JSON.stringify(data))
            state.systemBackupNames = data.backup_names // string数组
            break
          case 'backup_server_data':
            // console.log('服务器备份数据 ' + JSON.stringify(data))
            state.backupServerResult = data.result
            break
          case 'remove_backup_file':
            state.removeBackupFileResult = data.result
            break
          case 'upload_server_data':
            // state.uploadBackupResult = data.result
            break
          case 'restore_server_data':
            state.restoreServerDataResult = data.result
            if (data.result === 0) {
              state.rebootBySelf = true
            }
            break
          case 'copy_time_scheme':
            // console.log('复制定时方案 ' + JSON.stringify(data))
            state.copySchemaResult = data.result
            break
          case 'sort_device_custom':
            // console.log('sort_device_custom::: ' + JSON.stringify(data))
            state.sortDeviceCustomResult = data.result
            break
          case 'delete_device':
            // console.log('delete_device::: ' + JSON.stringify(data))
            state.deleteDeviceResult = data.result
            break
          case 'get_manual_task_info':
            // console.log('get_manual_task_info::: ' + JSON.stringify(data))
            state.manualTaskInfo = data.tasks
            break
          case 'set_task_play_mode':
            // console.log('set_task_play_mode::: ' + JSON.stringify(data))
            state.setTaskPlayModeResult = data.result
            break
          case 'set_task_play_status':
            // console.log('set_task_play_status::: ' + JSON.stringify(data))
            state.setTaskPlayStatusResult = data.result
            break
          case 'set_task_play_pre_next':
            // console.log('set_task_play_pre_next::: ' + JSON.stringify(data))
            state.setTaskPlayPreNextResult = data.result
            break
          case 'set_task_playback_progress':
            if (state.manualTaskInfo) {
              state.manualTaskInfo.forEach(task => {
                if (task.play_id === data.play_id) {
                  task.cur_play_time = data.cur_play_time;
                  // console.log("task.cur_play_time="+data.cur_play_time)
                }
              })
            }
            break
          case 'get_local_songlist_info':
            state.localSongListInfo = data.songlists.map((obj, index)=> ({ ...obj, id: index + 1 }))
            break
          case 'copy_time_point':
            // console.log('copy_time_point::: ' + JSON.stringify(data))
            state.copyTimerResult = data.result
            break
          case 'sort_time_point':
            state.sortTimerResult = data.result
            break;
          case 'sort_song_list':
            // console.log('sort_song_list::: ' + JSON.stringify(data))
            state.sortSongListResult = data.result
            break
          case 'single_cancel_time_point':
            // console.log('single_cancel_time_point::: ' + JSON.stringify(data))
            if (data.iscancel) {
              state.singleCancelTimePointResult = data.result
            } else {
              state.singleRestartTimePointResult = data.result;
            }
            break
          case 'get_sequence_power_info':
            // console.log('get_sequence_power_info::: ' + JSON.stringify(data))
            state.sequencePowerInfo = data.sequence_powers != null ? data.sequence_powers : []
            // 过滤掉不属于自己账户的电源时序器
            filterSequencePowerInfoByAccount(state)
            // console.log(state.sequencePowerInfo)
            break
          case 'set_sequence_power_device':
            // console.log('set_sequence_power_device::: ' + JSON.stringify(data))
            state.setSequencePowerDeviceResult = data.result
            break
          case 'set_time_sequence_power':
            // console.log('set_time_sequence_power::: ' + JSON.stringify(data))
            // state.setSequencePowerDeviceResult = data.result
            break
          case 'device_network_mode':
            // console.log('收到服务器响应:::device_network_mode::: ' + JSON.stringify(data))
            state.singleDeviceNetworkMode = data.network_mode
            state.singleDeviceNetworkServerIp = data.server_ip
            state.singleDeviceNetworkServerPort = data.server_port

            state.singleDeviceNetworkServerIp2 = data.backup_server_ip != null ? data.backup_server_ip : ""
            state.singleDeviceNetworkServerPort2 = data.backup_server_port != null ? data.backup_server_port : 0

            if (data.set === 0) {
              state.queryDeviceNetworkModeResult = data.result
            } else {
              state.setDeviceNetworkModeResult = data.result
            }
            break;
          case 'set_sip_info':
            state.singleDeviceSipEnable = data.sip_enable
            if (data.sip_server_protocol) {   //等同于if (data.sip_server_protocol !== null && data.sip_server_protocol !== undefined) {
              state.singleDeviceSipProtocol = data.sip_server_protocol
            }
            else {
              state.singleDeviceSipProtocol = 0  //UDP
            }
            state.singleDeviceSipOutPutVolume = data.sip_output_vol
            state.singleDeviceSipStatus = data.sip_status,
            state.singleDeviceSipServerIp = data.sip_server_ip
            state.singleDeviceSipServerPort = data.sip_server_port
            state.singleDeviceSipAccount =  data.sip_account,
            state.singleDeviceSipPassword = data.sip_password

            if (data.set === 0) {
              state.queryDeviceSipInfoResult = data.result
            } else {
              state.setDeviceSipInfoResult = data.result
            }
          break;
          case 'set_information_publish':
            state.singleDeviceInformationPubDisplayEnable = data.enable_display
            state.singleDeviceInformationPubText = data.text
            state.singleDeviceInformationEffects = data.effects,
            state.singleDeviceInformationSpeed = data.speed
            state.singleDeviceInformationStayTime = data.stay_time

            if (data.set === 0) {
              state.queryDeviceInformationPubResult = data.result
            } else {
              state.setDeviceInformationPubResult = data.result
            }
          break;
          case 'set_sub_volume':
            // console.log('检测到设置子音量 ' + JSON.stringify(data))
            if (data.set === 1) {
              state.setSubVolumeResult = data.result
            } else if (data.set === 0) {
              state.subVolumeFromServer = data.sub_volume
              state.auxVolumeFromServer = data.aux_volume != null ? data.aux_volume : 100
            }
            break
          case 'monitor_switch':
            // console.log('检测到视频监控修改 ' + JSON.stringify(data))
            state.switchMonitorResult = data.result
            state.switchMonitorType = data.enable_monitor
            // 如果视频修改开关与现有不一致，刷新开关变量
            if (data.enable_monitor !== state.isEnableMonitor) {
              state.isEnableMonitor = data.enable_monitor
            }
            break;
          case 'examination_mode_switch':
            // console.log('检测到考试模式修改 ' + JSON.stringify(data))
            state.isEnableExaminationMode = data.examination_mode
            break;
          case 'get_remote_controler_info':
            // console.log('检测到获取远程遥控器参数 ' + JSON.stringify(data))
            state.remoteControllerInfos = data.remote_controlers
            break
          case 'set_remote_controler_task':
            // console.log('检测到添加/编辑/删除远程遥控器任务 ' + JSON.stringify(data))
            state.setRemoteControllerTaskResult = data.result
            break
          case 'set_remote_controler_key':
            // console.log('检测到设置远程遥控器按键 ' + JSON.stringify(data))
            state.setRemoteControllerKeyResult = data.result
            break
          case 'start_manual_alarm':
            state.startManualAlarmResult = data.result
            state.startManualAlarmStatus = data.set
            break
          case 'set_intercom_basic':
            // console.log('检测到查询/设置对讲终端基本参数 ' + JSON.stringify(data))
            state.intercomKey1Mac = data.key1_mac
            state.intercomKey2Mac = data.key2_mac
            if(data.mic_vol == null) {
              state.intercomMicVolume = 5;
            }
            else {
              state.intercomMicVolume = data.mic_vol
            }
            if(data.far_out_vol == null) {
              state.intercomFarOutPutVolume = 5;
            }
            else {
              state.intercomFarOutPutVolume = data.far_out_vol
            }
            if (data.set === 1) {
              state.intercomSetResult = data.result
            } else {
              state.intercomQueryResult = data.result
            }
            state.intercomDeviceMac = data.device_mac
            break
          case 'set_trigger':
            // console.log('检测到查询/设置触发参数 ' + JSON.stringify(data))
            if (data.set === 0) {
              state.queryTriggerResult = data.result
            } else if (data.set === 1) {
              state.setTriggerResult = data.result
            } else {
              return;
            }
            state.triggerSwitch = data.trigger_switch
            state.triggerMode = data.trigger_mode
            state.triggerSongPathName = data.trigger_song_path_name
            state.triggerVolume = data.trigger_volume
            state.triggerDeviceMac = data.device_mac
            break
          case 'set_audio_collector_parm':
              if (data.set === 0) {
                state.queryAudioCollectorParmResult = data.result
              } else if (data.set === 1) {
                state.setAudioCollectorParmResult = data.result
              } else {
                return;
              }
              state.audioCollectorPriority = data.priority?data.priority:1,     //音源优先级：1-默认，低于定时音源 2-高优先级，高于定时音源
              state.audioCollectorTriggerSwitch = data.trigger_switch,          //触发主开关：0-关闭，1-开启
              state.audioCollectorTriggerChannelId = data.trigger_channel_id,   //触发通道Id（1~4），数值越大，优先级越高
              state.audioCollectorTriggerZoneVolume = data.trigger_zone_volume, //触发分区音量(0~100,255)
              state.audioCollectorDeviceMac = data.device_mac
              state.audioCollectorTriggerZoneMacs = data.trigger_zone_macs     //触发分区
            break
          case 'set_audio_mixer_parm':
            if (data.set === 0) {
              state.queryAudioMixerParmResult = data.result
            } else if (data.set === 1) {
              state.setAudioMixerParmResult = data.result
            } else {
              return;
            }
            state.audioMixerMasterSwitch = data.master_switch
            state.audioMixerPriority = data.priority
            state.audioMixerTriggerType = data.trigger_type
            state.audioMixerVolumeFadeLevel = data.volume_fade_level
            state.audioMixerZoneVolume = data.zone_volume
            state.audioMixerDeviceMac = data.device_mac
            state.audioMixerZoneMacs = data.zone_macs
            break
          case 'set_phone_gateway_parm':
            if (data.set === 0) {
              state.queryPhoneGatewayParmResult = data.result
            } else if (data.set === 1) {
              state.setPhoneGatewayParmResult = data.result
            } else {
              return;
            }
            state.phoneGatewayMasterSwitch = data.master_switch
            state.phoneGatewayZoneVolume = data.zone_volume
            state.phoneGatewayDeviceMac = data.device_mac
            state.phoneGatewayZoneMacs = data.zone_macs
            state.phoneGatewayTelWhitelist = data.tel_whitelist
            break
          case 'amp_controler_config':
            if (data.set === 0) {
              state.queryAmpControlerParmResult = data.result
            }
            state.ampControlerMasterStatus = data.master_channel_status
            state.ampControlerBackupStatus = data.backup_channel_status
            state.ampControlerBackupId = data.backup_channel_id
            state.ampControlerDeviceMac = data.device_mac
            break
          case 'noise_adaptive_config':
            if (data.set === 0) {
              state.queryNoiseDetectorParmResult = data.result
            } else if (data.set === 1) {
              state.setNoiseDetectorParmResult = data.result
            } else {
              return;
            }
            state.noiseDetectorSwitch = data.enable_control
            state.noiseDetectorDeviceMac = data.device_mac
            state.noiseDetectorChannelArray = data.channel_value
            state.noiseDetectorVolumeArray = data.segment_volume
            state.noiseDetectorZoneMacs = data.zone_macs
            break
          case 'set_user_storage_capacity':
            // console.log('设置存储空间 ' + JSON.stringify(data))
            state.setUserStorageCapacityResult = data.result
            // 更新userInfoData的两种数据方式
            // 1. 发送get_user_info刷新,刷新后需要在account.vue页面刷新页面
            // 2. 手动更新内存userInfoData数据 -- 选用
            // ws.getUserInfo(data.dest_account)
            updateStorageCapacityOrAuthorityForAccount(state, data.dest_account, data.storage_capacity, null)
            break
          case 'set_user_authority':
            // console.log('设置用户权限 ' + JSON.stringify(data))
            state.setUserAuthorityResult = data.result
            updateStorageCapacityOrAuthorityForAccount(state, data.dest_account, null, data.authority)
            break
          //主备服务器查询/设置
          case 'set_backup_server':
            if (data.set === 1) {
              state.setBackupServerResult = data.result
            }
            state.isEnableServerSync = data.enable_server_sync
            state.isBackupServer = data.is_backup_server
            state.server_sync_status = data.current_server_status
            state.server_sync_dest_ip = data.dest_server_ip
            break
          // 云控制
          case 'cloud_control_switch':
            state.cloud_control_switch = data.cloud_control
            if(data.connected !== undefined) {
              state.cloud_isConnected = data.connected
            }
            break
          // 通话录音开关
          case 'call_record_switch':
            state.callRecordSwitchResult = data
            break
          // 查询通话日志
          case 'query_call_log':
            state.queryCallLogResult = data
            break
          // 获取电台分组信息
          case 'get_radio_group_list':
            state.radioGroupListResult = data
            if (data && data.result == 0) {
              state.radioGroupList = data.groups || []
            }
            break
          // 获取电台详情信息
          case 'get_radio_list_by_group_id':
            state.radioDetailsResult = data
            if (data && data.result == 0) {
              state.radioList = data.radios || []
            }
            break
          // 播放电台响应
          case 'play_radio_source':
            state.playRadioResult = data
            break
          // 停止电台响应
          case 'stop_radio_source':
            state.stopRadioResult = data
            break
          // 添加电台响应
          case 'add_radio_info':
            state.addRadioResult = data
            break
          // 编辑电台响应
          case 'edit_radio_info':
            state.editRadioResult = data
            break
          // 删除电台响应
          case 'delete_radio_info':
            state.deleteRadioResult = data
            break
          default: break
        }
      }
      state.websocket.onerror = function (e) { // 错误
        // 20210516 发生错误时不再主动断开ws连接
        console.log("error occurred during connecting websocket", e)
        // state.wsStatus = 'closed'
        // console.log(e)
      }
      state.websocket.onclose = function (e) { // 关闭
        // console.log('websocket连接已关闭')
        if (state.websocket != null) {
          state.websocket.close()
        }
        state.wsStatus = 'closed'
      }
      state.websocket.binaryType = 'arraybuffer'
      // 发送心跳包 TODO 设置发送间隔及发送格式
      // setInterval(function () {
      //   if (state.uuid !== null) {
      //     console.log('ws发送心跳！')
      //     const heart = {N
      //
      //       command: 'get_heartbeat_info',
      //       uuid: state.uuid,
      //     }
      //     state.websocket.send(JSON.stringify(heart))
      //   }
      //   }, 5000)
    },
    WEBSOCKET_SEND (state, p) {
      // 2020.10.08 如果正在连接ws，等待200ms
      let time = 1
      // if (state.websocket == null || [3].includes(state.websocket.readyState)) {
      //   // websocket已关闭，直接返回登录界面
      //   // router.push('/')
      //   alert('服务器连接异常，即将返回登录页面')
      //   // window.location = 'http://' + this.$store.state.systemNetwork.ipAddress + ':9999'
      //   window.location = 'http://' + window.location.host
      // }
      if (state.websocket !== null && state.websocket.readyState === 0) {
        time = 300
      }
      // else if (state.websocket === null || state.websocket.readyState !== 1) {
      //   alert('服务器已断开连接，请重新登录')
      //   state.websocket.close()
      // }
      setTimeout(() => {
        if (process.env.VUE_APP_SHOW_WS_REQUEST === 'true') {
          console.log('发送json请求： ' + JSON.stringify(p));
        }
        // 过滤websocket为null时的错误日志
        if (state.websocket !== null && state.websocket.readyState === 1) {
          state.websocket.send(JSON.stringify(p, (key, value) => {
            if (value !== null) return value
          }), time);
        }
      })
    },
    WEBSOCKET_CLOSE (state) {
      if (state.websocket != null && state.websocket.readyState === 1) {
        state.websocket.close()
        state.websocket = null
      }
    },
    SET_USERNAME (state, username) {
      state.user = username
    },
    SET_PASSWORD (state, password) {
      state.pwd = utils.encrypt(password)
    },
    logout (state) {
      state.user = null
      state.loginResult = null
    },
    // 设置时间
    updateRealTime (state, time) {
      // console.log('更新时间' + time)
      state.timestamp = time
    },
    copyZoneList (state) {
      state.eventList = JSON.parse(JSON.stringify(state.newEventList))
      state.needRefreshZoneList = false
    },
    // 更新新增分组结果
    updateCreateGroupResult (state, result) {
      state.createNewGroupResult = result
    },
    // 更新新增分组结果
    updateEditGroupResult (state, result) {
      state.editGroupResult = result
    },
    // 更新设置分组分区结果
    updateSetGroupZoneResult (state, result) {
      state.setGroupZoneResult = result
    },
    // 更新选中分区
    updateSelectedPartition (state, mac) {
      const index = state.selectedPartition.indexOf(mac)
      if (index > -1) {
        state.selectedPartition.splice(index, 1)
      } else {
        state.selectedPartition.push(mac)
      }
    },
    // 全选所有分区
    updateAllSelectedPartition (state) {
      if (state.eventList != null && state.eventList.length > 0) {
        const eventListMacs = []
        if (state.user === 'admin') {
          state.eventList.forEach((event) => {
            const checkIfZone = getDeviceModelMappingList(0).includes(event.device_model)
            if (event.source !== -1 && checkIfZone) {
              // 剔除离线分区
              eventListMacs.push(event.mac)
            }
          })
        } else {
          // 对于操作员，只选择自己的分区
          const zones = state.zonesMacObject[state.user]
          state.eventList.forEach((event) => {
            const checkIfZone = getDeviceModelMappingList(0).includes(event.device_model)
            if (zones.indexOf(event.mac) !== -1 && event.source !== -1 && checkIfZone) {
              // -1 代表离线状态
              eventListMacs.push(event.mac)
            }
          })
        }
        if (state.selectedPartition.sort().toString() !== eventListMacs.sort().toString()) {
          state.selectedPartition = eventListMacs
        } else {
          state.selectedPartition = []
        }
      }
    },
    updateAllFilteredCards(state, eventListMacs) {
      // 如果每一个都存在，则取消选中
      if (eventListMacs.every(mac => state.selectedPartition.includes(mac))) {
        state.selectedPartition = state.selectedPartition.filter((el) => !eventListMacs.includes(el))
        return
      }
      // 否则去重并加入已选中
      state.selectedPartition = state.selectedPartition.concat(eventListMacs.filter((item) => state.selectedPartition.indexOf(item) < 0))
    },
    // 更新选中分组
    updateSelectedGroups (state, id) {
      // state.selectIdTime += 1
      const index = state.selectedGroups.indexOf(id)
      if (index > -1) {
        state.selectedGroups.splice(index, 1)
      } else {
        state.selectedGroups.push(id)
      }
    },
    // 全选所有分组
    updateAllSelectedGroups (state, groupList) {
      if (groupList != null && groupList.length !== 0) {
        const groupIds = []
        groupList.forEach((group) => {
          groupIds.push(group.group_id)
        })
        if (state.selectedGroups.sort().toString() !== groupIds.sort().toString()) {
          state.selectedGroups = groupIds
        } else {
          state.selectedGroups = []
        }
        return
      }
      state.selectedGroups = []
    },
    // 删除分组
    deleteAllSelectedGroups (state) {
      state.selectedGroups.forEach((selectedGroup) => {
        state.groupList = state.groupList.filter(group => group.group_id !== selectedGroup)
      })
      state.selectedGroups = []
    },
    // 新建分组
    addNewGroup (state, group) {
      state.groupList.push(group)
    },
    /* 交互相关 */
    // 移除选中分组
    resetSelectedGroups (state) {
      state.selectedGroups = []
    },
    // 移除选中分区
    resetSelectedZones (state) {
      state.selectedPartition = []
    },
    // 设置选中分区为指定的macs集合
    setSpecificSelectedZones(state, zoneMacs) {
      state.selectedPartition = zoneMacs
    },
    // 更新上传进度new
    updateUploadSongArrayProgress (state, data) {
      const value = data.completedPercent
      const index = data.i
      const rate = data.sizeRate
      state.uploadSongArrayProgress[index] = Number((value * rate).toFixed(2))
      //console.log(state.uploadSongArrayProgress);
      let progress = Number(utils.getSumOfIntArray(state.uploadSongArrayProgress).toFixed(2))
      if(progress > 100) {
        progress = 100
      }
      state.uploadSongProgress = progress
    },
    updateUploadSongRealProgress (state, progress) {
      // console.log("updateUploadSongRealProgress="+progress)
      state.uploadSongProgress = progress
    },
    // 更新上传进度， 此属性用于上传固件
    updateUploadFirmwareProgress (state, playload) {
      state.uploadFirmwareProgress = playload
    },
    // 更新上传进度， 此属性用于上传服务器升级包
    updateUploadServePackageProgress (state, playload) {
      state.uploadServePackageProcess = playload
    },
    // 更新上传进度， 此属性用于上传配置文件
    updateUploadBackupProcess (state, playload) {
      state.uploadBackupProcess = playload
    },
    // 重置固件更新相关内容
    resetDeviceUpdateState (state) {
      state.deviceUpgradeProgress = 0
      state.upgradeStatus = 0
    },
    // 更新分区设备
    updateZonesInfo (state) {
      state.eventList = []
      ws.getAllDeviceInfo()
    },
    // 更新分组信息
    updateGroupsInfo (state) {
      state.groupList = []
      ws.getGroupList()
    },
    // 更新列表信息
    updateListsInfo (state) {
      state.playList = []
      ws.getPlayList()
    },
    // 更新音频采集器
    updateAudioCollectors (state) {
      // state.eventList = [] 用新的变量记录音频采集器
      ws.getAudioCollectors()
    },
    // 更新获取音频采集器详细信息
    updateDetailAudioCollectorInfo (state) {
      // state.eventList = [] 用新的变量记录音频采集器
      ws.getAudioCollectorInfo()
    },
    // 更新消防采集器
    updateFireCollector (state) {
      ws.getFireCollectors()
    },
    // 更新电源时序器
    updateSequencePower (state) {
      ws.getSequencePower()
    },
    // 获取系统时间
    updateSystemDateTime (state) {
      ws.setSystemDateTime(0, null)
    },
    // 更新网络配置
    updateSystemNetwork (state) {
      ws.setSystemNetwork(0)
    },
    // 更新系统存储信息
    updateSystemStorage (state) {
      ws.getSystemStorage()
    },
    // 更新寻呼台
    updatePagingCenter (state) {
      ws.getPagingCenter()
    },
    // 更新音频协处理器
    updateAudioMixer (state) {
      ws.getAudioMixers()
    },
    // 更新远程遥控器
    updateRemoteControler (state) {
      ws.getRemoteControlers()
    },
    // 更新电话网关
    updatePhoneGateway (state) {
      ws.getPhoneGateways()
    },
    // 更新功放控制器
    updateAmpControlers (state) {
      ws.getAmpControlers()
    },
    // 更新噪声自适应终端
    updateNoiseDetectors (state) {
      ws.getNoiseDetector() 
    },

    // 更新监听音箱
    updateAudioMonitor (state) {
      ws.setAudioMonitorSpeaker(0, null)
    },
    // 获取监控信息
    updateMonitors (state) {
      state.monitors = []
      ws.getMonitorInfo()
    },
    // 获取今日定时点信息（今日任务）
    updateTodayTimerInfo (state) {
      ws.getTodayTimerInfo()
    },
    // 获取手动任务信息
    updateManualTaskInfo (state) {
      ws.getManualTaskInfo()
    },
    // 获取服务器本地歌曲信息
    updateLocalSongListInfo (state) {
      ws.getLocalSongListInfo()
    },
    // 获取远程遥控器参数
    updateRemoteControllerInfo() {
      ws.getRemoteControllerInfo()
    },
    // 获取主备服务器信息
    updateBackupServerInfo() {
      ws.setBackupServerInfo(0, null, null)
    },
    // 删除分组
    updateRemoveGroupResult (state) {
      state.removeGroupResult = null
    },
    // 只更新分组而不删除
    onlyUpdateGroupsInfo (state) {
      // console.log('更新分组信息')
      ws.getGroupList()
    },
    // 只更新分区而不删除
    onlyUpdateZonesInfo (state) {
      ws.getAllDeviceInfo()
    },
    // 只更新音频采集器而不删除
    onlyUpdateAudioCollector (state) {
      ws.getAudioCollectors()
    },
    // 只更新列表而不删除
    onlyUpdateListInfo (state) {
      ws.getPlayList()
    },
    // 只更新定时点而不删除
    onlyUpdateTimerInfo (state) {
      ws.getServerTimer()
    },
    updateAddSongListResult (state, value) {
      state.addSongListResult = value
    },
    updateRenameSongListResult (state, value) {
      state.renameSongListResult = value
    },
    updateRemoveSongListResult (state, value) {
      state.removeSongListResult = value
    },
    updateAddSongToListResult (state, value) {
      state.addSongToListResult = value
    },
    updateSortSongListResult (state, value) {
      state.sortSongListResult = value
    },
    updateSingleCancelTimePointResult (state, value) {
      state.singleCancelTimePointResult = value
    },
    updateSingleRestartTimePointResult (state, value) {
      state.singleRestartTimePointResult = value
    },
    // 更新播放相关
    updatePlaySourceResult (state) {
      state.playSourceResult = null
    },
    updatePlayCollectResult (state) {
      state.playCollectResult = null
    },
    updateSetVolumeResult (state) {
      state.setVolumeResult = null
    },
    updateRequestPagingResult (state) {
      state.requestPagingResult = null
    },
    updateSetIdleStatusResult (state) {
      state.setIdleStatusResult = null
    },
    updatePlayModeResult (state) {
      state.playModeResult = null
    },
    updateRemoveSongResult (state) {
      state.removeSongFromListResult = null
    },
    updateAuditSongResult (state) {
      state.auditSongForMediaResult = null
    },
    updateServerRegisterResult (state) {
      state.serverRegisterResult = null
    },
    updateSortDeviceCustomResult (state, value) {
      state.sortDeviceCustomResult = value
    },
    updateDeleteDeviceResult (state, value) {
      state.deleteDeviceResult = value
    },
    updateSetTaskPlayModeResult (state, value) {
      state.setTaskPlayModeResult = value
    },
    updateSetTaskPlayStatusResult (state, value) {
      state.setTaskPlayStatusResult = value
    },
    updateSetTaskPlayPreNextResult (state, value) {
      state.setTaskPlayPreNextResult = value
    },
    updateZoneMacSelectedForSort (state, value) {
      state.zoneMacSelectedForSort = value
    },
    updateSongMd5SelectedForSort (state, value) {
      state.songMd5SelectedForSort = value
    },
    updateSystemRegistrationType(state, type) {
      state.type = type
    },
    updateSetDeviceInfoResult (state) {
      state.setDeviceInfoResult = null
    },
    updateUpgradeDeviceResult (state) {
      state.upgradeDeviceResult = null
    },
    updateSetDeviceIpResult (state) {
      state.setDeviceIpResult = null
    },
    updateSetSubVolumeResult (state) {
      state.setSubVolumeResult = null
    },
    updateSubVolumeFromServer (state) {
      state.subVolumeFromServer = null
    },
    updateAuxVolumeFromServer (state) {
      state.auxVolumeFromServer = null
    },
    updateResetDeviceDataResult (state, value) {
      state.resetDeviceDataResult = value
    },
    updateRebootDeviceResult (state, value) {
      state.rebootDeviceResult = value
    },
    updateNoLoginRedirect (state, value) {
      state.noLoginRedirect = value
    },
    updateLoginResult (state, value) {
      state.loginResult = value
    },
    updateWebsocket (state, value) {
      state.websocket = null
    },
    updateUploadFirmwareResult (state, value) {
      state.uploadFirmwareResult = value
    },
    updateUploadServePackageResult (state, value) {
      state.uploadServePackageResult = value
    },
    updateUploadBackupResult (state, value) {
      state.uploadBackupResult = value
    },
    updateBackupServerResult (state, value) {
      state.backupServerResult = value
    },
    updateRemoveBackupFileResult (state, value) {
      state.removeBackupFileResult = value
    },
    updateCopySchemaResult (state, value) {
      state.copySchemaResult = value
    },
    updateCopyTimerResult (state, value) {
      state.copyTimerResult = value
    },
    updateSortTimerResult (state, value) {
      state.sortTimerResult = value
    },
    updateSetSequencePowerDeviceResult(state, value) {
      state.setSequencePowerDeviceResult = value
    },
    updateRestoreServerDataResult (state, value) {
      state.restoreServerDataResult = value
    },
    updateUpgradeServeResult (state, value) {
      state.upgradeServeResult = value
    },
    updateQueryEffectResult (state, value) {
      state.queryEffectResult = value
    },
    updateSetEffectResult (state, value) {
      state.setEffectResult = value
    },
    updateSetMonitorBindingResult (state, value) {
      state.setMonitorBindingResult = value
    },
    updateEffectGainInfo (state, value) {
      state.effectGainInfo = value
    },
    // 清空已选择分区和分组信息
    cleanSelectedPartitionAndGroups (state) {
      // console.log('cleanSelectedPartitionAndGroups')
      state.selectedPartition = []
      state.selectedGroups = []
    },
    updateGetDeviceIpInfo (state, value) {
      state.getDeviceIpInfo = value
    },
    updateAddSchemaResult (state, value) {
      state.addSchemaResult = value
    },
    updateSetSystemDateTimeResult (state, value) {
      state.setSystemDateTimeResult = value
    },
    updateSetSystemNetworkResult (state, value) {
      state.setSystemNetworkResult = value
    },
    updateEditSchemaResult (state, value) {
      state.editSchemaResult = value
    },
    updateDeleteSchemaResult (state, value) {
      state.deleteSchemaResult = value
    },
    updateDeleteUserResult (state, value) {
      state.deleteUserResult = value
    },
    updateOpenAudioMonitorResult (state, value) {
      state.openAudioMonitorResult = value
    },
    updateTextToSpeechResult (state, value) {
      state.textToSpeechResult = value
    },
    updateSetAudioMonitorSpeakerResult (state, value) {
      state.setAudioMonitorSpeakerResult = value
    },
    updateSetFireChannelResult (state, value) {
      state.setFireChannelResult = value
    },
    updateEditUserResult (state, value) {
      state.editUserResult = value
    },
    updateSetActiveSchemaResult (state, value) {
      state.setActiveSchemaResult = value
    },
    updateEditTimerResult (state, value) {
      state.editTimerResult = value
    },
    updateDeleteTimerResult (state, value) {
      state.deleteTimerResult = value
    },
    updateStatusCode (state, value) {
      state.statusCode = value
    },
    updateIsMiniDrawer (state, value) {
      state.isMiniDrawer = value
    },
    updateAddTimePointResult (state, value) {
      state.addTimePointResult = value
    },
    updateSetTimePointValidResult (state, value) {
      state.setTimePointValidResult = value
    },
    updateAddUserResult (state, value) {
      state.addUserResult = value
    },
    resetState (state) {
      // Merge rather than replace so we don't lose observers
      // https://github.com/vuejs/vuex/issues/1118
      Object.assign(state, getDefaultState())
    },
    updateResetDataResult (state, value) {
      state.resetDataResult = value
    },
    updateRebootResult (state, value) {
      state.rebootResult = value
    },
    updateRebootBySelf (state, value) {
      state.rebootBySelf = value
    },
    updateLogoutBySelf (state, value) {
      state.logoutBySelf = value
    },
    updateResetDefaultResult (state, value) {
      state.resetDefaultResult = value
    },
    updateSetBackupSyncResult (state, value) {
      state.setBackupServerResult = value
    },
    // 蓝牙设置
    updateQueryBluetoothResult (state, value) {
      state.queryBluetoothResult = value
    },
    updateSetBluetoothResult (state, value) {
      state.setBluetoothResult = value
    },
    updateQueryDeviceNetworkModeResult (state, value) {
      state.queryDeviceNetworkModeResult = value
    },
    updateSetDeviceNetworkModeResult (state, value) {
      state.setDeviceNetworkModeResult = value
    },
    updateQueryDeviceSipInfoResult (state, value) {
      state.queryDeviceSipInfoResult = value
    },
    updateSetDeviceSipInfoResult (state, value) {
      state.setDeviceSipInfoResult = value
    },
    updateQueryDeviceInformationPubResult (state, value) {
      state.queryDeviceInformationPubResult = value
    },
    updateSetDeviceInformationPubResult (state, value) {
      state.setDeviceInformationPubResult = value
    },
    updateSetMonitorInfoResult (state, value) {
      state.setMonitorInfoResult = value
    },
    updateAddCustomMonitorResult(state, value) {
      state.addCustomMonitorResult = value
    },
    updateDelMonitorInfoResult (state, value) {
      state.delMonitorInfoResult = value
    },
    updateSwitchMonitorResult (state, value) {
      state.switchMonitorResult = value
    },
    updateSetRemoteControllerTaskResult (state, value) {
      state.setRemoteControllerTaskResult = value
    },
    updateSetRemoteControllerKeyResult (state, value) {
      state.setRemoteControllerKeyResult = value
    },
    UpdateStartManualAlarmResult (state, value) {
      state.startManualAlarmResult = value
    },
    updateSetUserStorageCapacityResult (state, value) {
      state.setUserStorageCapacityResult = value
    },
    updateSetUserAuthorityResult (state, value) {
      state.setUserAuthorityResult = value
    },
    updateIntercomSetResult (state, value) {
      state.intercomSetResult = value
    },
    updateIntercomQueryResult (state, value) {
      state.intercomQueryResult = value
    },
    updateQueryTriggerResult (state, value) {
      state.queryTriggerResult = value
    },
    updateSetTriggerResult (state, value) {
      state.setTriggerResult = value
    },
    updateQueryAudioCollectorParmResult (state, value) {
      state.queryAudioCollectorParmResult = value
    },
    updateSetAudioCollectorParmResult (state, value) {
      state.setAudioCollectorParmResult = value
    },
    updateQueryAudioMixerParmResult (state, value) {
      state.queryAudioMixerParmResult = value
    },
    updateSetAudioMixerParmResult (state, value) {
      state.setAudioMixerParmResult = value
    },
    updateQueryPhoneGatewayParmResult (state, value) {
      state.queryPhoneGatewayParmResult = value
    },
    updateSetPhoneGatewayParmResult (state, value) {
      state.setPhoneGatewayParmResult = value
    },
    updateQueryAmpControlerParmResult (state, value) {
      state.queryAmpControlerParmResult = value
    },
    updateQueryNoiseDetectorParmResult (state, value) {
      state.queryNoiseDetectorParmResult = value
    },
    updateSetNoiseDetectorParmResult (state, value) {
      state.setNoiseDetectorParmResult = value
    },
    closeWebsocket (state) {
      if (state.websocket != null) {
        state.wsStatus = null
        state.websocket.close()
        state.websocket = null
      }
    },
    collapseAllSongList (state) {
      state.playList.forEach(list => {
        list.active = false
      })
    },
    // 重置zonesMac信息
    resetZonesMac (state) {
      state.zonesMacObject = Object.create(null)
    },
    // 重置userInfo信息
    resetUserInfoData (state) {
      state.userInfoData = null
    },
    /**
     * 重置歌曲上传进度
     * @param state
     */
    resetUploadProgress (state) {
      state.uploadSongProgress = 0
      state.uploadSongArrayProgress = []
    },
    // 日志管理
    updateRunLogPath (state, value) {
      state.runLogPath = value
    },
    // 通话录音相关
    updateCallRecordSwitchResult (state, value) {
      state.callRecordSwitchResult = value
    },
    updateCallRecordSwitch (state, value) {
      state.callRecordSwitch = value
    },
    updateQueryCallLogResult (state, value) {
      state.queryCallLogResult = value
    },
    updateCallLogs (state, value) {
      state.callLogs = value
    },
  },
  actions: {
    WEBSOCKET_INIT ({ commit }, url) {
      commit('WEBSOCKET_INIT', url)
    },
    WEBSOCKET_SEND ({ commit }, p) {
      commit('WEBSOCKET_SEND', p)
    },
    WEBSOCKET_CLOSE ({ commit }) {
      commit('WEBSOCKET_CLOSE')
    },
    setUsername ({ commit }, p) {
      commit('SET_USERNAME', p)
    },
    setPassword ({ commit }, p) {
      commit('SET_PASSWORD', p)
    },
    logout ({ commit }) {
      commit('logout')
    },
    resetCartState ({ commit }) {
      commit('resetState')
    },
  },
  getters: {
    allTypeZones: state => {
      const list = getAllTypeDeviceMappingList();
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => list.includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && list.includes(zone.device_model))
    },
    // 获取所有在线的分区
    onlineZones: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => zone.source !== -1 && (getDeviceModelMappingList(0).includes(zone.device_model)))
      }
      // 对于操作员，只显示自己的分区
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && zone.source !== -1 && (getDeviceModelMappingList(0).includes(zone.device_model)));
    },
    // 解码分区
    decodeZones: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => (getDeviceModelMappingList(0).includes(zone.device_model)))
      }
      // 对于操作员，只显示自己的分区
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(0).includes(zone.device_model)))
    },
    // 所有的寻呼台设备
    pagers: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => (getDeviceModelMappingList(1).includes(zone.device_model)))
      }
      // 对于操作员，只显示自己的寻呼台设备
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(1).includes(zone.device_model)))
    },
    // 在线的寻呼台设备
    onlinePagers: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => zone.source !== -1 && (getDeviceModelMappingList(1).includes(zone.device_model)))
      }
      // 对于操作员，只显示自己的寻呼台设备
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && zone.source !== -1 && (getDeviceModelMappingList(1).includes(zone.device_model)))
    },
    // 所有的音频采集器
    audioCollectors: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => (getDeviceModelMappingList(3).includes(zone.device_model)))
      }
       // 对于操作员，只显示自己的音频采集器设备
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(3).includes(zone.device_model)))
    },
    // 在线的音频采集器
    onlineAudioCollectors: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => zone.source !== -1 && (getDeviceModelMappingList(3).includes(zone.device_model)))
      }
      // 对于操作员，只显示自己的音频采集器设备
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && zone.source !== -1 && (getDeviceModelMappingList(3).includes(zone.device_model)))
    },
    // 所有的消防采集器
    fireCollectors: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => (getDeviceModelMappingList(4).includes(zone.device_model)))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(4).includes(zone.device_model)))
    },
    // 在线的消防采集器
    onlineFireCollectors: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => zone.source !== -1 && (getDeviceModelMappingList(4).includes(zone.device_model)))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && zone.source !== -1 && (getDeviceModelMappingList(4).includes(zone.device_model)))
    },
    //所有的电源时序器
    sequencePowers: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => getDeviceModelMappingList(5).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(5).includes(zone.device_model)))
    },
    //在线的电源时序器
    onlineSequencePowers: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => zone.source !== -1 && getDeviceModelMappingList(5).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && zone.source !== -1 && (getDeviceModelMappingList(5).includes(zone.device_model)))
    },
    //所有的音频协处理器
    audioMixers: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => getDeviceModelMappingList(6).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(6).includes(zone.device_model)))
    },
    //在线的音频协处理器
    onlineAudioMixers: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => zone.source !== -1 && getDeviceModelMappingList(6).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && zone.source !== -1 && (getDeviceModelMappingList(6).includes(zone.device_model)))
    },
    //所有的远程遥控器
    remoteControlers: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => getDeviceModelMappingList(7).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(7).includes(zone.device_model)))
    },
    //在线的远程遥控器
    onlineRemoteControlers: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => zone.source !== -1 && getDeviceModelMappingList(7).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && zone.source !== -1 && (getDeviceModelMappingList(7).includes(zone.device_model)))
    },
    //所有的电话网关
    phoneGateways: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => getDeviceModelMappingList(8).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(8).includes(zone.device_model)))
    },
    //在线的电话网关
    onlinePhoneGateways: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => zone.source !== -1 && getDeviceModelMappingList(8).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && zone.source !== -1 && (getDeviceModelMappingList(8).includes(zone.device_model)))
    },
    //所有的功放控制器
    ampControlers: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => getDeviceModelMappingList(9).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(9).includes(zone.device_model)))
    },
    //在线的功放控制器
    onlineAmpControlers: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => zone.source !== -1 && getDeviceModelMappingList(9).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && zone.source !== -1 && (getDeviceModelMappingList(9).includes(zone.device_model)))
    },
    //所有的噪声自适应终端
    noiseDetectors: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => getDeviceModelMappingList(10).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(10).includes(zone.device_model)))
    },
    //在线的噪声自适应终端
    onlineNoiseDetectors: state => {
      if (state.user == null || state.user === 'admin') {
        return state.eventList.filter(zone => zone.source !== -1 && getDeviceModelMappingList(10).includes(zone.device_model))
      }
      const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
      return state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && zone.source !== -1 && (getDeviceModelMappingList(10).includes(zone.device_model)))
    },

    // 通用成功信息
    apiSuccessMsg: state => {
      return state.apiName + i18n.t('store.success')
    },
    isAdmin: state => {
      return state.user === 'admin'
    },
    isWindowsServer: state => {
      return state.systemType === 2
    },
    isServerRegistered: state => {
      return state.type !== 1
    },
    myGroupList: state => {
      const username = state.user
      // 管理员账户，显示所有用户的分组
      if (username == null || username === 'admin') {
        return state.groupList
      }
      // 对于操作员，只显示自己用户创建的分组
      return state.groupList.filter(group => group.group_account === username)
    },
    isAudioMonitorZoneOnline: state => {
      if (state.audioMonitorDeviceMac == null || state.audioMonitorDeviceMac === '') {
        return false
      }
      return state.eventList.filter(e => e.mac === state.audioMonitorDeviceMac && e.source !== -1).length > 0
    },
    // 今日定时点列表
    todayTimerInfoList: state => {
      if (state.todayTimerList.length === 0 || state.timerList.length === 0) {
        return []
      }
      const timerInfoList = []
      // 获取当前生效的定时方案
      const currentTimerSchema = state.timerList.find(timer => timer.time_scheme_id === state.currentSchemaId)
      // 获取当前生效定时方案的定时列表
      const currentTimerList = currentTimerSchema.timerlists
      // ******** 非管理员只显示当前账户创建的定时点信息
      // ******** 增加权限控制，如果有定时点权限，可显示/管理所有用户的定时点
      const userTimerList = (state.user === 'admin' || checkIfTimerAllowed(state.authority))
        ? currentTimerList
        : currentTimerList.filter(list => list.account === state.user)
      state.todayTimerList.forEach((todayTimer, index) => {
        // 根据今日定时点的id查找对应的定时信息并填充
        const timer = userTimerList.find(t => t.id === todayTimer.id)
        if (timer == null) {
          return
        }
        const combinedTimerInfo = {
          id: index + 1,
          name: timer.name,
          start_time: timer.start_time,
          end_time: timer.end_time,
          play_mode: timer.play_mode,
          volume: timer.volume,
          sections: timer.sections,
          groups: timer.groups,
          songs: timer.songs,
          status: todayTimer.status,
          timer_id: timer.id,
          source_type: timer.source_type,
          device_type: timer.device_type,
          audio_collector: timer.audio_collector,
          sequence_powers: timer.sequence_powers
        }
        timerInfoList.push(combinedTimerInfo)
      })
      // 清空今日定时任务列表，待接收到新的任务列表后才继续该循环，确保timer不会查找不到
      // state.todayTimerList = []
      return timerInfoList
    },
    // 手动任务列表
    myManualTaskInfo: state => {
      // 管理员账户，显示所有用户的手动任务
      if (state.user === 'admin') {
        return state.manualTaskInfo
      }
      // 如果是非管理员，只显示属于自己用户的任务
      return state.manualTaskInfo.filter(task => task.account === state.user)
    },
    // 本地歌曲
    mySongListInfo: state => {
      // 管理员账户，显示所有用户的本地歌曲
      if (state.user === 'admin') {
        return state.localSongListInfo
      }
      // 如果是非管理员，但是具有管理歌曲列表的权限，显示所有用户的本地歌曲
      if(state.authority & USER_LIMITS_PLAYLIST)
        return state.localSongListInfo
      // 如果是非管理员，没有权限，只显示自己用户和管理员用户的歌曲
      return state.localSongListInfo.filter(task => task.account === state.user || task.account === 'admin')
    },
    // 用户对应权限校验（播放列表，定时点，音频采集器）
    isPlaylistAuthorizedUser: state => {
      return checkIfPlaylistAllowed(state.authority)
    },
    isTimerAuthorizedUser: state => {
      return checkIfTimerAllowed(state.authority)
    },
    isAudioCollectorAuthorizedUser: state => {
      return checkIfAudioCollectorAllowed(state.authority)
    },
  },
})

export function checkIfPlaylistAllowed(authority) {
  return ((authority & USER_LIMITS_PLAYLIST) !== 0)
}
export function checkIfTimerAllowed(authority) {
  return ((authority & USER_LIMITS_TIMER) !== 0)
}
export function checkIfAudioCollectorAllowed(authority) {
  return ((authority & USER_LIMITS_AUDIO_COLLECTOR) !== 0)
}

export function checkIfRepeatedLoginAllowed(authority) {
  return ((authority & USER_LIMITS_REPEATED_LOGIN) !== 0)
}

export function checkIfCreateSubUserAllowed(authority) {
  return ((authority & USER_LIMITS_CREATE_SUB_USER) !== 0)
}

// 根据歌曲位置判断类型为TTS还是音乐
export function getSongType(songPathName) {
  return (songPathName != null && songPathName.startsWith('/Data/Program/Common/Music/')) ? 'TTS' : i18n.t('store.music')
}
// 如果列表中歌曲的song_account为空，则设置为默认值admin(兼容旧的后台程序）
export function getSongAccount(songAccount) {
  return (songAccount == null || songAccount === '') ? 'admin' : songAccount
}

export function filterListSongsBySongAccount(user, authority, listName, songs) {
  if (user === 'admin') {
    return songs
  }
  if (authority & USER_LIMITS_PLAYLIST) {
    return songs
  }
  if (listName !== 'TTS List') {
    return songs;
  }
  // 非管理员用户只显示管理员和自己的TTS歌曲
  return songs.filter(song => song.song_account === user || song.song_account === 'admin')
}

function filterSequencePowerInfoByAccount(state) {
  if (state.sequencePowerInfo.length === 0 || state.user == null || state.user === 'admin') {
    return
  }
  const zones = state.zonesMacObject[state.user] ? state.zonesMacObject[state.user] : []
  const mySequencePowerList=state.eventList.filter(zone => zones.indexOf(zone.mac) !== -1 && (getDeviceModelMappingList(5).includes(zone.device_model)))
  const macList = mySequencePowerList.map(zone => zone.mac)
  state.sequencePowerInfo=state.sequencePowerInfo.filter( x=> macList.includes(x.device_mac) )
}


function updateStorageCapacityOrAuthorityForAccount(state, account, storageCapacity, authority) {
  if (state.userInfoData == null || state.userInfoData.length === 0) {
    return
  }
  if (account == null) {
    return;
  }
  // 1. find the account by state and account
  const userInfoData = state.userInfoData;
  let targetUser;
  if (userInfoData.account === account) {
    targetUser = userInfoData
  } else if (userInfoData.sub_users.find(u => u.account === account)) {
    // 2级账号
    targetUser = userInfoData.sub_users.find(u => u.account === account);
  } else {
    // 3级账号
    outer:
    for (let i = 0; i < userInfoData.sub_users.length; i++) {
      if (userInfoData.sub_users[i].sub_users && userInfoData.sub_users[i].sub_users.length > 0) {
        const tripleSubUsers = userInfoData.sub_users[i].sub_users;
        for (let j = 0; j < tripleSubUsers.length; j++) {
          if (tripleSubUsers[j].account === account) {
            targetUser = tripleSubUsers[j]
            break outer;
            // 4级账号
          } else if (tripleSubUsers[j].sub_users && tripleSubUsers[j].sub_users.length > 0) {
            const fourthSubUsers = tripleSubUsers[j].sub_users;
            for (let k = 0; k < fourthSubUsers.length; k++) {
              if (fourthSubUsers[k].account === account) {
                targetUser = fourthSubUsers[j]
                break outer;
              }
            }
          }
        }
      }
    }
  }
  if (targetUser == null) {
    // not found account
    return;
  }
  // update the account for the capacity or authority
  if (storageCapacity != null) {
    targetUser.storage_capacity = storageCapacity
  }
  if (authority != null) {
    targetUser.authority = authority
    targetUser.user_authority.authority = authority
  }
}

// function CommonException (message) {
//   this.message = message
//   this.name = 'CommonException'
// }
