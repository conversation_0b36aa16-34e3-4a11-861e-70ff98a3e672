<template>
  <div>
    <!--对话框-->
    <v-dialog
      v-model="renameDeviceDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.editDeviceName') }}</span>
        </v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col
                cols="12"
              >
                <v-text-field
                  v-model="newDeviceName"
                  :label="$t('partitionSystem.labels.enterNewName')"
                  required
                  hide-details
                />
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="renameDeviceDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            :loading="updateDeviceInfoLoading"
            :disabled="updateDeviceInfoLoading"
            @click="renameDevice"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="deviceUpdateDialog"
      max-width="500px"
      transition
      persistent
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.deviceUpgrade') }}</span>
        </v-card-title>
        <v-card-text>
          <v-container>
            <v-row dense>
              <v-col
                cols="12"
              >
                <span style="margin-right: 20px">{{ $t('partitionSystem.labels.deviceName') }}:</span>
                <span>{{ getDevice.name }}</span>
              </v-col>
              <v-col
                cols="12"
              >
                <span style="margin-right: 20px">{{ $t('partitionSystem.labels.currentFirmwareVersion') }}:</span>
                <span>{{ getDevice.firmware_version }}</span>
              </v-col>
              <v-col
                cols="12"
              >
                <v-switch
                  v-model="updateFirmwareFromServeSwitch"
                  :label="$t('partitionSystem.labels.selectServerFirmware')"
                  hide-details
                  dense
                />
                <v-switch
                  v-show="updateFirmwareFromServeSwitch"
                  v-model="onlyUpdateSelectedDeviceSwitch"
                  :label="$t('partitionSystem.labels.upgradeCurrentDeviceOnly')"
                  hide-details
                  dense
                />
              </v-col>
              <v-col v-if="updateFirmwareFromServeSwitch" cols="12">
                <v-list
                  dense
                  subheader
                >
                  <v-subheader style="font-size: 0.90em">{{ $t('partitionSystem.labels.serverFirmware') }}</v-subheader>
                  <v-list-item-group v-if="filterFirmwares.length > 0" color="primary">
                    <v-list-item
                      v-for="(firmware, i) in filterFirmwares"
                      :key="i"
                      :input-value="selectedFirmware === firmware"
                      @click.native="firmwareChange(firmware)"
                    >
                      <v-list-item-content>
                        <v-list-item-title style="font-size: 0.90em; padding-bottom: 3px;" v-html="firmware" />
                      </v-list-item-content>
                    </v-list-item>
                  </v-list-item-group>
                  <v-list-item-group v-else color="primary">
                    <v-list-item
                      v-for="(firmware, i) in emptyFirmwareArrary"
                      :key="i"
                      disabled
                    >
                      <v-list-item-content>
                        <v-list-item-title style="font-size: 0.90em" v-html="firmware" />
                      </v-list-item-content>
                    </v-list-item>
                  </v-list-item-group>
                </v-list>
              </v-col>
              <v-col v-if="!updateFirmwareFromServeSwitch" cols="12">
                <v-file-input
                  v-model="uploadFile"
                  :label="$t('partitionSystem.labels.uploadNewFirmware')"
                  style="padding-top: 5px"
                  clearable
                  counter
                  show-size
                />
              </v-col>
              <v-col cols="12">
                <v-progress-circular
                  v-if="!updateFirmwareFromServeSwitch && uploadFirmwareProgress !== 0"
                  class="ml-16"
                  :rotate="0"
                  :size="100"
                  :width="25"
                  :value="uploadFirmwareProgress"
                  color="primary"
                >
                  {{ uploadFirmwareProgress }}
                </v-progress-circular>
              </v-col>
              <v-col cols="12">
                <v-progress-circular
                  v-if="updateFirmwareFromServeSwitch && deviceUpgradeProgress !== 0"
                  class="ml-16"
                  :rotate="0"
                  :size="100"
                  :width="25"
                  :value="deviceUpgradeProgress"
                  color="primary"
                >
                  {{ deviceUpgradeProgress }}
                </v-progress-circular>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions style="padding-top: 0px">
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="deviceUpdateDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
          <v-btn
            v-if="!updateFirmwareFromServeSwitch"
            color="blue darken-1"
            text
            :loading="uploadLoading"
            :disabled="uploadLoading"
            @click="uploadFirmware"
          >
            {{ $t('common.upload') }}
          </v-btn>
          <v-btn
            v-else
            color="blue darken-1"
            text
            @click="upgradeDevice"
          >
            {{ $t('partitionSystem.buttons.upgrade') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="editDeviceIpDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.modifyDeviceIP') }}</span>
        </v-card-title>
        <v-card-text>
          <v-text-field
            :value="getDevice.name"
            :label="$t('partitionSystem.labels.deviceName')"
            disabled
            class="pt-6"
            hide-details
          />
          <v-text-field
            :value="getDevice.mac"
            label="MAC"
            disabled
            class="pt-6"
            hide-details
          />
          <v-switch
            v-model="autoGetIpAddress"
            :label="$t('partitionSystem.labels.autoGetIpAddress')"
          />
          <div v-if="!autoGetIpAddress">
            <v-text-field
              v-model="newIpAddress"
              :label="$t('partitionSystem.labels.ipAddress')"
              class="pt-6"
              hide-details
            />
            <v-text-field
              v-model="subnetMark"
              :label="$t('partitionSystem.labels.subnetMask')"
              class="pt-6"
              hide-details
            />
            <v-text-field
              v-model="gateway"
              :label="$t('partitionSystem.labels.defaultGateway')"
              class="pt-6"
              hide-details
            />
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="editDeviceIpDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="updateIpAddress(autoGetIpAddress)"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--对讲设置-->
    <v-dialog
      v-model="editIntercomDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title style="font-size: 2.0em;">{{ $t('partitionSystem.dialogs.intercomSettings') }}</v-toolbar-title>
        </v-toolbar>
        <v-container>
          <v-row>
            <v-col
              cols="12"
              class="mt-2"
              >
              <v-select
                v-model="key1MacForIntercom"
                :items="intercomItems"
                :label="$t('partitionSystem.labels.key1')"
                :menu-props="{offsetY: true, closeOnClick: true}"
                item-text="name"
                item-value="mac"
                dense
                outlined
              />
            </v-col>
            <v-col
              cols="12"
              class="mt-n7"
            >
              <v-select
                v-model="key2MacForIntercom"
                :items="intercomItems"
                :label="$t('partitionSystem.labels.key2')"
                :menu-props="{offsetY: true, closeOnClick: true}"
                item-text="name"
                item-value="mac"
                dense
                outlined
              />
            </v-col>
            <v-col
              cols="12"
              class="mt-n2"
            >
              <v-select
                v-model="intercomMicVolumeLevel"
                :items="intercomVolumeLevelItems"
                :label="$t('partitionSystem.labels.micInputVolumeLevel')"
                :menu-props="{offsetY: true, closeOnClick: true}"
                item-text="name"
                item-value="mac"
                dense
                outlined
              />
            </v-col>
            <v-col
              cols="12"
              class="mt-n7"
            >
              <v-select
                v-model="intercomFarOutPutVolumeLevel"
                :items="intercomVolumeLevelItems"
                :label="$t('partitionSystem.labels.intercomOutputVolumeLevel')"
                :menu-props="{offsetY: true, closeOnClick: true}"
                item-text="name"
                item-value="mac"
                dense
                outlined
              />
            </v-col>
          </v-row>
        </v-container>
        <v-card-actions class="mt-n6">
          <v-switch
            v-if="isAdmin"
            v-model="isSetIntercomToAllModelDevice"
            :label="$t('partitionSystem.labels.setAllSameTypeDevices')"
          />
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="editIntercomDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="saveIntercomSettings()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--触发设置-->
    <v-dialog
      v-model="editTriggerDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title style="font-size: 2.0em;">{{ $t('partitionSystem.dialogs.triggerSettings') }}</v-toolbar-title>
        </v-toolbar>
        <v-container>
          <v-row>
            <v-col cols="2" class="mt-1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.triggerSwitch') }}</span>
            </v-col>
            <v-col cols="10">
              <v-radio-group
                v-model="editTriggerSwitch"
                row
                class="mt-n1"
              >
                <v-radio
                  :label="$t('common.close')"
                  color="primary"
                  :value="Number(0)"
                />
                <v-radio
                  :label="$t('partitionSystem.labels.enable')"
                  color="primary"
                  :value="Number(1)"
                />
              </v-radio-group>
            </v-col>
            <v-col cols="2" class="mt-n3" v-show="editTriggerSwitch === 1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.triggerMode') }}</span>
            </v-col>
            <v-col cols="10" class="mt-n3" v-show="editTriggerSwitch === 1">
              <v-radio-group
                v-model="editTriggerMode"
                row
                class="mt-n1"
              >
                <v-radio
                  :label="$t('partitionSystem.labels.levelTrigger')"
                  color="primary"
                  :value="Number(0)"
                />
                <v-radio
                  :label="$t('partitionSystem.labels.shortCircuitTrigger')"
                  color="primary"
                  :value="Number(1)"
                />
              </v-radio-group>
            </v-col>
            <v-col cols="12" class="mt-n3" v-show="editTriggerSwitch === 1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.triggerSong') }}</span>
              <span style="padding-left: 30px" />
              <span v-if="editTriggerSongPathName == null || editTriggerSongPathName === ''">{{ $t('common.none') }}</span>
              <span v-else>{{ editTriggerSongPathName.substring(editTriggerSongPathName.lastIndexOf('/') + 1) }}</span>
              <span style="padding-left: 30px" />
              <v-btn
                color="primary"
                width="20"
                height="20"
                @click="preUpdateTriggerSong"
              >
                {{ $t('common.edit') }}
              </v-btn>
            </v-col>
            <v-col cols="2" class="mt-1" v-show="editTriggerSwitch === 1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.triggerVolume') }}</span>
            </v-col>
            <v-col cols="10" v-show="editTriggerSwitch === 1">
              <v-slider
                :value="editTriggerVolume"
                @change.passive="v => editTriggerVolume = v"
                append-icon="mdi-volume-plus"
                prepend-icon="mdi-volume-minus"
                @click:append="plusTriggerVolume"
                @click:prepend="minusTriggerVolume"
                :thumb-size="24"
                thumb-label="always"
              ></v-slider>
            </v-col>
          </v-row>
        </v-container>
        <v-card-actions class="mt-n6">
          <v-switch
            v-if="isAdmin"
            v-model="isSetTriggerToAllModelDevice"
            :label="$t('partitionSystem.labels.setAllSameTypeDevices')"
          />
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="editTriggerDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="saveTriggerSettings()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="networkModeConfirmDialog"
      max-width="600px"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          <p>{{ $t('partitionSystem.dialogs.editNetworkMode') }}</p>
        </v-card-title>
        <v-card-text class="text-center">
          <span class="red--text text--darken-4 font-weight-black" style="font-size: 1.15em">
            {{ $t('partitionSystem.warnings.networkModeWarning') }}
          </span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="networkModeConfirmDialog = false"
            class="mb-3"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="updateNetworkMode()"
            class="mb-3"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="editNetworkModeDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.editNetworkMode') }}</span>
        </v-card-title>
        <v-card-text>
          <v-text-field
            :value="getDevice.name"
            :label="$t('partitionSystem.labels.deviceName')"
            disabled
            class="pt-6"
            hide-details
          />
          <v-text-field
            :value="getDevice.mac"
            label="MAC"
            disabled
            class="pt-6"
            hide-details
          />
          <v-radio-group
            v-model="deviceNetworkMode"
            row
            class="mt-4"
            hide-details
          >
            <span class="font-weight-bold">{{ $t('partitionSystem.labels.networkMode') }}</span>
            <v-radio
              label="UDP"
              color="primary"
              :value="Number(1)"
              class="pl-4 ml-4"
            />
            <v-radio
              label="TCP"
              color="primary"
              :value="Number(2)"
            />
          </v-radio-group>
          <div v-if="deviceNetworkMode === 2">
            <v-text-field
              v-model="deviceNetworkServerIp"
              :label="$t('partitionSystem.labels.mainServerIp')"
              hide-details
            />
            <v-text-field
              v-model="deviceNetworkServerPort"
              :label="$t('partitionSystem.labels.mainServerPort')"
              hide-details
            />
            <v-text-field
              v-model="deviceNetworkServerIp2"
              :label="$t('partitionSystem.labels.backupServerIp')"
              hide-details
            />
            <v-text-field
              v-model="deviceNetworkServerPort2"
              :label="$t('partitionSystem.labels.backupServerPort')"
              hide-details
            />
          </div>
        </v-card-text>
        <v-card-actions class="mt-n5">
          <v-switch v-model="isSetAllSameModelNetworkMode" :label="$t('partitionSystem.labels.setAllSameTypeDevices')" class="ml-2"/>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="editNetworkModeDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="preUpdateNetwork()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="editSipDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.sipSettings') }}</span>
        </v-card-title>
        <v-card-text>
          <v-text-field
            :value="getDevice.name"
            :label="$t('partitionSystem.labels.deviceName')"
            disabled
            class="pt-6"
            hide-details
          />
          <v-text-field
            :value="getDevice.mac"
            label="MAC"
            disabled
            class="pt-6"
            hide-details
          />
          <v-text-field
            :value="getSipStatusStr(deviceSipStatus)"
            :label="$t('common.status')"
            disabled
            class="pt-6"
            hide-details
          />
          <v-radio-group
            v-model="deviceSipEnable"
            row
            class="mt-4"
            hide-details
          >
            <span class="font-weight-bold">{{ $t('partitionSystem.labels.sipSwitch') }}</span>
            <v-radio
              :label="$t('common.close')"
              color="primary"
              :value="false"
              class="pl-4 ml-4"
            />
            <v-radio
              :label="$t('partitionSystem.labels.enable')"
              color="primary"
              :value="true"
            />
          </v-radio-group>

          <v-col  class="mt-5" v-show="deviceSipEnable">
            <span class="font-weight-bold">{{ $t('partitionSystem.labels.callVolume') }}</span>
          </v-col>
          <v-slider
            v-if="deviceSipEnable"
            class="mt-1"
            :value="deviceSipOutPutVolume"
            prepend-icon="mdi-volume-high"
            thumb-label="always"
            thumb-size="28"
            :min="10"
            :max="100"
            @change.passive="v => deviceSipOutPutVolume = v"
          />

          <v-radio-group
            v-if="deviceSipEnable"
            v-model="deviceSipProtocol"
            row
            class="mt-4"
            hide-details
          >
            <span class="font-weight-bold">{{ $t('partitionSystem.labels.transportProtocol') }}</span>
            <v-radio
              label="UDP"
              color="primary"
              :value="Number(0)"
              class="pl-4 ml-4"
            />
            <v-radio
              label="TCP"
              color="primary"
              :value="Number(1)"
            />
          </v-radio-group>

          <div v-if="deviceSipEnable" style="padding-top: 10px">
            <v-text-field
              v-model="deviceSipServerIp"
              :label="$t('partitionSystem.labels.serverIpAddress')"
              hide-details
            />
            <v-text-field
              v-model="deviceSipServerPort"
              :label="$t('partitionSystem.labels.serverPort')"
              hide-details
              type="number"
            />
            <v-text-field
              v-model="deviceSipAccount"
              :label="$t('partitionSystem.labels.sipAccount')"
              hide-details
            />
            <v-text-field
              v-model="deviceSipPassword"
              :label="$t('partitionSystem.labels.sipPassword')"
              hide-details
            />
          </div>
        </v-card-text>
        <v-card-actions class="mt-n5">
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="editSipDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="updateSipInfo()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="editInformationPubDialog"
      max-width="600px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.informationPublish') }}</span>
        </v-card-title>
        <v-card-text>
          <v-text-field
            :value="getDevice.name"
            :label="$t('partitionSystem.labels.deviceName')"
            disabled
            class="pt-6"
            hide-details
          />
          <v-text-field
            :value="getDevice.mac"
            label="MAC"
            disabled
            class="pt-6"
            hide-details
          />

          <div style="padding-top: 30px">

            <v-textarea
              v-model="deviceInformationPubText"
              filled
              :counter="calculateTextUTF8ByteLength(deviceInformationPubText)"
              clearable
              :rules="informationPubTextLengthRules()"
              height="120"
              :label="$t('partitionSystem.labels.enterDisplayContent')"
            />

            <v-select
              v-model="deviceInformationPubEffects"
              :items="localizedEffectsList"
              filled
              item-value="value"
              item-text="text"
              name="soundSelected"
              :label="$t('partitionSystem.labels.selectEffect')"
              class="pt-3 pb-1 section-high-class"
            />

            <v-text-field
              v-model="deviceInformationPubSpeed"
              :label="$t('partitionSystem.labels.moveSpeed')"
              hide-details
              type="number"
            />
            <v-text-field
              v-model="deviceInformationPubStayTime"
              :label="$t('partitionSystem.labels.stayTime')"
              hide-details
              type="number"
            />
          </div>
          <v-radio-group
            v-model="deviceInformationPubDisplayEnable"
            row
            class="mt-4"
            hide-details
          >
            <span class="font-weight-bold">{{ $t('partitionSystem.labels.displaySwitch') }}</span>
            <v-radio
              :label="$t('common.close')"
              color="primary"
              :value="false"
              class="pl-4 ml-4"
            />
            <v-radio
              :label="$t('partitionSystem.labels.enable')"
              color="primary"
              :value="true"
            />
          </v-radio-group>

        </v-card-text>
        <v-card-actions class="mt-n5">
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="editInformationPubDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="updateInformationPublish()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!--噪声自适应终端设置-->
    <v-dialog
      v-model="editNoiseDetectorDialog"
      max-width="900px"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title style="font-size: 2.0em;">{{ $t('partitionSystem.dialogs.noiseAdaptiveSettings') }}</v-toolbar-title>
        </v-toolbar>
        <v-container>
          <v-row>
            <!-- 设备信息 -->
            <v-col cols="12">
              <v-text-field
                :value="getDevice.name"
                :label="$t('partitionSystem.labels.deviceName')"
                disabled
                hide-details
              />
            </v-col>

            <!-- 启用/关闭开关 -->
            <v-col cols="12" class="mt-2">
              <v-switch
                v-model="editNoiseDetectorSwitch"
                :label="$t('partitionSystem.labels.enableNoiseAdaptive')"
                color="primary"
                hide-details
              />
            </v-col>

            <!-- 噪声检测器通道显示 -->
            <v-col cols="12" class="mt-4" v-show="editNoiseDetectorSwitch">
              <v-card outlined>
                <v-card-title class="pb-2">
                  <span class="text-h6">{{ $t('partitionSystem.labels.noiseDetectorChannels') }}</span>
                </v-card-title>
                <v-card-text>
                  <v-row>
                    <v-col
                      v-for="(channel, index) in 8"
                      :key="index"
                      cols="3"
                      class="text-center"
                    >
                      <v-card
                        :color="getChannelColor(index)"
                        :class="getChannelClass(index)"
                        height="80"
                        class="d-flex align-center justify-center"
                      >
                        <div>
                          <div class="text-h6 font-weight-bold">CH{{ index + 1 }}</div>
                          <div v-if="isChannelValid(index)" class="text-body-2">
                            {{ getChannelNoiseValue(index) }}dB
                          </div>
                          <div v-else class="text-body-2">
                            {{ $t('partitionSystem.labels.inactive') }}
                          </div>
                        </div>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- 平均噪声值显示 -->
            <!--
            <v-col cols="12" class="mt-2" v-show="editNoiseDetectorSwitch && getValidChannelsCount() > 0">
              <v-card outlined color="info">
                <v-card-text class="text-center">
                  <div class="text-h5 font-weight-bold">
                    {{ $t('partitionSystem.labels.averageNoiseLevel') }}: {{ getAverageNoiseLevel() }}dB
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
            -->

            <!-- 噪声/音量设置 -->
            <v-col cols="12" class="mt-4" v-show="editNoiseDetectorSwitch">
              <v-card outlined>
                <v-card-title class="pb-2">
                  <span class="text-h6">{{ $t('partitionSystem.labels.noiseVolumeSettings') }}</span>
                </v-card-title>
                <v-card-text>
                  <!-- 8段式调节控制 -->
                  <v-row>
                    <v-col
                      v-for="(segment, index) in 8"
                      :key="index"
                      cols="3"
                      class="text-center"
                    >
                      <div class="segment-control">
                      <!--
                        <div class="text-caption font-weight-bold mb-2">
                          {{ $t('partitionSystem.labels.segment') }} {{ index + 1 }}
                        </div>
                      -->

                        <!-- 固定噪声值显示 -->
                        <v-card
                          flat
                          color="grey lighten-4"
                          class="mb-2 pa-2 text-center"
                        >
                          <div class="text-caption text--secondary">
                            {{ $t('partitionSystem.labels.noiseLevel') }}
                          </div>
                          <div class="text-h6 font-weight-bold primary--text">
                            {{ getFixedNoiseValue(index) }} dB
                          </div>
                        </v-card>

                        <!-- 音量值输入 -->
                        <v-text-field
                          v-model.number="editNoiseDetectorVolumeArray[index]"
                          :label="$t('partitionSystem.labels.volumeLevel')"
                          type="number"
                          :min="0"
                          :max="100"
                          suffix="%"
                          dense
                          outlined
                          hide-details
                        />
                      </div>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- 分区绑定 -->
            <v-col cols="12" class="mt-4" v-show="editNoiseDetectorSwitch">
              <v-card outlined>
                <v-card-title class="pb-2">
                  <span class="text-h6">{{ $t('partitionSystem.labels.bindPartitions') }}</span>
                </v-card-title>
                <v-card-text>
                  <div class="mb-2">
                    {{ $t('partitionSystem.labels.selectedPartitions') }}: {{ noiseDetectorPartitionSelectTransfer.length }}
                  </div>
                  <v-btn
                    color="primary"
                    @click="noiseDetectorPartitionSelectDialog = true"
                  >
                    {{ $t('partitionSystem.buttons.selectPartitions') }}
                  </v-btn>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-container>

        <v-card-actions class="mt-n6">
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="editNoiseDetectorDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="saveNoiseDetectorSettings()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!--噪声自适应终端分区选择对话框-->
    <v-dialog
      v-model="noiseDetectorPartitionSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.partitionSelection') }}</span>
        </v-card-title>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="noiseDetectorPartitionSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('partitionSystem.placeholders.enterPartitionKeyword')"
            :data="myZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('partitionSystem.labels.partitionList'), $t('partitionSystem.labels.selectedPartitions')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="noiseDetectorPartitionSelectDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!--设备音量-->
    <v-dialog
      v-model="editVolumeDialog"
      max-width="520px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.advancedVolumeSettings') }}</span>
        </v-card-title>
        <v-card-subtitle class="pt-9 text-lg-h3 text-center">
          <span>{{ $t('partitionSystem.tips.volumeCalculation') }}</span>
        </v-card-subtitle>
        <v-card-text class="mt-n5">
          <v-text-field
            :value="getDevice.name"
            :label="$t('partitionSystem.labels.deviceName')"
            disabled
            class="pt-6"
            hide-details
          />
          <v-text-field
            :value="getDevice.mac"
            label="MAC"
            disabled
            class="pt-6"
            hide-details
          />
          <v-row>
            <v-col cols="3" class="mt-7">
                <span class="font-weight-bold">{{ $t('partitionSystem.labels.globalSubVolume') }}</span>
            </v-col>
            <v-slider cols="10"
              class="mt-10 mr-2"
              :value="subVolume"
              prepend-icon="mdi-volume-high"
              thumb-label="always"
              thumb-size="28"
              :min="0"
              :max="100"
              @change.passive="v => subVolume = v"
            />
          </v-row>
          <v-row>
            <v-col cols="3" class="mt-1">
                <span class="font-weight-bold">{{ $t('partitionSystem.labels.localVolume') }}</span>
            </v-col>
            <v-slider cols="6"
              class="mt-4 mb-5 mr-2"
              :value="auxVolume"
              prepend-icon="mdi-volume-high"
              thumb-label="always"
              thumb-size="28"
              :min="0"
              :max="100"
              @change.passive="v => auxVolume = v"
            />
          </v-row>
        </v-card-text>
        <v-card-actions class="mt-n9">
          <v-switch v-if="isAdmin" v-model="isSetAllSameModelVolume" :label="$t('partitionSystem.labels.setAllSameTypeDevices')" class="ml-2"/>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="editVolumeDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="editVolume()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--任务管理对话框-->
    <v-dialog
      v-model="taskManagerDialog"
      max-width="55%"
      transition
    >
      <v-card>
        <v-card-title class="text-left">
          <v-row>
            <v-col cols="8">
              <span class="text-lg-h3">{{ $t('partitionSystem.dialogs.taskManagement') }}</span>
              <span style="padding-left: 30px" />
              <v-btn
                color="primary"
                @click="preCreateTask"
              >
                {{ $t('partitionSystem.buttons.createTask') }}
              </v-btn>
              <v-btn
                v-show="taskSelected.length > 0"
                color="primary"
                @click="preEditTask"
              >
                {{ $t('partitionSystem.buttons.editTask') }}
              </v-btn>
              <v-btn
                v-show="taskSelected.length > 0"
                color="primary"
                @click="preDeleteTask"
              >
                {{ $t('partitionSystem.buttons.deleteTask') }}
              </v-btn>
            </v-col>
            <v-col cols="4">
              <v-text-field
                v-model="taskSearch"
                append-icon="mdi-magnify"
                :label="$t('common.search')"
                single-line
                hide-details
                style="padding-bottom: 35px; padding-top: 0"
              />
            </v-col>
          </v-row>
        </v-card-title>
        <!--todo id处理-->
        <v-data-table
          v-model="taskSelected"
          :headers="localizedTaskHeaders"
          :items="remoteControllerTasks"
          :search="taskSearch"
          sort-by.sync="['task_id']"
          item-key="task_id"
          class="elevation-1"
          single-select
          :loading="remoteControllerTasks.length === 0"
          :loading-text="$t('partitionSystem.placeholders.noTasksSet')"
          style="margin-top: -10px"
          :items-per-page="10"
          @click:row="rowTaskClick"
          :footer-props="{
            itemsPerPageOptions: [5,10,20,50],
            showFirstLastPage: true,
            showCurrentPage: true,
            firstIcon: 'mdi-arrow-collapse-left',
            lastIcon: 'mdi-arrow-collapse-right',
            prevIcon: 'mdi-minus',
            nextIcon: 'mdi-plus'
          }"
        >
          <template v-slot:item.task_id="{ item }">
            <span>{{ remoteControllerTasks.map(function(x) {return x.task_id; }).indexOf(item.task_id) + 1 }}</span>
          </template>
          <template v-slot:item.play_mode="{ item }">
            <span>{{ $util.getPlayMode(item.play_mode) }}</span>
          </template>
          <template v-slot:item.song_pathnames="{ item }">
            <v-tooltip bottom open-on-hover open-delay="500" v-show="item.source_type == null || item.source_type === 0">
              <template v-slot:activator="{ attr, on }">
                <span v-bind="attr" v-on="on">{{ $util.getTimerSongs(item.song_pathnames) }}</span>
              </template>
              <span>{{ $util.getSongNames(item.song_pathnames) }}</span>
            </v-tooltip>
            <span v-show="item.source_type === 1">
              {{ $util.getAudioCollectorDescription($t('partitionSystem.tooltips.audioCollection'),
              item.audio_collector == null ? null : item.audio_collector.device_mac,
              item.audio_collector == null ? null : item.audio_collector.channel) }}
            </span>
          </template>
          <template v-slot:item.zone_macs="{ item }">
            <v-tooltip bottom open-on-hover open-delay="500">
              <template v-slot:activator="{ attr, on }">
                <span v-bind="attr" v-on="on">{{ $util.getTimerSections(item.zone_macs) }}</span>
              </template>
              <span>{{ $util.getTimerSectionsNames(item.zone_macs) }}</span>
            </v-tooltip>
          </template>
          <template v-slot:item.group_ids="{ item }">
            <v-tooltip bottom open-on-hover open-delay="500">
              <template v-slot:activator="{ attr, on }">
                <span v-bind="attr" v-on="on">{{ $util.getTimerGroups(item.group_ids) }}</span>
              </template>
              <span>{{ $util.getTimerGroupsName(item.group_ids) }}</span>
            </v-tooltip>
          </template>
          <!--操作列-->
          <template v-slot:item.actions="{ item }">
            <v-icon
              large
              :title="$t('common.edit')"
              @click.stop.prevent="preEditTaskActionInTable(item)"
            >
              mdi-pencil
            </v-icon>
            <v-icon
              large
              :title="$t('common.delete')"
              @click.stop.prevent="preDeleteTaskActionInTable(item)"
              class="mx-1"
            >
              mdi-delete
            </v-icon>
          </template>
        </v-data-table>
      </v-card>
    </v-dialog>
    <!-- 新建任务， 编辑任务通用对话框 -->
    <v-dialog
      v-model="taskDialog"
      max-width="55%"
      transition
      persistent
    >
      <v-card>
        <v-container>
        <v-row
          justify="center"
          align="center"
          dense
          style="background-color: white"
        >
          <v-col
            cols="12"
          >
            <v-card
              max-height="100px"
            >
              <v-toolbar
                color="primary"
                dark
              >
                <v-app-bar-nav-icon class="mt-1"/>

                <v-toolbar-title v-show="isCreateTask">{{ $t('partitionSystem.buttons.createTask') }}</v-toolbar-title>
                <v-toolbar-title v-show="!isCreateTask">{{ $t('partitionSystem.buttons.editTask') }}</v-toolbar-title>
              </v-toolbar>
            </v-card>
          </v-col>
          <v-col cols="12">
            <v-card height="80px">
              <v-row>
                <v-col cols="12" lg="3" class="mx-5">
                  <v-text-field
                    v-model="taskName"
                    :label="$t('partitionSystem.labels.taskName')"
                    :placeholder="$t('partitionSystem.placeholders.enterTaskName')"
                    hide-details
                  />
                </v-col>
                <v-col cols="12" lg="3">
                  <v-slider
                    class="mt-5"
                    :value="taskVolume"
                    prepend-icon="mdi-volume-high"
                    thumb-label="always"
                    thumb-size="28"
                    @change.passive="v => taskVolume = v"
                  />
                </v-col>
              </v-row>
            </v-card>
          </v-col>

          <v-col
            cols="12"
            lg="3"
          >
            <v-card :height="get_vcard_height">
              <v-toolbar
                color="primary"
                dark
              >
                <v-toolbar-title>{{ $t('partitionSystem.steps.deviceSelection') }}</v-toolbar-title>
              </v-toolbar>
              <v-list>
                <v-card-title class="text-left pt-2">{{ $t('partitionSystem.labels.selectedPartitions') }}: {{ taskPartitionSelectTransfer.length }}</v-card-title>
                <v-btn color="primary" class="mt-4 ml-6" @click="taskPartitionSelectDialog = true">{{ $t('partitionSystem.buttons.selectPartitions') }}</v-btn>
              </v-list>
              <v-list>
                <v-card-title class="text-left pt-2">{{ $t('partitionSystem.labels.selectedGroups') }}: {{ groupSelectTransfer.length }}</v-card-title>
                <v-btn color="primary" class="mt-4 ml-6" @click="groupSelectDialog = true">{{ $t('partitionSystem.buttons.selectGroups') }}</v-btn>
              </v-list>
            </v-card>
          </v-col>

          <v-col
            cols="12"
            lg="9"
          >
            <v-card :height="get_vcard_height">
              <v-toolbar
                color="primary"
                dark
              >
                <v-toolbar-title>
                  <v-row style="margin: 0px">
                    <span>{{ $t('partitionSystem.steps.audioSourceSelection') }}</span>
                    <v-radio-group
                      v-model="taskSourceType"
                      row
                      hide-details
                    >
                      <v-radio
                        :label="$t('partitionSystem.labels.songPlayback')"
                        color="info"
                        :value="Number(0)"
                        class="ml-6"
                      />
                      <v-radio
                        v-if="checkIfShowAudioCollector"
                        :label="$t('partitionSystem.labels.audioCollection')"
                        color="info"
                        :value="Number(1)"
                      />
                    </v-radio-group>
                  </v-row>
                </v-toolbar-title>
              </v-toolbar>
              <!--音源列表选中-->
              <div v-if="taskSourceType === 1">
                <div v-show="audioCollectors.length === 0">
                  <v-card-subtitle class="pt-8 text-lg-h3">
                    <span style="color: red">{{ $t('partitionSystem.tips.noAudioCollectorFound') }}</span>
                  </v-card-subtitle>
                </div>
                <div class="ml-4 mt-3" v-show="audioCollectors.length > 0">
                  <v-row>
                    <v-col col="12">
                      <v-toolbar
                        color="primary"
                        dark
                        height="40px"
                      >
                        <v-toolbar-title
                          class="ml-n4 text-lg-h4"
                          v-html="getTooltipForSelectedAudioCollector"
                        >
                        </v-toolbar-title>
                      </v-toolbar>
                      <!--<v-card-text>-->
                      <v-text-field
                        v-model="audioCollectorSearch"
                        prepend-icon="mdi-magnify"
                        flat
                        hide-details
                        clearable
                        clear-icon="mdi-close-circle-outline"
                        class="ml-2"
                      />
                      <v-treeview
                        :items="audioCollectorSearchTreeData"
                        item-key="id"
                        item-text="name"
                        item-children="children"
                        selected-color="primary"
                        selection-type="leaf"
                        hoverable
                        activatable
                        dense
                        :open-all="audioCollectorSearchTreeData.length === 1"
                        :filter="treeViewFilter"
                        :search="audioCollectorSearch"
                        :active.sync="audioCollectorActiveIds"
                        :open.sync="audioCollectorOpenIds"
                        open-on-click
                        class="mt-2"
                      >
                        <!-- 设置标签大小 -->
                        <template v-slot:label="{ item }">
                          <span :class="getAudioCollectorClass(item.id)" style="font-weight: 350">{{ item.name }}</span>
                        </template>
                      </v-treeview>
                      <!--</v-card-text>-->
                    </v-col>
                  </v-row>
                </div>
              </div>
              <!--歌曲选择-->
              <div v-else-if="taskSourceType === 0">
                <div v-show="playList.length === 0">
                  <v-card-subtitle class="pt-8 text-lg-h3">
                    <span style="color: red">{{ $t('partitionSystem.tips.playlistEmpty') }}</span>
                  </v-card-subtitle>
                </div>
                <div class="ml-4 mt-3" v-show="playList.length > 0">
                  <v-row>
                    <v-col cols="12">
                      <v-toolbar
                        color="primary"
                        dark
                        height="40px"
                      >
                        <v-toolbar-title
                          class="ml-n4 text-lg-h4"
                          v-html="getTitleOfTaskSelectedSongs()"
                        >
                        </v-toolbar-title>
                      </v-toolbar>
                    </v-col>
                    <v-col cols="6" class="mt-n4">
                      <v-select
                        v-model="selectedPlayList"
                        :items="playList"
                        filled
                        item-value="list_id"
                        item-text="list_name"
                        name="selectedPlayMode"
                        :label="$t('partitionSystem.labels.playlist')"
                        class="section-high-class mr-3"
                      />
                    </v-col>
                    <v-col cols="6" class="mt-n4">
                      <v-select
                        v-model="selectedPlayMode"
                        :items="localizedPlayModes"
                        filled
                        item-value="id"
                        item-text="name"
                        name="selectedPlayMode"
                        :label="$t('partitionSystem.labels.playMode')"
                        class="section-high-class mr-3"
                      />
                    </v-col>
                    <v-col cols="6" class="mt-n11">
                      <v-card min-height="100px" width="96%">
                        <div class="pt-2">
                          <v-chip
                            class="text-h4 ml-1"
                            color="primary"
                            text-color="white"
                          >
                            {{ $t('partitionSystem.labels.playlistSongs') }}
                          </v-chip>
                          <v-btn
                            color="primary darken-1"
                            text
                            disabled
                            max-width="100px"
                          >
                            {{ $t('partitionSystem.labels.doubleClickToAdd') }}
                          </v-btn>
                          <v-btn
                            color="primary darken-1"
                            text
                            @click="addAllLeftSongs"
                            max-width="100px"
                          >
                            {{ $t('partitionSystem.buttons.addAll') }}
                          </v-btn>
                        </div>
                        <v-list :max-height="get_vlist_max_height">
                          <draggable
                            tag="v-list-item-group"
                            :list="getSelectedPlayListSongs"
                            :group="{ name: 'song', pull: 'clone', put: false }"
                          >
                            <v-list-item
                              v-for="(song,i) in getSelectedPlayListSongs"
                              :key="i"
                              dense
                              @dblclick="dbClickLeftSongList(song)"
                              :disabled="!song.alive"
                            >
                              <v-tooltip
                                top
                                open-on-hover
                                open-delay="1000"
                                nudge-left="10"
                              >
                                <template v-slot:activator="{ on, attrs }">
                                  <v-list-item-content v-bind="attrs" v-on="on">
                                    <v-list-item-title
                                      :style="songStyleInPlaylist(song.alive)"
                                      v-text="song.song_name"
                                    />
                                  </v-list-item-content>
                                </template>
                                <span v-html="getSongNameAndDurationForSongTooltip(song)"/>
                              </v-tooltip>
                            </v-list-item>
                          </draggable>
                        </v-list>
                      </v-card>
                    </v-col>
                    <v-col cols="6" class="mt-n11">
                      <v-card
                        min-height="100px"
                        class="mr-3"
                        width="96%"
                      >
                        <div class="pt-2">
                          <v-chip
                            class="text-h4 ml-1"
                            color="primary"
                            text-color="white"
                          >
                            {{ $t('partitionSystem.labels.selectedSongs') }}
                          </v-chip>
                          <v-btn
                            color="primary darken-1"
                            text
                            disabled
                            max-width="60px"
                            class="pl-1"
                          >
                            {{ $t('partitionSystem.labels.doubleClickToDelete') }}
                          </v-btn>
                          <v-btn
                            fab
                            color="primary"
                            @click="songMoveUp"
                            :disabled="singleSelectedSongIndex == null || singleSelectedSongIndex === 0"
                            small
                          >
                            <v-icon size="25">mdi-arrow-up-bold</v-icon>
                          </v-btn>
                          <v-btn
                            fab
                            color="primary"
                            @click="songMoveDown"
                            :disabled="singleSelectedSongIndex == null || singleSelectedSongIndex >= selectedSongs.length - 1"
                            small
                          >
                            <v-icon size="25">mdi-arrow-down-bold</v-icon>
                          </v-btn>
                          <v-btn
                            fab
                            color="primary"
                            @click="removeSelectedSongs"
                            :disabled="selectedSongs.length === 0"
                            small
                            class="ml-n0"
                          >
                            <v-icon size="25">mdi-delete</v-icon>
                          </v-btn>
                        </div>
                        <v-list :max-height="get_vlist_max_height">
                          <draggable
                            :list="selectedSongs"
                            :group="{ name: 'song'}"
                          >
                            <v-list-item-group :value="singleSelectedSongIndex">
                              <v-list-item
                                v-for="(song,i) in selectedSongs"
                                :key="i"
                                dense
                                :input-value="singleSelectedSongIndex === i"
                                @dblclick="dbClickRightSongList(i)"
                                @change="changeSelected(i)"
                              >
                                <v-tooltip
                                  top
                                  open-on-hover
                                  open-delay="1000"
                                  nudge-left="10"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-list-item-content v-bind="attrs" v-on="on">
                                      <v-list-item-title
                                        :style="songStyleInPlaylist(song.alive)"
                                        v-text="song.song_name"
                                      />
                                    </v-list-item-content>
                                  </template>
                                  <span v-html="getSongNameAndDurationForSongTooltip(song)"/>
                                </v-tooltip>
                              </v-list-item>
                            </v-list-item-group>
                          </draggable>
                        </v-list>
                      </v-card>
                    </v-col>
                  </v-row>
                </div>
              </div>
            </v-card>
          </v-col>

          <v-col
            cols="12"
          >
            <v-card height="60px" align="center" class="pt-3">
              <v-btn color="primary" @click="taskDialog = false">{{ $t('common.cancel') }}</v-btn>
              <v-btn color="primary" @click="createOrEditTask(isCreateTask)">{{ $t('common.confirm') }}</v-btn>
            </v-card>
          </v-col>
        </v-row>
        </v-container>
      </v-card>
    </v-dialog>
    <!--任务分区选择对话框-->
    <v-dialog
      v-model="taskPartitionSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.partitionSelection') }}</span>
        </v-card-title>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="taskPartitionSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('partitionSystem.placeholders.enterPartitionKeyword')"
            :data="myZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('partitionSystem.labels.partitionList'), $t('partitionSystem.labels.selectedPartitions')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="taskPartitionSelectDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- 分组选择对话框-->
    <v-dialog
      v-model="groupSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.groupSelection') }}</span>
        </v-card-title>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="groupSelectTransfer"
            filterable
            :filter-placeholder="$t('partitionSystem.placeholders.enterGroupKeyword')"
            :data="groupList"
            :props="{
              key: 'group_id',
              label: 'group_name'
            }"
            :titles="[$t('partitionSystem.labels.groupList'), $t('partitionSystem.labels.selectedGroups')]"
          />
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="groupSelectDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="editSoundEffectDialog"
      max-width="780px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.soundEffectSettings') }}</span>
        </v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col cols="12" class="mt-n3" lg="6">
                <span class="font-weight-bold">{{ $t('partitionSystem.labels.currentSoundEffect') }}： </span>
                <span style="padding-left: 20px" />
                <span class="font-weight-bold">{{ getPresentEffect() }}</span>
              </v-col>
              <v-col cols="12" lg="6" class="mt-n8">
                <v-switch v-if="isAdmin" v-model="isSetAllSameModelSoundEffect" :label="$t('partitionSystem.labels.setAllSameTypeDevices')" />
              </v-col>
              <v-col cols="12" class="mt-n5">
                <v-select
                  v-model="soundSelected"
                  :items="localizedSoundEffectList"
                  filled
                  item-value="value"
                  item-text="text"
                  name="soundSelected"
                  :label="$t('partitionSystem.labels.selectSoundEffect')"
                  class="pb-6 section-high-class"
                />
              </v-col>
              <v-col v-if="soundSelected === 1" cols="12" class="ml-3 mt-n10">
                <!-- since v-model must be a data variable, add 10 node (need refactor) -->
                <vue-slider
                  v-model="gain1"
                  direction="btt"
                  :height="200"
                  :lazy="true"
                  :interval="4"
                  :tooltip="'always'"
                  :data="ticksLabels"
                  :marks="true"
                  :hide-label="true"
                  style="display: inline-block; margin: 2px 25px;"
                />
                <vue-slider
                  v-model="gain2"
                  direction="btt"
                  :height="200"
                  :lazy="true"
                  :interval="4"
                  :tooltip="'always'"
                  :data="ticksLabels"
                  :marks="true"
                  :hide-label="true"
                  style="display: inline-block; margin: 2px 25px;"
                />
                <vue-slider
                  v-model="gain3"
                  direction="btt"
                  :height="200"
                  :lazy="true"
                  :interval="4"
                  :tooltip="'always'"
                  :data="ticksLabels"
                  :marks="true"
                  :hide-label="true"
                  style="display: inline-block; margin: 2px 25px;"
                />
                <vue-slider
                  v-model="gain4"
                  direction="btt"
                  :height="200"
                  :lazy="true"
                  :interval="4"
                  :tooltip="'always'"
                  :data="ticksLabels"
                  :marks="true"
                  :hide-label="true"
                  style="display: inline-block; margin: 2px 25px;"
                />
                <vue-slider
                  v-model="gain5"
                  direction="btt"
                  :height="200"
                  :lazy="true"
                  :interval="4"
                  :tooltip="'always'"
                  :data="ticksLabels"
                  :marks="true"
                  :hide-label="true"
                  style="display: inline-block; margin: 2px 25px;"
                />
                <vue-slider
                  v-model="gain6"
                  direction="btt"
                  :height="200"
                  :lazy="true"
                  :interval="4"
                  :tooltip="'always'"
                  :data="ticksLabels"
                  :marks="true"
                  :hide-label="true"
                  style="display: inline-block; margin: 2px 25px;"
                />
                <vue-slider
                  v-model="gain7"
                  direction="btt"
                  :height="200"
                  :lazy="true"
                  :interval="4"
                  :tooltip="'always'"
                  :data="ticksLabels"
                  :marks="true"
                  :hide-label="true"
                  style="display: inline-block; margin: 2px 25px;"
                />
                <vue-slider
                  v-model="gain8"
                  direction="btt"
                  :height="200"
                  :lazy="true"
                  :interval="4"
                  :tooltip="'always'"
                  :data="ticksLabels"
                  :marks="true"
                  :hide-label="true"
                  style="display: inline-block; margin: 2px 25px;"
                />
                <vue-slider
                  v-model="gain9"
                  direction="btt"
                  :height="200"
                  :lazy="true"
                  :interval="4"
                  :tooltip="'always'"
                  :data="ticksLabels"
                  :marks="true"
                  :hide-label="true"
                  style="display: inline-block; margin: 2px 25px;"
                />
                <vue-slider
                  v-model="gain10"
                  direction="btt"
                  :height="200"
                  :lazy="true"
                  :interval="4"
                  :tooltip="'always'"
                  :data="ticksLabels"
                  :marks="true"
                  :hide-label="true"
                  style="display: inline-block; margin: 2px 25px;"
                />
              </v-col>
              <!-- <v-col
                v-for="(label, index) in gainsLabels"
                :key="index"
                cols="auto"
                style="white-space: pre-wrap ; margin: 5px 12px;"
                v-html="getEffectLabel(label, index)"
              >
              </v-col>-->
              <div v-if="soundSelected === 1">
                <span style="margin: 1px 47px;">31</span>
                <span style="margin: 1px 7px;">62</span>
                <span style="margin: 1px 38px;">125</span>
                <span style="margin: 1px 7px;">250</span>
                <span style="margin: 1px 34px;">500</span>
                <span style="margin: 1px 12px;">1k</span>
                <span style="margin: 1px 43px;">2k</span>
                <span style="margin: 1px 9px;">4k</span>
                <span style="margin: 1px 42px;">8k</span>
                <span style="margin: 1px 8px;">16k</span>
              </div>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions :class="soundSelected === 1 ? 'mt-n6' : 'mt-n16'">
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="editSoundEffectDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="updateSoundEffect"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="monitorBindingDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.monitorBinding') }}</span>
        </v-card-title>
        <v-card-subtitle class="pt-9 text-lg-h3">
          <span v-if="getDevice.monitor_mac == null || getDevice.monitor_mac === ''">{{ $t('partitionSystem.tips.noMonitorBound') }}</span>
          <span v-else>{{ $t('partitionSystem.labels.currentBinding') }}: {{ getMonitorNameByMac(getDevice.monitor_mac) }}</span>
        </v-card-subtitle>
        <v-list class="mt-n3">
          <v-list
            nav
          >
            <v-list-item-group v-model="monitorSelected" color="primary">
              <v-list-item
                v-for="monitor in monitors"
                :key="monitor.monitor_mac"
                class="pl-8"
              >
                <v-list-item-content>
                  <v-list-item-title style="font-size: 1.25em" v-text="monitor.name" />
                </v-list-item-content>
              </v-list-item>
            </v-list-item-group>
          </v-list>
        </v-list>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="monitorBindingDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="setMonitorInfo()"
          >
            {{ $t('common.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--分区选择对话框-->
    <v-dialog
      v-model="partitionSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.partitionSelection') }}</span>
        </v-card-title>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="partitionSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('partitionSystem.placeholders.enterPartitionKeyword')"
            :data="myZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('partitionSystem.labels.partitionList'), $t('partitionSystem.labels.selectedPartitions')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="partitionSelectDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--消防告警音效选择对话框-->
    <v-dialog
      v-model="channelSongSelectDialog"
      max-width="800px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.alarmSoundSelection') }}</span>
        </v-card-title>
        <v-card-subtitle class="pt-9 text-lg-h3">
          <span v-if="currentSong === null">{{ $t('partitionSystem.tips.noAlarmSoundSelected') }}</span>
          <span v-else>{{ $t('partitionSystem.labels.currentSelection') }}: {{ currentSong }}</span>
        </v-card-subtitle>
        <v-list class="mt-n3" max-height="70vh">
          <v-list-group
            v-for="item in playList"
            :key="item.list_name"
            v-model="item.active"
            no-action
          >
            <template v-slot:activator>
              <v-list-item-content>
                <v-list-item-title
                  v-text="item.list_name"
                />
              </v-list-item-content>
            </template>

            <v-list-item
              v-for="song in item.songs"
              :key="song.song_name"
              :input-value="currentSong === song.song_name"
              class="pl-8"
              @click.stop="songChanged(song.song_name, song.song_path_name)"
            >
              <v-list-item-content>
                <v-list-item-title
                  class="text--primary"
                  v-text="song.song_name"
                />
              </v-list-item-content>
            </v-list-item>
          </v-list-group>
        </v-list>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="channelSongSelectDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="updateFireSong()"
          >
            {{ $t('common.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--触发歌曲选择对话框-->
    <v-dialog
      v-model="editTriggerSongSelectDialog"
      max-width="800px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.triggerSongSelection') }}</span>
        </v-card-title>
        <v-card-subtitle class="pt-9 text-lg-h3">
          <span v-if="editTriggerCurrentSong === null">{{ $t('partitionSystem.tips.noTriggerSongSelected') }}</span>
          <span v-else>{{ $t('partitionSystem.labels.currentSelection') }}: {{ editTriggerCurrentSong }}</span>
        </v-card-subtitle>
        <v-list class="mt-n3" max-height="70vh">
          <v-list-group
            v-for="item in playList"
            :key="item.list_name"
            v-model="item.active"
            no-action
          >
            <template v-slot:activator>
              <v-list-item-content>
                <v-list-item-title
                  v-text="item.list_name"
                />
              </v-list-item-content>
            </template>

            <v-list-item
              v-for="song in item.songs"
              :key="song.song_name"
              :input-value="editTriggerCurrentSong === song.song_name"
              class="pl-8"
              @click.stop="editTriggerSongChanged(song.song_name, song.song_path_name)"
            >
              <v-list-item-content>
                <v-list-item-title
                  class="text--primary"
                  v-text="song.song_name"
                />
              </v-list-item-content>
            </v-list-item>
          </v-list-group>
        </v-list>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="editTriggerSongSelectDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="updateEditTriggerSong()"
          >
            {{ $t('common.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--监听音箱选择对话框-->
    <v-dialog
      v-model="setMonitorDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.monitorSpeaker') }}</span>
        </v-card-title>
        <v-card-subtitle class="pt-9 text-lg-h3 text-center">
          <span>{{ $t('partitionSystem.tips.monitorSpeakerWarning') }}</span>
          <span style="color: red">{{ $t('partitionSystem.labels.disabled') }}</span>
          <span>！</span>
        </v-card-subtitle>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="monitorPartitionSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('partitionSystem.placeholders.enterPartitionKeyword')"
            :data="myZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('partitionSystem.labels.partitionList'), $t('partitionSystem.labels.selectedPartitions')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="setMonitorDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="setMonitorDevice()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="fireCollectorDialog"
      max-width="1600px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.fireManagement') }}</span>
        </v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col :cols="fireChannelSelected.length === 0 ? 12 : 6">
                <v-data-table
                  v-model="fireChannelSelected"
                  :headers="localizedFireChannelHeaders"
                  :items="getSelectedFireCollector"
                  :search="fireChannelSearch"
                  sort-by.sync="['channel_id']"
                  item-key="channel_id"
                  class="elevation-1"
                  single-select
                  :loading="fireCollectorInfo.length === 0"
                  :loading-text="$t('partitionSystem.tips.noFireCollectorChannelInfo')"
                  style="margin-top: -10px"
                  :footer-props="{'items-per-page-options':[11]}"
                  :items-per-page="11"
                  @click:row="fireChannelRowClick"
                >
                  <template v-slot:item.trigger="{ item }">
                    <span>{{ getChannelTriggerMode(item.trigger) }}</span>
                  </template>
                  <template v-slot:item.sound="{ item }">
                    <span>{{ getChannelFireSound(item.sound) }}</span>
                  </template>
                  <template v-slot:item.zone_macs="{ item }">
                    <span>{{ getChannelFireZoneCount(item.zone_macs) }}</span>
                  </template>
                  <template v-slot:item.status="{ item }">
                    <v-tooltip
                      bottom
                      open-on-hover
                      open-delay="500"
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <v-avatar :color="item.status === 1 ? 'primary' : 'grey'" size="24" v-bind="attrs" v-on="on" />
                      </template>
                      <span>{{ item.status === 1 ? $t('partitionSystem.labels.triggered') : $t('partitionSystem.labels.notTriggered') }}</span>
                    </v-tooltip>
                  </template>
                </v-data-table>
              </v-col>
              <v-col v-if="fireChannelSelected.length !== 0" cols="6">
                <v-row>
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="2">
                        <span class="font-weight-bold">{{ $t('partitionSystem.labels.channelName') }}</span>
                      </v-col>
                      <v-col cols="10">
                        <v-text-field
                          v-model="channelName"
                          :label="$t('partitionSystem.labels.channelName')"
                          :placeholder="$t('partitionSystem.placeholders.enterChannelName')"
                          hide-details
                          class="mt-n3 ml-6"
                        />
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-radio-group
                      v-model="fireChannelTrigger"
                      row
                      class="mt-n4"
                    >
                      <span class="font-weight-bold">{{ $t('partitionSystem.labels.triggerMode') }}</span>
                      <v-radio
                        :label="$t('partitionSystem.labels.levelTrigger')"
                        color="primary"
                        :value="Number(0)"
                        class="pl-16 ml-5"
                      />
                      <v-radio
                        :label="$t('partitionSystem.labels.shortCircuitTrigger')"
                        color="primary"
                        :value="Number(1)"
                      />
                    </v-radio-group>
                  </v-col>
                  <v-col cols="12">
                    <span class="font-weight-bold">{{ $t('partitionSystem.labels.currentAlarmSound') }}:</span>
                    <span style="padding-left: 50px" />
                    <span v-if="fireChannelSound === null || fireChannelSound === ''">{{ $t('common.none') }}</span>
                    <span v-else>{{ fireChannelSound.substring(fireChannelSound.lastIndexOf('/') + 1) }}</span>
                    <span style="padding-left: 30px" />
                    <v-btn
                      color="primary"
                      width="20"
                      height="20"
                      @click="preUpdateFireChannelSound"
                    >
                      {{ $t('common.edit') }}
                    </v-btn>
                  </v-col>
                  <v-col cols="12">
                    <span class="font-weight-bold">{{ $t('partitionSystem.labels.currentTriggerPartitions') }}: </span>
                    <span style="padding-left: 50px" />
                    <span>{{ fireChannelZones.length }}</span>
                    <span style="padding-left: 30px" />
                    <v-btn
                      color="primary"
                      width="20"
                      height="20"
                      @click="partitionSelectDialog = true"
                    >
                      {{ $t('common.edit') }}
                    </v-btn>
                    <v-list
                      v-show="getZoneNamesOfFireChannels.length > 0"
                      disabled
                      dense
                      subheader
                    >
                      <v-list-item-group color="primary">
                        <v-list-item
                          v-for="(item, i) in getZoneNamesOfFireChannels"
                          :key="i"
                        >
                          <v-list-item-content>
                            <v-list-item-title v-html="item" />
                          </v-list-item-content>
                        </v-list-item>
                      </v-list-item-group>
                    </v-list>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions v-if="fireChannelSelected.length !== 0" class="mt-n10 justify-center">
          <v-btn
            color="blue darken-1"
            text
            @click="fireCollectorDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="updateFireCollector(false)"
          >
            {{ $t('partitionSystem.buttons.setCurrentChannel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="updateFireCollector(true)"
          >
            {{ $t('partitionSystem.buttons.setAllChannels') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- 电源时序信息对话框 -->
    <v-dialog
      v-model="sequencePowerInfoDialog"
      max-width="700px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.powerManagement') }}</span>
        </v-card-title>
        <v-card-text class="mt-n6">
          <v-container class="powerInfo-container">
            <v-row>
              <v-col cols="12" class="py-2">
                <!--<span class="headline">控制模式</span>-->
                <v-radio-group
                  v-model="updatedPowerControlMode"
                  @change="changePowerControlMode"
                  row
                >
                  <span class="font-weight-bold text-lg-h3 pt-1">{{ $t('partitionSystem.labels.controlMode') }}</span>
                  <v-radio
                    :label="$t('partitionSystem.labels.manualMode')"
                    color="primary"
                    :value="Number(1)"
                    class="pl-2 ml-1"
                  />
                  <v-radio
                    :label="$t('partitionSystem.labels.autoMode')"
                    color="primary"
                    :value="Number(2)"
                  />
                  <v-btn
                    color="primary"
                    @click="openAllPowerChannel"
                  >
                    {{ $t('partitionSystem.buttons.allOn') }}
                  </v-btn>
                  <v-btn
                    color="primary"
                    @click="disableAllPowerChannel"
                    class="mr-n2"
                  >
                    {{ $t('partitionSystem.buttons.allOff') }}
                  </v-btn>
                </v-radio-group>
              </v-col>
              <v-col cols="12" class="mt-n6">
                <v-data-table
                  dense
                  :headers="localizedPowerInfoHeaders"
                  :items="updatedPowerChannels"
                  sort-by.sync="['channel_id']"
                  item-key="channel_id"
                  class="elevation-1"
                  :loading="updatedPowerChannels.length === 0"
                  :loading-text="$t('partitionSystem.tips.noPowerChannelInfo')"
                  style="margin-top: -10px"
                  :footer-props="{'items-per-page-options':[16]}"
                  :items-per-page="16"
                >
                  <!--todo 文本框样式及点击修改功能待完善-->
                  <template v-slot:item.name="{ item }">
                    <!--<v-text-field-->
                    <!--  :value="item.name"-->
                    <!--  :readonly="item.locked"-->
                    <!--  hide-details-->
                    <!--  :append-icon="item.locked ? 'mdi-pencil' : 'mdi-check-bold'"-->
                    <!--  @click:append="powerAppendIconClick(item.channel_id)"-->
                    <!--  @change="name => updatePowerChannelName(item.channel_id, name)"-->
                    <!--  />-->
                    <v-row justify="center" class="my-n2">
                      <v-col cols="3"></v-col>
                      <!--v-row控制文本框长度-->
                      <v-col cols="7">
                        <v-text-field
                          class="powerInfo-text-field"
                          :value="item.name"
                          hide-details
                          dense
                          @change="name => updatePowerChannelName(item.channel_id, name)"
                        />
                      </v-col>
                    </v-row>
                  </template>
                  <!--开关设置为switch模式-->
                  <!--@click.native.stop: 当点击滑块操作时,不修改表格行的选中状态-->
                  <template v-slot:item.status="{ item }">
                    <!-- todo 控制高度位置-->
                    <v-row justify="center" class="my-n2">
                      <v-switch
                        hide-details
                        :key="powerInfoStatusRefreshKey"
                        :input-value="getValidPowerStatus(item.status)"
                        :label="$t('partitionSystem.labels.open')"
                        color="primary"
                        :disabled="updatedPowerControlMode === 2"
                        @change="updatePowerChannelStatus(item.channel_id, item.status)"
                        @click.native.stop
                      />
                    </v-row>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <!--
        <v-card-actions class="mt-n10 justify-center">
          <v-btn
            color="blue darken-1"
            text
            @click="sequencePowerInfoDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="resetSequencePowerDevice()"
          >
            {{ $t('partitionSystem.buttons.resetChanges') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="updateSequencePowerDevice()"
            :disabled="!isSequencePowerDeviceUpdated && !isSequencePowerModeUpdated"
          >
            {{ $t('partitionSystem.buttons.confirmChanges') }}
          </v-btn>
        </v-card-actions>
        -->
      </v-card>
    </v-dialog>
    <!--重启设备-->
    <v-dialog
      v-model="restartDeviceDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('partitionSystem.dialogs.restartDevice') }}
        </v-card-title>
        <v-card-text class="text-center">
          <v-container>
            <v-row dense>
              <v-col cols="12">
                <span style="padding-right: 10px">{{ $t('partitionSystem.tips.aboutToRestartDevice') }}</span>
                <span
                  class="font-weight-black text-decoration-underline"
                  style="padding-right: 10px"
                >{{ getDevice.name }}</span>
                <span>,{{ $t('partitionSystem.tips.pleaseConfirm') }}</span>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-switch
            v-if="isAdmin"
            v-model="restartSameTypeDeviceSwitch"
            :label="$t('partitionSystem.labels.restartAllSameTypeDevices')"
            hide-details
            dense
            class="pl-9 pb-6"
          />
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="restartDeviceDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="restartDevice"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--重置设备数据-->
    <v-dialog
      v-model="resetDeviceDialog"
      max-width="600px"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          <p>{{ $t('partitionSystem.dialogs.resetDeviceData') }}</p>
        </v-card-title>
        <v-card-text class="text-center">
          <span style="padding-right: 10px">{{ $t('partitionSystem.tips.aboutToResetDevice') }}</span>
          <span
            class="font-weight-black text-decoration-underline"
            style="padding-right: 10px"
          >{{ getDevice.name }}</span>
          <span>{{ $t('partitionSystem.tips.andRestoreFactorySettings') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-switch
            v-model="resetSameTypeDeviceSwitch"
            :label="$t('partitionSystem.labels.resetAllSameTypeDevicesData')"
            hide-details
            dense
            class="pl-9 pb-6"
          />
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="resetDeviceDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="resetDeviceConfirmDialog = true"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="resetDeviceConfirmDialog"
      max-width="600px"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          <p>{{ $t('partitionSystem.dialogs.resetDeviceData') }}</p>
        </v-card-title>
        <v-card-text class="text-center">
          <span class="red--text text--darken-4 font-weight-black" style="font-size: 1.15em">
            {{ $t('partitionSystem.warnings.factoryResetWarning') }}
          </span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="resetDeviceConfirmDialog = false"
            class="mb-3"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="resetDevice"
            class="mb-3"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--  设置蓝牙对话框   -->
    <v-dialog
      v-model="setBluetoothDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.bluetoothSettings') }}</span>
        </v-card-title>
        <v-card-text>
          <v-text-field
            :value="getDevice.name"
            :label="$t('partitionSystem.labels.deviceName')"
            disabled
            class="pt-6"
            hide-details
          />
          <v-text-field
            :value="getDevice.mac"
            label="MAC"
            disabled
            class="pt-6"
            hide-details
          />
          <v-form
            ref="bluetoothForm"
            v-model="bluetoothForm"
          >
            <v-text-field
              v-model="btName"
              :label="$t('partitionSystem.labels.bluetoothName')"
              class="pt-6"
              counter="32"
              clearable
              :rules="[localizedRules.btName]"
            />
            <v-radio-group
              v-model="btEncryption"
              row
            >
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.bluetoothEncryption') }}</span>
              <v-spacer />
              <v-radio
                :label="$t('partitionSystem.labels.noPasswordRequired')"
                color="primary"
                :value="Number(0)"
                class="pl-16 ml-16"
              />
              <v-radio
                :label="$t('partitionSystem.labels.passwordRequired')"
                color="primary"
                :value="Number(1)"
              />
            </v-radio-group>
            <div v-show="btEncryption === 1">
              <v-text-field
                v-model="btPin"
                :label="$t('partitionSystem.labels.bluetoothPassword')"
                counter="4"
                clearable
                :rules="[localizedRules.btPin]"
              />
            </div>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="setBluetoothDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="blue darken-1"
            text
            @click="setBluetooth()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--删除设备-->
    <v-dialog
      v-model="deleteDeviceDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-card-title class="headline">
          {{ $t('partitionSystem.dialogs.deleteDevice') }}
        </v-card-title>
        <v-card-text class="text-center">
          <v-container>
            <v-row dense>
              <v-col cols="12">
                <span style="padding-right: 10px">{{ $t('partitionSystem.tips.aboutToDeleteDevice') }}</span>
                <span
                  class="font-weight-black text-decoration-underline"
                  style="padding-right: 10px"
                >{{ deleteDeviceInfo == null ? '' : deleteDeviceInfo.name }}</span>
                <span>,{{ $t('partitionSystem.tips.pleaseConfirm') }}</span>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteDeviceDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="deleteDevice"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="deleteTaskDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title style="font-size: 2.0em;">{{ $t('partitionSystem.dialogs.deleteTask') }}</v-toolbar-title>
        </v-toolbar>
        <v-card-text class="text-center">
          <span>{{ $t('partitionSystem.confirmations.aboutToDeleteTask') }}</span>
          <span class="font-weight-black text-decoration-underline">{{ taskSelected.length === 0 ? '' : taskSelected[0].name }}</span>
          <span>,{{ $t('partitionSystem.confirmations.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteTaskDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="deleteTask"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="deleteTaskActionInTableDialog"
      max-width="500"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title style="font-size: 2.0em;">{{ $t('partitionSystem.dialogs.deleteTask') }}</v-toolbar-title>
        </v-toolbar>
        <v-card-text class="text-center">
          <span>{{ $t('partitionSystem.confirmations.aboutToDeleteTask') }}</span>
          <span class="font-weight-black text-decoration-underline">{{ deletedTaskActionInTable ? deletedTaskActionInTable.name : '' }}</span>
          <span>,{{ $t('partitionSystem.confirmations.pleaseConfirm') }}</span>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="deleteTaskActionInTableDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="deleteTaskActionInTable()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--按键管理-->
    <v-dialog
      v-model="keyManagerDialog"
      max-width="600px"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title style="font-size: 2.0em;">{{ $t('partitionSystem.dialogs.keyManagement') }}</v-toolbar-title>
        </v-toolbar>
        <v-container>
          <v-row>
            <v-col
              cols="6"
              v-for="index in 12"
              :key="index">
              <v-select
                v-model="keys['key' + index]"
                :items="keyEventItems"
                :label="$t('partitionSystem.inputLabels.key') + index"
                :menu-props="{offsetY: true}"
                dense
                outlined
              />
            </v-col>
          </v-row>
        </v-container>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="keyManagerDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="saveKeySettings()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--采播设置-->
    <v-dialog
      v-model="editAudioCollectorDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title style="font-size: 2.0em;">{{ $t('partitionSystem.dialogs.audioCollectorSettings') }}</v-toolbar-title>
        </v-toolbar>
        <v-container>

      <v-card class="channel-card" style="padding-bottom: 0px">
        <v-card-title style="margin-top: -15px; margin-bottom: 15px; font-size: 1.4em;">
          {{ $t('partitionSystem.labels.channelName') }}
        </v-card-title>
        <v-card-text style="margin-bottom: 26px;padding-bottom: 0px">
          <v-text-field
            v-for="(channel, index) in audioCollectorChannels"
            :key="index"
            :label="`${channel.fixedName}`"
            v-model="channel.channel_name"
            :rules="audioCollectorChannelNameRules(index)"
            outlined
          ></v-text-field>
        </v-card-text>
      </v-card>

          <v-row>
            <v-col cols="12" class="mt-1">
              <v-select
                v-model="editAudioCollectorPriority"
                :items="audioCollectorPriorityItems"
                :label="$t('partitionSystem.inputLabels.audioSourcePriority')"
                :menu-props="{offsetY: true, closeOnClick: true}"
                item-text="val"
                item-value="id"
                dense
                outlined
              />
            </v-col>
            <v-col cols="2" class="mt-1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.autoTrigger') }}</span>
            </v-col>
            <v-col cols="10">
              <v-radio-group
                v-model="editAudioCollectorTriggerSwitch"
                row
                class="mt-n1"
              >
                <v-radio
                  :label="$t('partitionSystem.radioLabels.off')"
                  color="primary"
                  :value="Number(0)"
                />
                <v-radio
                  :label="$t('partitionSystem.radioLabels.on')"
                  color="primary"
                  :value="Number(1)"
                />
              </v-radio-group>
            </v-col>
            <v-col
              v-show="editAudioCollectorTriggerSwitch === 1"
              cols="12"
              class="mt-n7"
            >
              <v-select
                v-model="editAudioCollectorTriggerChannelId"
                :items="audioCollectorChannelItems"
                :label="$t('partitionSystem.inputLabels.triggerChannel')"
                :menu-props="{offsetY: true, closeOnClick: true}"
                item-text="val"
                item-value="id"
                dense
                outlined
              />
            </v-col>
            <v-col cols="12" class="mt-n4 mb-n4" v-show="editAudioCollectorTriggerSwitch === 1">
                  <v-switch  :label="$t('partitionSystem.inputLabels.followDeviceVolume')" v-model="isAudioCollectorVolumeFollowDevice" />
            </v-col>
            <v-col cols="2" class="mt-1" v-show="editAudioCollectorTriggerSwitch === 1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.partitionVolume') }}</span>
            </v-col>
            <v-col cols="10" v-show="editAudioCollectorTriggerSwitch === 1">
              <v-slider
                :value="editAudioCollectorTriggerZoneVolume"
                @change.passive="v => editAudioCollectorTriggerZoneVolume = v"
                append-icon="mdi-volume-plus"
                prepend-icon="mdi-volume-minus"
                @click:append="plusTriggerVolume"
                @click:prepend="minusTriggerVolume"
                :thumb-size="24"
                thumb-label="always"
                :disabled="editAudioCollectorTriggerZoneVolume === 255"
              ></v-slider>
            </v-col>
          <v-col cols="12" v-show="editAudioCollectorTriggerSwitch === 1">
            <v-row>
              <v-col cols="6" class="ml-0">
                <span class="ml-0 text-left font-weight-bold" style="font-size: 15px;">{{ $t('partitionSystem.labels.selectedPartitionCount') }}: {{ audioCollectorPartitionSelectTransfer.length }}</span>
              </v-col>
              <v-col cols="6" class="mt-n3">
                <v-btn color="primary" class="ml-n12" @click="audioCollectorPartitionSelectDialog = true">{{ $t('partitionSystem.buttons.partitionSelection') }}</v-btn>
              </v-col>
            </v-row>
          </v-col>
          </v-row>
        </v-container>
        <v-card-actions>

          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="editAudioCollectorDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="saveAudioCollectorSettings()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--采播设置-分区选择对话框-->
    <v-dialog
      v-model="audioCollectorPartitionSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.partitionSelection') }}</span>
        </v-card-title>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="audioCollectorPartitionSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('partitionSystem.transferLabels.enterPartitionKeyword')"
            :data="myZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('partitionSystem.transferLabels.partitionList'), $t('partitionSystem.transferLabels.selectedPartitions')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="audioCollectorPartitionSelectDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--混音设置-->
    <v-dialog
      v-model="editAudioMixerDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title style="font-size: 2.0em;">{{ $t('partitionSystem.dialogs.parameterSettings') }}</v-toolbar-title>
        </v-toolbar>
        <v-container>
          <v-row>
            <v-col cols="2" class="mt-1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.mixingSwitch') }}</span>
            </v-col>
            <v-col cols="10">
              <v-radio-group
                v-model="editAudioMixerMasterSwitch"
                row
                class="mt-n1"
              >
                <v-radio
                  :label="$t('partitionSystem.radioLabels.off')"
                  color="primary"
                  :value="Number(0)"
                />
                <v-radio
                  :label="$t('partitionSystem.radioLabels.on')"
                  color="primary"
                  :value="Number(1)"
                />
              </v-radio-group>
            </v-col>
            <v-col cols="2" class="mt-n3" v-show="editAudioMixerMasterSwitch === 1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.triggerType') }}</span>
            </v-col>
            <v-col cols="10" class="mt-n3" v-show="editAudioMixerMasterSwitch === 1">
              <v-radio-group
                v-model="editAudioMixerTriggerType"
                row
                class="mt-n1"
              >
                <v-radio
                  :label="$t('partitionSystem.radioLabels.mixedTrigger')"
                  color="primary"
                  :value="Number(1)"
                />
                <v-radio
                  label="MIC"
                  color="primary"
                  :value="Number(2)"
                />
                <v-radio
                  label="AUX"
                  color="primary"
                  :value="Number(3)"
                />
              </v-radio-group>
            </v-col>
            <v-col
              v-show="editAudioMixerMasterSwitch === 1"
              cols="12"
              class="mt-n2"
            >
              <v-select
                v-model="editAudioMixerPriority"
                :items="audioMixerPriorityItems"
                :label="$t('partitionSystem.inputLabels.mixingPriority')"
                :menu-props="{offsetY: true, closeOnClick: true}"
                item-text="name"
                item-value="mac"
                dense
                outlined
              />
            </v-col>
            <v-col
              v-show="editAudioMixerMasterSwitch === 1"
              cols="12"
              class="mt-n7"
            >
              <v-select
                v-model="editAudioMixerVolumeFadeLevel"
                :items="audioMixerVolumeFadeLevelItems"
                :label="$t('partitionSystem.inputLabels.musicSignalFadeLevel')"
                :menu-props="{offsetY: true, closeOnClick: true}"
                item-text="val"
                item-value="level"
                dense
                outlined
              />
            </v-col>
            <v-col cols="12" class="mt-n4 mb-n4" v-show="editAudioMixerMasterSwitch === 1">
                  <v-switch  :label="$t('partitionSystem.inputLabels.followDeviceVolume')" v-model="isAudioMixerVolumeFollowDevice" />
            </v-col>
            <v-col cols="2" class="mt-1" v-show="editAudioMixerMasterSwitch === 1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.partitionVolume') }}</span>
            </v-col>
            <v-col cols="10" v-show="editAudioMixerMasterSwitch === 1">
              <v-slider
                :value="editAudioMixerZoneVolume"
                @change.passive="v => editAudioMixerZoneVolume = v"
                append-icon="mdi-volume-plus"
                prepend-icon="mdi-volume-minus"
                @click:append="plusTriggerVolume"
                @click:prepend="minusTriggerVolume"
                :thumb-size="24"
                thumb-label="always"
                :disabled="editAudioMixerZoneVolume === 255"
              ></v-slider>
            </v-col>
          <v-col cols="12" v-show="editAudioMixerMasterSwitch === 1">
            <v-row>
              <v-col cols="6" class="ml-0">
                <span class="ml-0 text-left font-weight-bold" style="font-size: 15px;">{{ $t('partitionSystem.labels.selectedPartitionCount') }}: {{ audioMixerPartitionSelectTransfer.length }}</span>
              </v-col>
              <v-col cols="6" class="mt-n3">
                <v-btn color="primary" class="ml-n12" @click="audioMixerPartitionSelectDialog = true">{{ $t('partitionSystem.buttons.partitionSelection') }}</v-btn>
              </v-col>
            </v-row>
          </v-col>
          </v-row>
        </v-container>
        <v-card-actions>

          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="editAudioMixerDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="saveAudioMixerSettings()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--混音设置-分区选择对话框-->
    <v-dialog
      v-model="audioMixerPartitionSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.partitionSelection') }}</span>
        </v-card-title>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="audioMixerPartitionSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('partitionSystem.transferLabels.enterPartitionKeyword')"
            :data="myZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('partitionSystem.transferLabels.partitionList'), $t('partitionSystem.transferLabels.selectedPartitions')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="audioMixerPartitionSelectDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!--电话网关设置-->
    <v-dialog
      v-model="editPhoneGatewayDialog"
      max-width="500px"
      transition
    >
      <v-card>
        <v-toolbar
          color="primary"
        >
          <v-toolbar-title style="font-size: 2.0em;">{{ $t('partitionSystem.dialogs.parameterSettings') }}</v-toolbar-title>
        </v-toolbar>
        <v-container>
          <v-row>
            <v-col cols="2" class="mt-1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.functionSwitch') }}</span>
            </v-col>
            <v-col cols="10">
              <v-radio-group
                v-model="editPhoneGatewayMasterSwitch"
                row
                class="mt-n1"
              >
                <v-radio
                  :label="$t('partitionSystem.radioLabels.off')"
                  color="primary"
                  :value="Number(0)"
                />
                <v-radio
                  :label="$t('partitionSystem.radioLabels.on')"
                  color="primary"
                  :value="Number(1)"
                />
              </v-radio-group>
            </v-col>
            </v-radio-group>
            <v-col cols="12" class="mt-n4 mb-n4" v-show="editPhoneGatewayMasterSwitch === 1">
              <v-text-field
                v-model="editPhoneGatewayTelWhitelist"
                :label="$t('partitionSystem.inputLabels.phoneWhitelist')"
                counter="255"
                clearable
                :rules="[localizedRules.telephone]"
              />
            </v-col>
            <v-col cols="12" class="mt-n4 mb-n4" v-show="editPhoneGatewayMasterSwitch === 1">
                  <v-switch  :label="$t('partitionSystem.inputLabels.followDeviceVolume')" v-model="isPhoneGatewayVolumeFollowDevice" />
            </v-col>
            <v-col cols="2" class="mt-1" v-show="editPhoneGatewayMasterSwitch === 1">
              <span class="font-weight-bold">{{ $t('partitionSystem.labels.partitionVolume') }}</span>
            </v-col>
            <v-col cols="10" v-show="editPhoneGatewayMasterSwitch === 1">
              <v-slider
                :value="editPhoneGatewayZoneVolume"
                @change.passive="v => editPhoneGatewayZoneVolume = v"
                append-icon="mdi-volume-plus"
                prepend-icon="mdi-volume-minus"
                @click:append="plusTriggerVolume"
                @click:prepend="minusTriggerVolume"
                :thumb-size="24"
                thumb-label="always"
                :disabled="editPhoneGatewayZoneVolume === 255"
              ></v-slider>
            </v-col>
          <v-col cols="12" v-show="editPhoneGatewayMasterSwitch === 1">
            <v-row>
              <v-col cols="6" class="ml-0">
                <span class="ml-0 text-left font-weight-bold" style="font-size: 15px;">{{ $t('partitionSystem.labels.selectedPartitionCount') }}: {{ phoneGatewayPartitionSelectTransfer.length }}</span>
              </v-col>
              <v-col cols="6" class="mt-n3">
                <v-btn color="primary" class="ml-n12" @click="phoneGatewayPartitionSelectDialog = true">{{ $t('partitionSystem.buttons.partitionSelection') }}</v-btn>
              </v-col>
            </v-row>
          </v-col>
          </v-row>
        </v-container>
        <v-card-actions>

          <v-spacer />
          <v-btn
            color="primary darken-1"
            text
            @click="editPhoneGatewayDialog = false"
          >
            {{ $t('common.cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            text
            @click="savePhoneGatewaySettings()"
          >
            {{ $t('common.confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!--电话网关设置-分区选择对话框-->
    <v-dialog
      v-model="phoneGatewayPartitionSelectDialog"
      width="unset"
      transition
    >
      <v-card>
        <v-card-title>
          <span class="headline">{{ $t('partitionSystem.dialogs.partitionSelection') }}</span>
        </v-card-title>
        <div class="pt-3 text-center mx-4">
          <el-transfer
            v-model="phoneGatewayPartitionSelectTransfer"
            :filter-method="filterZoneByIpOrName"
            filterable
            :filter-placeholder="$t('partitionSystem.transferLabels.enterPartitionKeyword')"
            :data="myZones"
            :props="{
              key: 'mac'
            }"
            :titles="[$t('partitionSystem.transferLabels.partitionList'), $t('partitionSystem.transferLabels.selectedPartitions')]"
          >
            <span slot-scope="{ option }" :class="option.source === -1 ? 'darkOfflineZone' : ''">{{ option.name }}</span>
          </el-transfer>
        </div>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="blue darken-1"
            text
            @click="phoneGatewayPartitionSelectDialog = false"
          >
            {{ $t('common.close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 功放控制器状态对话框 -->
    <v-dialog
      v-model="editAmpControlerDialog"
      max-width="500px"
      transition
    >
      <div class="amp-dialog">
          <div class="dialog-header">
              <div class="header-title">
                  <i class="fas fa-volume-up"></i> {{ $t('partitionSystem.labels.amplifierControllerStatus') }}
              </div>
              <button class="close-btn" @click="editAmpControlerDialog = false">
                  <i class="fas fa-times"></i>
              </button>
          </div>
          
          <div class="dialog-content">
              <div class="status-summary">
                  <div class="summary-item">
                      <div class="summary-count count-normal">{{ ampStatusCounts.normal }}</div>
                      <div class="summary-label">{{ $t('partitionSystem.statusLabels.normal') }}</div>
                  </div>
                  <div class="summary-item">
                      <div class="summary-count count-fault">{{ ampStatusCounts.fault }}</div>
                      <div class="summary-label">{{ $t('partitionSystem.statusLabels.fault') }}</div>
                  </div>
                  <div class="summary-item">
                      <div class="summary-count count-idle">{{ ampStatusCounts.idle }}</div>
                      <div class="summary-label">{{ $t('partitionSystem.statusLabels.idle') }}</div>
                  </div>
              </div>
              
              <div class="amps-container">
                  <!-- 主功放状态卡片 -->
                  <div 
                      v-for="(status, index) in editAmpControlerMasterStatus" 
                      :key="'master-'+index"
                      class="amp-status-card"
                  >
                      <div 
                          class="status-indicator" 
                          :class="statusClass(status)"
                      ></div>
                      <div class="amp-name">{{ $t('partitionSystem.labels.masterAmplifier') }} {{ index + 1 }}</div>
                      <div class="amp-status" :class="statusClass(status)">
                          {{ statusText(status) }}
                      </div>
                  </div>
                  
                  <!-- 备用功放状态卡片 -->
                  <div class="amp-status-card">
                      <div 
                          class="status-indicator" 
                          :class="statusClass(editAmpControlerBackupStatus)"
                      ></div>
                      <div class="amp-name">{{ $t('partitionSystem.labels.backupAmplifier') }}</div>
                      <div class="amp-status" :class="statusClass(editAmpControlerBackupStatus)">
                          {{ statusText(editAmpControlerBackupStatus) }}
                      </div>
                  </div>
              </div>
              
              <div class="backup-section">
                  <div class="backup-header">
                      <div class="backup-icon">
                          <i class="fas fa-shield-alt"></i>
                      </div>
                      <div class="backup-title">{{ $t('partitionSystem.labels.backupAmplifierStatus') }}</div>
                  </div>
                  <div 
                      class="backup-status" 
                      :class="statusClass(editAmpControlerBackupStatus)"
                  >
                      {{ backupStatusText }}
                  </div>
              </div>
          </div>
      </div>
  
    </v-dialog>

    <!--##############################################-->
    <!--消息框-->
    <!--通用successSnackBar-->
    <v-snackbar
      v-model="commonSuccessSnackBar"
      color="primary"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ successMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="primary" fab small class="ml-6" v-bind="attrs" @click="commonSuccessSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--通用errorSnackBar-->
    <v-snackbar
      v-model="commonErrorSnackBar"
      color="error"
      :timeout="snackbarTimeout"
      centered
      multi-line
      content-class="snackbar-content"
      elevation="24"
      shaped
    >
      {{ errorMessages }}
      <template v-slot:action="{ attrs }">
        <v-btn color="error" fab small class="ml-6" v-bind="attrs" @click="commonErrorSnackBar = false">
          <v-icon>
            mdi-close-thick
          </v-icon>
        </v-btn>
      </template>
    </v-snackbar>
    <!--列表控制-->
    <v-container
      id="Partitions"
      fluid
      class="mt-n2"
      style="padding-bottom: 120px"
    >
      <v-card class="mb-2">
        <v-card-title style="margin-top: -20px">
          <v-switch
            v-model="isAllowDrag"
            :label="$t('partitionSystem.switchLabels.enableDeviceSort')"
            :class="isAllowDrag ? 'pt-7' : 'pt-2'"
            v-show="deviceType === 'decode' || deviceType === 'pager'">
          </v-switch>
          <v-radio-group
            v-show="!isAllowDrag"
            v-model="deviceType"
            row
            dense
            class="pl-4"
          >
            <v-radio
              :label="$t('deviceTypes.decodeTerminal')"
              color="primary"
              value="decode"
            />
            <v-radio
              :label="$t('deviceTypes.smartPager')"
              color="primary"
              value="pager"
            />
            <v-radio
              :label="$t('deviceTypes.audioCollector')"
              color="primary"
              value="audioCollector"
            />
            <v-radio
              :label="$t('deviceTypes.fireCollector')"
              color="primary"
              value="fire"
            />
            <v-radio
              :label="$t('deviceTypes.powerSequencer')"
              color="primary"
              value="power"
            />
            <v-radio
              v-if="checkIfShowRemoteControlers"
              :label="$t('deviceTypes.remoteController')"
              color="primary"
              value="remote"
            />
            <v-radio
              v-if="checkIfShowAudioMixers"
              :label="getAudioMixersDisplayLable"
              color="primary"
              value="mixer"
            />
            <v-radio
              v-if="checkIfShowPhoneGateways"
              :label="$t('deviceTypes.phoneGateway')"
              color="primary"
              value="phone"
            />
            <v-radio
              v-if="checkIfShowAmpControlers"
              :label="$t('deviceTypes.ampController')"
              color="primary"
              value="ampControler"
            />
            <v-radio
              v-if="checkIfShowNoiseDetectors"
              :label="$t('deviceTypes.noiseDetector')"
              color="primary"
              value="noise"
            />
          </v-radio-group>
          <div v-show="isAllowDrag" class="mt-6">
            <v-btn
              color="primary darken-1"
              text
              disabled
              max-width="60px"
              class="mx-12"
              style="font-weight: 700"
            >
              {{ $t('partitionSystem.labels.dragOrSelectPartitionSort') }}
            </v-btn>
            <v-btn
              color="primary"
              class="mx-2"
              @click="sortDeviceByName"
              :disabled="zoneListForSort.length === 0"
            >
              {{ $t('partitionSystem.buttons.sortByDeviceName') }}
            </v-btn>
            <v-btn
              color="primary"
              class="mx-2"
              @click="sortDeviceByIp"
              :disabled="zoneListForSort.length === 0"
            >
              {{ $t('partitionSystem.buttons.sortByDeviceIP') }}
            </v-btn>
            <v-btn
              fab
              color="primary"
              class="mx-3"
              :title="$t('partitionSystem.buttonTitles.moveUpSelectedPartition')"
              @click="zoneMoveUp"
              :disabled="zoneMacSelectedForSortIndex == null || zoneMacSelectedForSortIndex === 0"
            >
              <v-icon size="30">mdi-arrow-up-bold</v-icon>
            </v-btn>
            <v-btn
              fab
              color="primary"
              class="mx-3"
              :title="$t('partitionSystem.buttonTitles.moveDownSelectedPartition')"
              @click="zoneMoveDown"
              :disabled="isDragDownDisabled()"
            >
              <v-icon size="30">mdi-arrow-down-bold</v-icon>
            </v-btn>
            <v-btn
              fab
              color="primary"
              class="mx-3"
              :title="$t('partitionSystem.buttonTitles.cancelSortChanges')"
              @click="revertSorting"
              :disabled="!isZoneSortChange"
            >
              <v-icon size="30">mdi-undo</v-icon>
            </v-btn>
            <v-btn
              fab
              color="primary"
              class="mx-3"
              :title="$t('partitionSystem.buttonTitles.confirmSortChanges')"
              @click="confirmSorting"
              :disabled="!isZoneSortChange"
            >
              <v-icon size="30">mdi-check-bold</v-icon>
            </v-btn>
          </div>
          <v-spacer />
          <v-text-field
            v-show="!isAllowDrag"
            v-model="search"
            append-icon="mdi-magnify"
            :label="$t('common.search')"
            single-line
            hide-details
            full-width
            style="padding-bottom: 35px"
          />
        </v-card-title>
        <!-- 开启排序时的表格 -->
        <!-- ******** 关闭开启排序功能的表格的vuetify自带排序功能 -->
        <v-data-table
          v-show="isAllowDrag"
          :headers="draggableHeaders"
          :items="zoneListForSort"
          :disable-sort="true"
          item-key="mac"
          class="elevation-1 mt-2"
          :loading="zoneListForSort.length === 0"
          :loading-text="$t('partitionSystem.tableMessages.decoderPartitionNotExist')"
          style="margin-top: -10px"
          :items-per-page="sortItemPerPage"
          :page.sync="tablePageForSort"
          ref="myTable"
          @update:items-per-page="updateSortItemPerPage"
          :footer-props="{
            itemsPerPageOptions: [15,50,100,-1],
            showFirstLastPage: true,
            showCurrentPage: true,
            firstIcon: 'mdi-arrow-collapse-left',
            lastIcon: 'mdi-arrow-collapse-right',
            prevIcon: 'mdi-minus',
            nextIcon: 'mdi-plus'
          }"
          height="75vh"
        >
          <template #body="props">
            <draggable
              :list="props.items"
              tag="tbody"
              :disabled="!isAllowDrag"
              :move="onMoveCallback"
              :force-fallback="true"
              group="zone"
              animation="150"
              delay="50"
              @change="onZoneSortChange(props.items)"
              :fallback-tolerance="1"
              :scroll-sensitivity="250"
              chosenClass="chosen-zone-sort"
            >
              <data-table-row-handler
                v-for="(item, index) in props.items"
                :key="item.mac"
                :item="item"
                :headers="draggableHeaders"
              >
                <template v-slot:item.id="{ item }">
                  <span>{{ index + 1 }}</span>
                </template>
                <template v-slot:item.source="{ item }">
                  <span>{{ getMonitorStatus(item.source) }}</span>
                </template>
                <template v-slot:item.device_model="{ item }">
                  <span>{{ getDeviceType(item.device_model) }}</span>
                </template>
                <template v-slot:item.network_mode="{ item }">
                  <span>{{ getNetworkMode(item.network_mode) }}</span>
                </template>
                <template v-slot:item.monitor_mac="{ item }">
                  <span>{{ getMonitorNameByMac(item.monitor_mac) }}</span>
                </template>
                <template v-slot:item.module4g_csq="{ item }">
                  <span>{{ getMoudle4GCSQLevel(item.module4g_csq) }}</span>
                </template>

                <!--增加用户列-->
                <template v-if="isAdmin" v-slot:item.account="{ item }">
                  <v-tooltip
                    bottom
                    open-on-hover
                    open-delay="500"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on">{{ getZoneAccountDescription(item.mac) }}</span>
                    </template>
                    <span>{{ getZoneAccounts(item.mac) }}</span>
                  </v-tooltip>
                </template>
          </data-table-row-handler>
            </draggable>
          </template>
        </v-data-table>
        <!-- 不开启排序时的表格 -->
        <!-- ******** 当vuetify自带排序功能使用时，清空选中的分区，避免在当前页面找不到 -->
        <v-data-table
          v-show="!isAllowDrag"
          v-model="selected"
          :headers="headers"
          :custom-sort="customSort"
          :items="getEventList"
          :disable-sort="isTableSortingDisabled"
          :search="search"
          sort-by.sync="['source']"
          item-key="mac"
          :item-class="itemRowBackground"
          multi-sort
          class="elevation-1"
          single-select
          :loading="getEventList.length === 0"
          :loading-text="$t('partitionSystem.tableMessages.noCurrentTypeDeviceFound')"
          style="margin-top: -10px"
          :items-per-page="15"
          @click:row="rowClick"
          :page.sync="tablePage"
          fixed-header
          height="63vh"
          @update:sort-by="resetSelectedZone"
          :footer-props="{
            itemsPerPageOptions: [15,50,100],
            showFirstLastPage: true,
            showCurrentPage: true,
            firstIcon: 'mdi-arrow-collapse-left',
            lastIcon: 'mdi-arrow-collapse-right',
            prevIcon: 'mdi-minus',
            nextIcon: 'mdi-plus'
          }"
        >
          <template v-slot:item.id="{ item }">
            <span>{{ getEventList.map(function(x) {return x.mac; }).indexOf(item.mac) + 1 }}</span>
          </template>
          <template v-slot:item.source="{ item }">
            <span>{{ getMonitorStatus(item.source) }}</span>
          </template>
          <template v-slot:item.device_model="{ item }">
            <span>{{ getDeviceType(item.device_model) }}</span>
          </template>
          <template v-slot:item.network_mode="{ item }">
            <span>{{ getNetworkMode(item.network_mode) }}</span>
          </template>
          <template v-slot:item.monitor_mac="{ item }">
            <span>{{ getMonitorNameByMac(item.monitor_mac) }}</span>
          </template>
          <template v-slot:item.module4g_csq="{ item }">
            <span>{{ getMoudle4GCSQLevel(item.module4g_csq) }}</span>
          </template>
          <template v-slot:item.module4g_iccid="{ item }">
            <div style="max-width: 100px;">
              <span>{{ item.module4g_iccid }}</span>
            </div>
          </template>
          <!--增加用户列-->
          <template v-if="isAdmin" v-slot:item.account="{ item }">
            <v-tooltip
              bottom
              open-on-hover
              open-delay="500"
            >
              <template v-slot:activator="{ on, attrs }">
                <span v-bind="attrs" v-on="on">{{ getZoneAccountDescription(item.mac) }}</span>
              </template>
              <span>{{ getZoneAccounts(item.mac) }}</span>
            </v-tooltip>
          </template>

          <template v-slot:item.feature="{ item }">
            <!-- 蓝牙播放 -->
            <v-icon size="32px" :color="getColorOfBtPlay(item.feature)" v-show="deviceType === 'decode'" class="ma-2" :title="$t('partitionSystem.iconTitles.bluetoothPlay')">mdi-bluetooth</v-icon>
            <!-- 现场监听 -->
            <!-- <v-icon :color="getColorOfLiveCollection(item.feature)" class="ma-2" title="现场监听">mdi-account-voice</v-icon> -->
            <!-- 对讲 -->
            <v-icon size="32px" :color="getColorOfIntercom(item.feature)" v-show="deviceType === 'decode' || deviceType === 'pager'" class="ma-2" :title="$t('partitionSystem.iconTitles.voiceIntercom')">mdi-phone</v-icon>
            <!-- 删除设备 -->
            <v-icon
              size="32px"
              @click.native.stop="preDeleteDevice(item)"
              :color="getColorOfDeletingDevice(item.source)"
              class="ma-2" :title="$t('partitionSystem.iconTitles.deleteDevice')" >
              mdi-delete
            </v-icon>
          </template>
        </v-data-table>
      </v-card>
    </v-container>
    <!--分区管理按钮栏-->
    <v-footer
      v-show="!isAllowDrag"
      class="justify-center align-center"
      light
      absolute
      height="120px"
      max-height="120px"
    >
      <!-- 蓝牙设置 -->
      <v-btn
        v-show="deviceType === 'decode'"
        :disabled="!checkIfSupportBluetooth()"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preSetBlueTooth() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.bluetoothSettings') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'decode'"
        :disabled="!checkIfSupportIntercom() || queryIntercomLoading"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditIntercom() : showTipsForZoneSelection()"
        :loading="queryIntercomLoading"
      >
        {{ $t('partitionSystem.buttons.intercomSettings') }}
      </v-btn>
      <!-- SIP设置 -->
      <v-btn
        v-show="deviceType === 'decode'"
        :disabled="!checkIfSupportSip()"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditSip() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.sipSettings') }}
      </v-btn>
      </v-btn>
      <!-- 信息发布 -->
      <v-btn
        v-show="deviceType === 'decode'"
        :disabled="!checkIfSupportInformationPublish()"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditInformationPublish() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.informationPublish') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'decode'"
        class="mx-3"
        color="primary"
        @click="preMonitorDevice()"
      >
        {{ $t('partitionSystem.buttons.monitorSpeaker') }}
      </v-btn>
      <v-btn
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preRenameDevice() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.deviceName') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'decode'"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditVolume() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.deviceVolume') }}
      </v-btn>
      <v-btn
        v-if="isAdmin"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preUpdateDevice() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.deviceUpgrade') }}
      </v-btn>
      <v-btn
        v-if="isAdmin"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditIp() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.ipSettings') }}
      </v-btn>
      <v-btn
        v-if="isAdmin"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditNetworkMode() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.networkMode') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'decode'"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditSoundEffect() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.soundEffectSettings') }}
      </v-btn>
      <v-btn
        v-show="checkIfShowTrigger()"
        :disabled="!checkIfSupportTrigger() || queryTriggerLoading"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditTrigger() : showTipsForZoneSelection()"
        :loading="queryTriggerLoading"
      >
        {{ $t('partitionSystem.buttons.triggerSettings') }}
      </v-btn>
      <v-btn
        v-if="!isWindowsServer && !isCloudServer && isAdmin && deviceType === 'decode'"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preMonitorBind() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.monitorBinding') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'fire'"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditFireCollector() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.fireManagement') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'power'"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditPower() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.powerManagement') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'audioCollector'"
        :disabled="!checkIfSupportAudioCollectorParmConfig() || queryAudioCollectorLoading"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditAudioCollector() : showTipsForZoneSelection()"
        :loading="queryAudioCollectorLoading"
      >
        {{ $t('partitionSystem.buttons.audioCollectorSettings') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'mixer'"
        :disabled="queryAudioMixerLoading"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditAudioMixer() : showTipsForZoneSelection()"
        :loading="queryAudioMixerLoading"
      >
        {{ $t('partitionSystem.buttons.parameterSettings') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'phone'"
        :disabled="queryPhoneGatewayLoading"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditPhoneGateway() : showTipsForZoneSelection()"
        :loading="queryPhoneGatewayLoading"
      >
        {{ $t('partitionSystem.buttons.gatewaySettings') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'ampControler'"
        :disabled="queryAmpControlerLoading"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditAmpControler() : showTipsForZoneSelection()"
        :loading="queryAmpControlerLoading"
      >
        {{ $t('partitionSystem.buttons.ampSettings') }}
      </v-btn>

      <v-btn
        v-show="deviceType === 'noise'"
        :disabled="queryNoiseDetectorLoading"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditNoiseDetector() : showTipsForZoneSelection()"
        :loading="queryNoiseDetectorLoading"
      >
        {{ $t('partitionSystem.buttons.parameterSettings') }}
      </v-btn>

      <v-btn
        v-show="deviceType === 'remote'"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditRemoteController() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.taskManagement') }}
      </v-btn>
      <v-btn
        v-show="deviceType === 'remote'"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preEditRemoteControllerKey() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.keyManagement') }}
      </v-btn>
      <v-btn
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preRestartDevice() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.restartDevice') }}
      </v-btn>
      <v-btn
        v-if="isAdmin"
        class="mx-3"
        color="primary"
        @click="selected.length > 0 ? preResetDevice() : showTipsForZoneSelection()"
      >
        {{ $t('partitionSystem.buttons.resetDevice') }}
      </v-btn>
    </v-footer>
  </div>
</template>

<style>
.channel-card {
  border: 2px  #000000; /* 边框颜色和宽度 */
  border-radius: 10px; /* 边框圆角 */
}
</style>

<style scoped>
  /* 修改底部按钮中文字与边框的间距，第一个为竖向间距不需修改，第二个为横向间距需要修改 */
  .v-sheet button.v-btn.v-size--default:not(.v-btn--icon):not(.v-btn--fab) {
    padding: 12px 20px !important;
  }
  /deep/ tr.v-data-table__selected {
    background: #c2c9f3 !important;
  }
  .section-high-class >>> .v-select__selection {
    border-top-width: 3px;
    margin-top: 6px;
  }
  /* 调整被选中音源列表的背景颜色深度 */
  .theme--light.v-list-item--active:hover::before, .theme--light.v-list-item--active::before {
    opacity: 0.45;
  }
  /deep/ .v-select__selection--comma {
    margin: 7px 4px 7px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  /* 去除滑动条的刻度 */
  /deep/ .vue-slider-mark-step {
    display: none;
  }
  /deep/ .vue-slider-rail {
    background-color: whitesmoke;
    border-radius: 15px;
    -webkit-transition: background-color 0.3s;
    transition: background-color 0.3s;
  }
  .chosen-zone-sort {
    border-style: solid;
    border-width: 2px;
    /*border-color: var(--v-secondary-base) !important;*/
  }
  /*隐藏下划线*/
  .powerInfo-text-field >>> .v-input__slot:before {
    display: none;
  }
  .powerInfo-container .v-input--selection-controls {
    margin-top: -2px !important;
    padding-top: 5px !important;
  }
  /deep/ .v-list {
    overflow-y: auto
  }

  /*下面的全部是功放控制器页面的样式*/
  .amp-dialog {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    width: 100%;
    max-width: 500px;
    overflow: hidden;
    z-index: 100;
}

.dialog-header {
    background: linear-gradient(135deg, #1a73e8, #0d47a1);
    color: white;
    padding: 18px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.dialog-content {
    padding: 25px;
}

.status-summary {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.summary-item {
    text-align: center;
    flex: 1;
}

.summary-count {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.summary-label {
    font-size: 0.9rem;
    color: #666;
}

.count-normal { color: #4CAF50; }
.count-fault { color: #F44336; }
.count-idle { color: #2196F3; }

.amps-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.amp-status-card {
    background: #f9f9f9;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s;
    border: 1px solid #eee;
    position: relative;
    overflow: hidden;
}

.amp-status-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.amp-name {
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 1rem;
}

.amp-status {
    font-size: 0.85rem;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 20px;
    display: inline-block;
}

.status-normal {
    background: rgba(76, 175, 80, 0.15);
    color: #2E7D32;
}

.status-fault {
    background: rgba(244, 67, 54, 0.15);
    color: #C62828;
    animation: blink 1.5s infinite;
}

.status-idle {
    background: rgba(33, 150, 243, 0.15);
    color: #1565C0;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: absolute;
    top: 10px;
    right: 10px;
}

.indicator-normal { background: #4CAF50; }
.indicator-fault { 
    background: #F44336; 
    animation: blink 1.5s infinite;
}
.indicator-idle { background: #2196F3; }

.backup-section {
    background: #f0f7ff;
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #d0e3ff;
    margin-top: 15px;
}

.backup-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.backup-icon {
    width: 36px;
    height: 36px;
    background: #2196F3;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
}

.backup-title {
    font-weight: 600;
    color: #1565C0;
}

.backup-status {
    display: flex;
    justify-content: center;
    font-size: 1.1rem;
    font-weight: 500;
}

.footer-note {
    text-align: center;
    margin-top: 20px;
    font-size: 0.85rem;
    color: #777;
}

.control-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    width: 100%;
    max-width: 500px;
    overflow: hidden;
    margin-top: 20px;
}

.panel-header {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    padding: 15px 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.panel-content {
    padding: 20px;
}

.control-group {
    margin-bottom: 20px;
}

.control-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #444;
}

.master-controls {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.master-controls > div {
    display: flex;
    flex-direction: column;
}

select {
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid #ddd;
    background: white;
    font-size: 1rem;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.3; }
    100% { opacity: 1; }
}

@media (max-width: 768px) {
    .amps-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .master-controls {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .amps-container {
        grid-template-columns: 1fr;
    }
}
 /*上面的全部是功放控制器页面的样式*/
</style>

<script>
  import { mapGetters, mapState } from 'vuex'
  import { getStrLen } from '@/plugins/utils'
  import { getDevicePlayStatusMap, customerVersion } from '@/plugins/websocket'
  import DataTableRowHandler from "./DataTableRowHandler.vue";
  import draggable from "vuedraggable";
  import { deviceModelEnum, deviceFeatureEnum } from '@/store'
  import { getDeviceModelMappingList } from '@/store'
  import {querySubVolume, setSubVolume} from "../../../plugins/websocket";
  import {addTimes} from "../../../plugins/utils";
  import {utf8ToGb2312, gb2312ToUtf8} from "@/plugins/utils";

  // 这些常量将在组件中通过计算属性动态生成，以支持国际化
  const baseIntercomItems = [{name: 'None', mac: '00:00:00:00:00:00'}] // 将在组件中国际化
  // define UPGRADE_PEFIX_PAGER              ("P002")
  // define UPGRADE_PEFIX_IP_SPEAKER_A      ("P003")
  // define UPGRADE_PEFIX_IP_SPEAKER_B      ("P004")
  // define UPGRADE_PEFIX_IP_SPEAKER_C      ("P005")
  // define UPGRADE_PEFIX_AUDIO_COLLECTOR   ("P011")
  // define UPGRADE_PEFIX_FIRE_COLLECTOR    ("P012")
  const deviceFirmwareMap = new Map([
    [deviceModelEnum.NetPagerA, 'S002'],
    [deviceModelEnum.NetPagerB, 'S019'],
    [deviceModelEnum.NetPagerC, 'S020'],
    [deviceModelEnum.NetSpeakerA, 'S003'],
    [deviceModelEnum.NetSpeakerB, 'S004'],
    [deviceModelEnum.NetSpeakerC, 'S005'],
    [deviceModelEnum.NetSpeakerD, 'S006'],
    [deviceModelEnum.NetSpeakerE, 'S007'],
    [deviceModelEnum.NetAudioCollectorA, 'S011'],
    [deviceModelEnum.NetFireCollectorA, 'S012'],
    [deviceModelEnum.NetSequencePowerA, 'S013'],
    [deviceModelEnum.NetAudioCollectorB, 'S014'],
    [deviceModelEnum.NetFireCollectorB, 'S015'],
    [deviceModelEnum.NetSequencePowerB, 'S016'],
    [deviceModelEnum.NetAudioMixerDecoder, 'S017'],
    [deviceModelEnum.NetAudioMixerEncoder, 'S017'],
    [deviceModelEnum.NetRemoteControler, 'S018'],
    [deviceModelEnum.NetPhoneGateway, 'S021'],

    [deviceModelEnum.NetFireCollectorC, 'S022'],
    [deviceModelEnum.NetAudioCollectorC, 'S023'],
    [deviceModelEnum.NetSequencePowerC, 'S024'],
    [deviceModelEnum.NetAudioMixerDecoderC, 'S026'],
    [deviceModelEnum.NetAudioMixerEncoderC, 'S026'],
    [deviceModelEnum.NetRemoteControlerC, 'S025'],

    [deviceModelEnum.NetSpeakerF, 'S030'],
    [deviceModelEnum.NetFireCollectorF, 'S031'],
    [deviceModelEnum.NetAudioCollectorF, 'S032'],
    [deviceModelEnum.NetSequencePowerF, 'S033'],
    [deviceModelEnum.NetRemoteControlerF, 'S034'],

    [deviceModelEnum.NetSpeakerG, 'S035'],
    [deviceModelEnum.NetAmpControler, 'S036'],
    [deviceModelEnum.NetNoiseDetector, 'S037']

  ])
  export default {
    components: {
      draggable,
      DataTableRowHandler,
    },
    data() {
      return {
        restartSameTypeDeviceSwitch: false,
        resetSameTypeDeviceSwitch: false,
        emptyFirmwareArrary: [], // 将在 mounted 中更新
        onlyUpdateSelectedDeviceSwitch: true, // 仅升级已选择设备
        selectedFirmware: null, // 选中的固件版本
        uploadLoading: false,
        updateFirmwareFromServeSwitch: true,
        updateDeviceInfoLoading: false,
        successMessages: '', // 将在 mounted 中更新
        commonSuccessSnackBar: false,
        errorMessages: '', // 将在 mounted 中更新
        commonErrorSnackBar: false,
      renameDeviceDialog: false,
      deviceUpdateDialog: false,
      editDeviceIpDialog: false,
      editSoundEffectDialog: false,
      monitorBindingDialog: false,
      fireCollectorDialog: false,
      sequencePowerInfoDialog: false,
      restartDeviceDialog: false,
      resetDeviceDialog: false,
      deleteDeviceDialog: false, // 删除设备对话框
      deleteDeviceInfo: null, // 待删除的设备的信息
      resetDeviceConfirmDialog: false,
      newDeviceName: '',
      newIpAddress: '',
      deviceType: 'decode',
      // cleanDataType: '1',重置数据不需要分类，默认恢复出厂设置，即255
      subnetMark: '',
      gateway: '',
      snackbarTimeout: 1500,
      selected: [], // 已选择的分区，切换设备类型时应该清零
      pagination: {
        sortBy: 'id', // The field that you're sorting by
        descending: true,
      },
      uploadFile: null,
      autoGetIpAddress: false,
      search: '',
      // 音效设置 - 使用计算属性 localizedSoundEffectList
      soundEffectList: [], // 将在 mounted 中通过计算属性更新
      soundSelected: null,
      querySoundEffectFlag: false, // 主动查询设备音效的标志
      gain1: '0',
      gain2: '0',
      gain3: '0',
      gain4: '0',
      gain5: '0',
      gain6: '0',
      gain7: '0',
      gain8: '0',
      gain9: '0',
      gain10: '0',
      ticksLabels: [
        '-10', '-9', '-8', '-7', '-6', '-5', '-4', '-3', '-2', '-1', '0',
        '+1', '+2', '+3', '+4', '+5', '+6', '+7', '+8', '+9', '+10',
      ],
      isSetAllSameModelSoundEffect: false,
      gainsLabels: ['31', '62', '125', '250', '500', '1k', '2k', '4k', '8k', '16k'],
      inputNotAllowErrorMessage: '', // 将在 mounted 中更新
      // 消防设置
      fireChannelSelected: [],
      fireChannelHeaders: [], // 使用计算属性 localizedFireChannelHeaders
      powerInfoHeaders: [], // 使用计算属性 localizedPowerInfoHeaders
      fireChannelSearch: null,
      channelName: null,
      fireChannelTrigger: 0, // 消防通道触发方式
      fireChannelSound: null, // 消防通道告警音效
      fireChannelZones: [], // 消防通道绑定分区
      partitionSelectDialog: false,
      channelSongSelectDialog: false, // 消防通道告警音效选择
      partitionSelectTransfer: [],
      // 控制告警音效列表的打开和关闭 todo 修改为树形框 + 提示
      expandSongList: false,
      expandCollector: false,
      currentList: null,
      currentSong: null,
      currentSongPathName: null,
      // 监听相关
      setMonitorDialog: false,
      monitorPartitionSelectTransfer: [],
      // 蓝牙相关
      setBluetoothDialog: false,
      btName: null,
      btEncryption: false,
      btPin: null,
      bluetoothForm: false,
      queryBluetoothFlag: false,
      rules: {}, // 使用计算属性 localizedRules

      //SIP设置
      editSipDialog: false,
      deviceSipEnable: false,
      deviceSipProtocol: 0,
      deviceSipOutPutVolume: 80,
      deviceSipStatus: null,
      deviceSipServerIp: null,
      deviceSipServerPort: null,
      deviceSipAccount: null,
      deviceSipPassword: null,

      //信息发布
      editInformationPubDialog: false,
      deviceInformationPubDisplayEnable: false,
      deviceInformationPubText: "",
      deviceInformationPubEffects: 0,
      deviceInformationPubSpeed: 3,
      deviceInformationPubStayTime: 3,
      // 特效列表 - 使用计算属性 localizedEffectsList
      deviceInformationPubEffectsList: [], // 将在 mounted 中通过计算属性更新

      currentMonitorName: '',
      currentMonitorMac: null,
      monitorSelected: null,
      tablePage: 1,
      isAllowDrag: false, // 是否开启拖拽排序功能
      zoneListForSort: [], // 分区排序时使用的分区数据
      sortDeviceByNameAsc: true, // 按设备名称排序升序
      sortDeviceByIpAsc: true, // 按设备IP排序升序
      tablePageForSort: 1, // 分区排序时的页码
      sortItemPerPage: 15, // 分区排序的每页数目，默认为15
      zoneMacSelectedForSortIndex: null, // 分区排序选中分区的index
      isTableSortingDisabled: false, // 是否关闭表格表头排序功能
      // 电源管理
      updatedPowerControlMode: null, // 1手动 2自动
      updatedPowerChannels: [], // 更新后的电源信道信息
      powerInfoStatusRefreshKey: 0, // 刷新key
      // 网络模式
      editNetworkModeDialog: false,
      deviceNetworkMode: null,
      deviceNetworkServerIp: null,
      deviceNetworkServerPort: null,
      deviceNetworkServerIp2: null,
      deviceNetworkServerPort2: null,
      isSetAllSameModelNetworkMode: false,
      networkModeConfirmDialog: false,
      editVolumeDialog: false,
      isSetAllSameModelVolume: false,
      subVolume: 100,
      auxVolume: 100,
      // 任务管理
      taskSelected: [],
      taskSearch: null,
      taskHeaders: [], // 使用计算属性 localizedTaskHeaders
      taskManagerDialog: false, // 任务管理对话框
      isDeleteTask: false,
      deleteTaskDialog: false,
      taskDialog: false,
      isCreateTask: false,
      taskName: null,
      taskVolume: 50,
      taskPartitionSelectTransfer: [], // 参考Timer.vue的partitionSelectTransfer
      groupSelectTransfer: [],
      selectedPlayMode: null,
      selectedPlayList: null,
      selectedSongs: [],
      singleSelectedSongIndex: null,
      taskSourceType: 0, // 任务音源选择： 0表示歌曲播放，1表示音频采集
      audioCollectorSearch: null,
      audioCollectorActiveIds: [], // 通过代码逻辑设置为单选,取该数组中的第一个元素即为当前选中的通道
      audioCollectorOpenIds: [],
      lastActiveOpenId: null,
      // 播放模式 - 使用计算属性 localizedPlayModes
      playModes: [], // 将在 mounted 中通过计算属性更新
      deletedTaskActionInTable: null,
      editTaskActionInTable: null, // 通过右侧列表中的编辑图标选中的任务
      taskPartitionSelectDialog: false, // todo 后续部分需要修改
      groupSelectDialog: false,
      deleteTaskActionInTableDialog: false,
      // 按键管理
      keyManagerDialog: false, // 按键管理对话框
      keys: {
        key1: null,
        key2: null,
        key3: null,
        key4: null,
        key5: null,
        key6: null,
        key7: null,
        key8: null,
        key9: null,
        key10: null,
        key11: null,
        key12: null,
      },
      // 其他
      winHeight : window.innerHeight,
      render: true,

      // 对讲
      editIntercomDialog: false,
      isSetIntercomToAllModelDevice: false,
      key1MacForIntercom: '00:00:00:00:00:00',
      key2MacForIntercom: '00:00:00:00:00:00',
      intercomMicVolumeLevel:  5,
      intercomFarOutPutVolumeLevel:  5,
      queryIntercomLoading: false,

      // 触发
      queryTriggerLoading: false,
      editTriggerDialog: false,
      isSetTriggerToAllModelDevice: false,
      editTriggerSwitch: 0,
      editTriggerMode: 0,
      editTriggerSongPathName: null,
      editTriggerVolume: 0,
      editTriggerCurrentList: null,
      editTriggerCurrentSong: null,
      editTriggerCurrentSongPathName: null,
      editTriggerSongSelectDialog: false,

      //采播设置
      audioCollectorPartitionSelectDialog: false,
      audioCollectorPartitionSelectTransfer: [], // 参考Timer.vue的partitionSelectTransfer
      editAudioCollectorDialog: false,
      queryAudioCollectorLoading: false,
      editAudioCollectorPriority: 1,            //音源优先级（1-默认，低于定时音源，2-高优先级（高于定时音源））
      editAudioCollectorTriggerSwitch: 0,       //触发开关：0-关闭，1-开启
      editAudioCollectorTriggerChannelId: 1,    //触发通道ID（1~4）
      editAudioCollectorTriggerZoneVolume: 50,  //触发（0~100，255）

      // 混音设置
      audioMixerPartitionSelectDialog: false,
      audioMixerPartitionSelectTransfer: [], // 参考Timer.vue的partitionSelectTransfer
      editAudioMixerDialog: false,
      queryAudioMixerLoading: false,
      editAudioMixerMasterSwitch: 0,  //混音主开关：0-关闭，1-开启
      editAudioMixerPriority: 1,      //混音器优先级（1~9），数值越大，优先级越高
      editAudioMixerTriggerType: 1,   //触发类型（1~3），1-混合触发，2-MIC，3-AUX，默认为1
      editAudioMixerVolumeFadeLevel: 5, //存在MIC信号时，其他信号的淡化级别（0~9）
      editAudioMixerZoneVolume: 50,  //触发分区音量（0~100,255）

      // 电话网关设置
      phoneGatewayPartitionSelectDialog: false,
      phoneGatewayPartitionSelectTransfer: [], // 参考Timer.vue的partitionSelectTransfer
      editPhoneGatewayDialog: false,
      queryPhoneGatewayLoading: false,
      editPhoneGatewayMasterSwitch: 0,  //电话网关主开关：0-关闭，1-开启
      editPhoneGatewayZoneVolume: 50,  //触发分区音量（0~100,255）
      editPhoneGatewayTelWhitelist: null,  //电话白名单

      //功放控制器
      editAmpControlerDialog: false,
      queryAmpControlerLoading: false,
      editAmpControlerMasterStatus:[0, 0, 0, 0, 0],  // 0:空闲, 1:正常, 2:故障
      editAmpControlerBackupStatus: 0, // 0:空闲, 1:正常, 2:故障
      editAmpControlerBackupId:0,

      //噪声自适应器
      noiseDetectorPartitionSelectDialog: false,
      noiseDetectorPartitionSelectTransfer: [],
      editNoiseDetectorDialog: false,
      queryNoiseDetectorLoading: false,
      editNoiseDetectorSwitch: false,
      editNoiseDetectorChannelArray: [],
      editNoiseDetectorVolumeArray: [],

      audioCollectorChannels: [
        { channel_id:1, fixedName: 'CH1', channel_name: 'CH1' },
        { channel_id:2, fixedName: 'CH2', channel_name: 'CH2' },
        { channel_id:3, fixedName: 'CH3', channel_name: 'CH3' },
        { channel_id:4, fixedName: 'CH4', channel_name: 'CH4' }
      ]
    };
  },
    computed: {
      ...mapState(['eventList', 'setDeviceInfoResult', 'upgradeDeviceResult', 'setDeviceIpResult', 'existFirmwareNames',
                   'existFirmwareCount', 'uuid', 'uploadFirmwareProgress', 'uploadFirmwareResult', 'deviceUpgradeProgress',
                   'upgradeStatus', 'deviceUpgradeProgress', 'rebootDeviceResult', 'resetDeviceDataResult', 'getDeviceIpInfo',
                   'errorId', 'errorWsMessage', 'fireCollectorInfo', 'audioCollectorInfo', 'playList', 'audioMonitorDeviceMac', 'setAudioMonitorSpeakerResult',
                   'setFireChannelResult', 'queryBluetoothResult', 'setBluetoothResult', 'bluetoothName', 'bluetoothEncryption',
                   'bluetoothPin', 'queryEffectResult', 'effectGainInfo', 'setEffectResult', 'monitors', 'setMonitorBindingResult',
                    'commandName', 'isCloudServer', 'sortDeviceCustomResult', 'zoneMacSelectedForSort', 'deleteDeviceResult',
                    'sequencePowerInfo', 'setSequencePowerDeviceResult', 'queryDeviceNetworkModeResult', 'setDeviceNetworkModeResult',
                    'singleDeviceNetworkMode', 'singleDeviceNetworkServerIp', 'singleDeviceNetworkServerPort',
                    'singleDeviceNetworkServerIp2', 'singleDeviceNetworkServerPort2',
                    'queryDeviceSipInfoResult','setDeviceSipInfoResult','singleDeviceSipEnable','singleDeviceSipOutPutVolume','singleDeviceSipStatus','singleDeviceSipProtocol',
                    'singleDeviceSipServerIp','singleDeviceSipServerPort','singleDeviceSipAccount','singleDeviceSipPassword',
                    'queryDeviceInformationPubResult', 'setDeviceInformationPubResult', 'singleDeviceInformationPubDisplayEnable', 'singleDeviceInformationPubText',
                    'singleDeviceInformationEffects', 'singleDeviceInformationSpeed', 'singleDeviceInformationStayTime',
                    'zonesMacKeyAndAccountValueObject', 'setSubVolumeResult', 'subVolumeFromServer', 'auxVolumeFromServer', 'remoteControllerInfos',
                    'setRemoteControllerTaskResult', 'setRemoteControllerKeyResult', 'intercomSetResult',
                    'intercomKey1Mac', 'intercomKey2Mac', 'intercomMicVolume', 'intercomFarOutPutVolume',  'intercomQueryResult', 'intercomDeviceMac', 'queryTriggerResult',
                    'setTriggerResult', 'triggerSwitch', 'triggerMode', 'triggerSongPathName', 'triggerVolume', 'triggerDeviceMac',
                    'audioMixerMasterSwitch', 'audioMixerPriority', 'audioMixerTriggerType', 'audioMixerVolumeFadeLevel', 'audioMixerZoneVolume',
                    'queryAudioMixerParmResult', 'setAudioMixerParmResult', 'audioMixerDeviceMac', 'audioMixerZoneMacs',
                    'audioCollectorPriority', 'audioCollectorTriggerSwitch', 'audioCollectorTriggerChannelId', 'audioCollectorTriggerZoneVolume',
                    'queryAudioCollectorParmResult', 'setAudioCollectorParmResult', 'audioCollectorDeviceMac', 'audioCollectorTriggerZoneMacs',
                    'setPhoneGatewayParmResult', 'queryPhoneGatewayParmResult', 'phoneGatewayMasterSwitch', 'phoneGatewayZoneVolume', 'phoneGatewayTelWhitelist', 'phoneGatewayDeviceMac', 'phoneGatewayZoneMacs',
                    'queryAmpControlerParmResult', 'ampControlerMasterStatus', 'ampControlerBackupStatus', 'ampControlerBackupId', 'ampControlerDeviceMac',
                    'queryNoiseDetectorParmResult', 'noiseDetectorSwitch', 'noiseDetectorDeviceMac', 'noiseDetectorChannelArray', 'noiseDetectorVolumeArray', 'noiseDetectorZoneMacs', 'setNoiseDetectorParmResult'
                  ]),
      ...mapGetters(['apiSuccessMsg', 'isWindowsServer', 'isAdmin', 'audioCollectors']),
      // todo 重构任务部分，因与Timer.vue中一致
      // ############################# 任务部分开始

      currentRemoteControllerInfo() {
        if (this.getDevice.mac === '' || this.remoteControllerInfos.length === 0) {
          return undefined
        }
        return this.remoteControllerInfos.find(info => info.device_mac === this.getDevice.mac);
      },
      remoteControllerTasks() {
        return this.currentRemoteControllerInfo == null ? [] : this.currentRemoteControllerInfo.tasks
      },
      remoteControllerKeys() {
        return this.currentRemoteControllerInfo == null ? [] : this.currentRemoteControllerInfo.keys
      },
      remoteControllerTaskNames() {
        return this.remoteControllerTasks.length === 0 ? [] : this.remoteControllerTasks.map(task => task.name)
      },
      // 基础按键项目
      baseItems() {
        return [
          this.$t('partitionSystem.keyActions.none'),
          this.$t('partitionSystem.keyActions.pauseResume'),
          this.$t('partitionSystem.keyActions.stop'),
          this.$t('partitionSystem.keyActions.previous'),
          this.$t('partitionSystem.keyActions.next'),
          this.$t('partitionSystem.keyActions.volumeUp'),
          this.$t('partitionSystem.keyActions.volumeDown')
        ]
      },
      // 国际化的音效设置列表
      localizedSoundEffectList() {
        return [
          { text: this.$t('partitionSystem.soundEffects.off'), value: 0 },
          { text: this.$t('partitionSystem.soundEffects.pop'), value: 2 },
          { text: this.$t('partitionSystem.soundEffects.dance'), value: 3 },
          { text: this.$t('partitionSystem.soundEffects.rock'), value: 4 },
          { text: this.$t('partitionSystem.soundEffects.classical'), value: 5 },
          { text: this.$t('partitionSystem.soundEffects.vocal'), value: 6 },
          { text: this.$t('partitionSystem.soundEffects.soft'), value: 7 },
          { text: this.$t('partitionSystem.soundEffects.custom'), value: 1 },
        ]
      },
      // 国际化的播放模式列表
      localizedPlayModes() {
        return [
          { id: 3, name: this.$t('partitionSystem.playModes.sequential'), icon: 'mdi-playlist-play' },
          { id: 4, name: this.$t('partitionSystem.playModes.loop'), icon: 'mdi-repeat' },
          { id: 5, name: this.$t('partitionSystem.playModes.random'), icon: 'mdi-shuffle-variant' },
        ]
      },
      // 国际化的特效列表
      localizedEffectsList() {
        return [
          { text: this.$t('partitionSystem.effects.autoWithDescription'), value: 0 },
          { text: this.$t('partitionSystem.effects.pageFlipWithDescription'), value: 1 },
          { text: this.$t('partitionSystem.effects.continuousLeftMove'), value: 2 },
          { text: this.$t('partitionSystem.effects.leftMoveWithStay'), value: 3 },
          { text: this.$t('partitionSystem.effects.continuousDownMove'), value: 4 },
          { text: this.$t('partitionSystem.effects.downMoveWithStay'), value: 5 },
          { text: this.$t('partitionSystem.effects.blink'), value: 6 },
          { text: this.$t('partitionSystem.effects.continuousUpMove'), value: 7 },
          { text: this.$t('partitionSystem.effects.upMoveWithStay'), value: 8 },
          { text: this.$t('partitionSystem.effects.snow'), value: 9 },
        ]
      },
      // 国际化的表格头部
      localizedFireChannelHeaders() {
        return [
          { text: this.$t('common.number'), align: 'center', value: 'channel_id' },
          { text: this.$t('partitionSystem.labels.channelName'), value: 'name', align: 'center', sortable: false },
          { text: this.$t('partitionSystem.labels.triggerMode'), value: 'trigger', align: 'center', sortable: false },
          { text: this.$t('partitionSystem.labels.alarmSound'), value: 'sound', align: 'center', sortable: false },
          { text: this.$t('partitionSystem.labels.triggerPartitionCount'), value: 'zone_macs', align: 'center', sortable: false },
          { text: this.$t('partitionSystem.labels.triggerStatus'), value: 'status', align: 'center', sortable: false },
        ]
      },
      localizedPowerInfoHeaders() {
        return [
          { text: this.$t('common.number'), align: 'center', value: 'channel_id', width: '25%' },
          { text: this.$t('partitionSystem.labels.channelName'), value: 'name', align: 'center', sortable: false, width: '50%'},
          { text: this.$t('partitionSystem.labels.switch'), value: 'status', align: 'center', sortable: false, width: '25%'},
        ]
      },
      localizedTaskHeaders() {
        return [
          { text: this.$t('common.number'), value: 'task_id', align: 'center'},
          { text: this.$t('common.name'), value: 'name', align: 'center', sortable: false },
          { text: this.$t('partitionSystem.labels.playMode'), value: 'play_mode', align: 'center', sortable: false },
          { text: this.$t('partitionSystem.labels.playVolume'), value: 'volume', align: 'center', sortable: false },
          { text: this.$t('partitionSystem.labels.selectedPartitions'), value: 'zone_macs', align: 'center', sortable: false },
          { text: this.$t('partitionSystem.labels.selectedGroups'), value: 'group_ids', align: 'center', sortable: false },
          { text: this.$t('partitionSystem.labels.playSource'), value: 'song_pathnames', align: 'center', sortable: false },
          { text: this.$t('common.actions'), value: 'actions', align: 'center', sortable: false },
        ]
      },
      // 国际化的验证规则
      localizedRules() {
        return {
          required: v => !!v || this.$t('partitionSystem.messages.textCannotBeEmpty'),
          btName: v => !!(v || '').match(/^[A-Za-z0-9]{1,32}$/) || this.$t('partitionSystem.messages.bluetoothNameFormat'),
          btPin: v => !!(v || '').match(/^\d{4}$/) || this.$t('partitionSystem.messages.bluetoothPasswordFormat'),
          telephone: v => !!(v || '').match(/^\d{7,11}(,\d{7,11})*$/) || this.$t('partitionSystem.messages.phoneNumberFormat')
        }
      },
      // 组装按键下拉框数据
      keyEventItems() {
        // 获取任务名称
        if (this.remoteControllerTasks.length === 0) {
          return this.baseItems;
        }
        const taskNames = this.remoteControllerTaskNames
        return [...this.baseItems, ...taskNames]
      },
      // 基础对讲项目
      baseIntercomItems() {
        return [{name: this.$t('partitionSystem.keyActions.none'), mac: '00:00:00:00:00:00'}]
      },
      intercomItems() {
        if (this.myZones.length === 0) {
          return this.baseIntercomItems
        }
        // 解码终端E
        const intercomList = this.myZones.filter(zone => zone.mac !== this.getDevice.mac && zone.device_model === deviceModelEnum.NetSpeakerE)
        // 带有对讲特性的寻呼台
        // const pagerList = this.myPagers.filter(zone => (zone.feature & deviceFeatureEnum.Intercom))
        return [...this.baseIntercomItems, ...intercomList, ...this.myPagers]
      },
      intercomVolumeLevelItems() {
        return [1,2,3,4,5,6,7,8,9]
      },
      audioMixerPriorityItems() {
        return [1,2,3,4,5,6,7,8,9]
      },
      audioMixerVolumeFadeLevelItems() {
        const volumeFadeLevels = [
          "0 (0dB)",
          "1 (-3dB)",
          "2 (-6dB)",
          "3 (-9dB)",
          "4 (-12dB)",
          "5 (-15dB)",
          "6 (-18dB)",
          "7 (-21dB)",
          "8 (-24dB)",
          "9 (-27dB)"
        ];
        return volumeFadeLevels.map((item, index) => ({
          val: item,
          level: index
        }));
      },
      audioCollectorPriorityItems() {
        const priority = [
          this.$t('partitionSystem.priorities.default'),
          this.$t('partitionSystem.priorities.high')
        ];
        return priority.map((item, index) => ({
          val: item,
          id: index+1
        }));
      },
      audioCollectorChannelItems() {
        const channels = [
          "CH1",
          "CH2",
          "CH3",
          "CH4",
        ];
        return channels.map((item, index) => ({
          val: item,
          id: index+1
        }));
      },
      treeViewFilter () {
        // 搜索为大小写不敏感
        return (item, treeViewSearch, textKey) => {
          const text = item[textKey] != null ? item[textKey].toLowerCase() : ''
          const searchKey = treeViewSearch != null ? treeViewSearch.toLowerCase() : ''
          return text.indexOf(searchKey) > -1
        }
      },
      audioCollectorSearchTreeData() {
        if (this.audioCollectors.length === 0) {
          return []
        }
        const data = []
        this.audioCollectors.forEach(collector => {
          const children = []
          const channelSizes = 4
          for (let i = 1; i <= channelSizes; i++) {
            children.push({
              // 举例： 2C:21:C8:B8:66:1C|1
              id: collector.mac + '|' + i,
              name: 'CH' + i,
            })
          }
          data.push({
            id: collector.mac,
            name: collector.name,
            children: children
          })
        })
        return data
      },
      get_vcard_height() {
        // console.log("height1="+this.winHeight)
        if(this.winHeight > 900) {
          return 650
        }
        else {
          return 0.62*this.winHeight
        }
      },
      get_vlist_max_height() {
        // console.log("height2="+this.winHeight)
        if(this.winHeight > 900) {
          return 400
        }
        else {
          return 0.3*this.winHeight
        }
      },
      /*操作员显示自己的分组*/
      groupList() {
        return this.$store.getters.myGroupList
      },
      // 任务编辑-音源选择是否显示音频采集器
      checkIfShowAudioCollector () {
        return (customerVersion !== 'C4A2')
      },
      getTooltipForSelectedAudioCollector() {
        if (this.audioCollectorActiveIds.length === 0) {
          return this.$t('partitionSystem.messages.noAudioCollectorSelected')
        }
        const mac = this.audioCollectorActiveIds[0].split("|")[0]
        const channel = this.audioCollectorActiveIds[0].split("|")[1]
        return this.$util.getAudioCollectorDescription(this.$t('partitionSystem.tooltips.selectedAudioCollector'), mac, channel)
      },
      getSelectedPlayListSongs() {
        if (this.selectedPlayList == null) {
          return []
        }
        const list = this.playList.filter(list => list.list_id === this.selectedPlayList)
        return list == null ? [] : list[0].songs
      },
      // ############################# 任务部分结束
      // 表格显示头部
      headers() {
        const header = this.getBaseHeaders()
        if (this.isAdmin) {
          header.push({text: this.$t('common.user'), value: 'account', align: 'center', sortable: false})
        }
        header.push({text: this.$t('partitionSystem.labels.featuresAndOperations'), value: 'feature', align: 'center', sortable: false})
        return header;
      },
      // 适用于分区排序时的表格头部，需要去掉点击表头排序功能,去掉特性栏
      draggableHeaders() {
        const header = this.getBaseHeaders()
        if (this.isAdmin) {
          header.push({text: this.$t('common.user'), value: 'account', align: 'center', sortable: false})
        }
        header.forEach(v => v.sortable = false)
        return header
      },
      // 获取分区名称
      getZoneNamesOfFireChannels: function () {
        return this.$ws.getZoneNamesByZoneMacs(this.fireChannelZones)
      },
      // 获取所有解码设备
      myZones () {
        //********不显示音频协处理器-解码器
        //********在排序的时候显示
        if(this.isAllowDrag) {
          return this.$store.getters.decodeZones
        }
        else {
          return this.$store.getters.decodeZones.filter(zone=>zone.device_model !== deviceModelEnum.NetAudioMixerDecoder)
        }
      },
      // 获取所有寻呼台设备
      myPagers () {
        return this.$store.getters.pagers
      },
      // 获取所有音频采集器
      myAudioCollectors () {
        return this.$store.getters.audioCollectors
      },
      // 获取所有消防采集器
      myFireCollectors () {
        return this.$store.getters.fireCollectors
      },
      // 获取所有电源时序器
      mySequencePowers () {
        return this.$store.getters.sequencePowers
      },
      // 获取所有音频协处理器
      myAudioMixers () {
        return this.$store.getters.audioMixers
      },
      // 获取所有远程遥控器
      myRemoteControlers () {
        return this.$store.getters.remoteControlers
      },
      // 获取所有电话网关
      myPhoneGateways () {
        return this.$store.getters.phoneGateways
      },
      // 获取所有功放控制器
      myAmpControlers () {
        return this.$store.getters.ampControlers
      },
      // 获取所有的噪声自适应终端
      myNoiseDetectors () {
        return this.$store.getters.noiseDetectors
      },
      // 是否有显示音频协处理器的权限
      checkIfShowAudioMixers () {
        return (this.isNotCloudIpSystem())
      },
      // 获取音频协议处理器的显示名称
      getAudioMixersDisplayLable () {
        if(customerVersion === 'C3A0') {
          return this.$t('partitionSystem.deviceTypes.audioProcessor')
        }
        else {
          return this.$t('partitionSystem.deviceTypes.audioRelay')
        }
      },
      // 是否有显示远程遥控器的权限
      checkIfShowRemoteControlers () {
        return (this.isNotCloudIpSystem())
      },
      // 是否显示电话网关
      checkIfShowPhoneGateways () {
        return true
      },
      // 是否显示功放控制器
      checkIfShowAmpControlers () {
        //如果存在功放控制器，才显示
        return (this.myAmpControlers.length > 0)
      },
      // 是否显示噪声自适应终端
      checkIfShowNoiseDetectors () {
        //如果存在噪声自适应终端，才显示
        return (this.myNoiseDetectors.length > 0)
      },

      // 获取被选中的分区的信息
      getDevice: function () {
        if (this.selected.length > 0) {
          return this.selected[0]
        } else {
          const defaultDevice = [{
            name: '',
            id: '',
            device_model: '',
            mac: '',
            monitor_mac: '',
          }]
          return defaultDevice[0]
        }
      },
      // 获取被选中的消防通道的信息
      getFireChannel: function () {
        if (this.fireChannelSelected.length > 0) {
          return this.fireChannelSelected[0]
        } else {
          const defaultFireChannel = [{
            channel_id: '',
            name: '',
            trigger: 0,
            sound: '',
            zone_macs: [],
          }]
          return defaultFireChannel[0]
        }
      },
      // 获取不同类型的分区
      getEventList: function () {
        /*
        // 20210321 合并解码分区A/B/C显示
        if (this.deviceType === 'decode') {
          return this.myZones
        }
        */
        let modelInt = 0
        // modelInt = deviceModelEnum.NetPager;
        switch (this.deviceType) {
          case 'decode': return this.myZones;break
          case 'pager': return this.myPagers; break
          case 'fire': return this.myFireCollectors; break
          case 'audioCollector': return this.myAudioCollectors; break
          case 'power': return this.mySequencePowers;break
          case 'mixer': return this.myAudioMixers;break
          case 'remote': return this.myRemoteControlers;break
          case 'phone': return this.myPhoneGateways;break
          case 'ampControler': return this.myAmpControlers;break
          case 'noise': return this.myNoiseDetectors;break
        }

        return this.getEventListByType(modelInt)
      },
      // 获取当前设备的可用固件
      filterFirmwares: function () {
        const prefix = deviceFirmwareMap.get(this.getDevice.device_model)
        if (prefix !== null && prefix !== undefined) {
          // 20201222 筛选出升级文件名与预定义型号头匹配项以及（当前设备型号不为消防采集器且文件名包含'DSP'）的固件，因为DSP固件对于所有除消防采集器外的终端通用）
          // 20210118 筛选出升级文件名与预定义型号头匹配项以及（当前设备型号不为寻呼台且文件名包含'LOADER'）的固件，因为LOADER固件对于所有除寻呼台外的终端通用）
          // 20220302 对于解码终端D，不显示DSP固件和LOADER固件
          // 20220628 对于消防采集终端A、B,电源时序器A、B,音频采集器B,解码终端D,不显示DSP固件
          // 20220628 所有终端不显示LOADER固件
          // 20220707 对于音频协处理器，不显示DSP固件
          // 20220813 对于解码终端E,不显示DSP固件
          // 20220924 对于远程遥控器,不显示DSP固件
          let that = this
          return this.existFirmwareNames.filter(function (firmware) {
            if (firmware.indexOf(prefix) === 0)
              return true
            if (firmware.indexOf('DSP') !== -1) {
              if (that.getDevice.device_model !== deviceModelEnum.NetFireCollectorA
              && that.getDevice.device_model !== deviceModelEnum.NetFireCollectorB
              && that.getDevice.device_model !== deviceModelEnum.NetSequencePowerA
              && that.getDevice.device_model !== deviceModelEnum.NetSequencePowerB
              && that.getDevice.device_model !== deviceModelEnum.NetAudioCollectorB
              && that.getDevice.device_model !== deviceModelEnum.NetSpeakerD
              && that.getDevice.device_model !== deviceModelEnum.NetSpeakerE
              && that.getDevice.device_model !== deviceModelEnum.NetAudioMixerDecoder
              && that.getDevice.device_model !== deviceModelEnum.NetAudioMixerEncoder
              && that.getDevice.device_model !== deviceModelEnum.NetRemoteControler
              && that.getDevice.device_model !== deviceModelEnum.NetPhoneGateway)
                return true
            }
            /**
            if (firmware.indexOf('LOADER') !== -1) {
              if (that.getDevice.device_model !== deviceModelEnum.NetPager
              && that.getDevice.device_model !== deviceModelEnum.NetSpeakerD)
                return true
            }
            */
            return false
          })
        } else {
          return []
        }
      },
      // 获取当前的消防采集器通道信息
      getSelectedFireCollector: function () {
        if (this.fireCollectorInfo.length === 0) {
          return []
        }
        const fireCollector = this.fireCollectorInfo.filter(info => info.device_mac === this.getDevice.mac)
        if (fireCollector.length === 0) {
          return []
        }
        return fireCollector[0].channels
      },
      /**
       * 检查分区排序是否有修改
       * @returns {boolean}
       */
      isZoneSortChange() {
        if (this.myZones.length === 0 || this.zoneListForSort.length === 0) {
          return false
        }
        return !(JSON.stringify(this.zoneListForSort) === JSON.stringify(this.myZones))
      },
      // 获取当前的电源的通道信息
      selectedSequencePowerChannels() {
        if (this.sequencePowerInfo.length === 0 || this.getDevice.mac === '') {
          return []
        }
        const powerInfo = this.sequencePowerInfo.find(info => info.device_mac === this.getDevice.mac)
        if (powerInfo == null) {
          return []
        }
        return powerInfo.channels
      },
      selectedSequencePowerMode() {
        if (this.sequencePowerInfo.length === 0 || this.getDevice.mac === '') {
          return null
        }
        const powerInfo = this.sequencePowerInfo.find(info => info.device_mac === this.getDevice.mac)
        if (powerInfo == null) {
          return null
        }
        return powerInfo.mode
      },
      isSequencePowerDeviceUpdated() {
        if (this.updatedPowerChannels.length === 0 && this.selectedSequencePowerChannels.length === 0) {
          return true
        }
        const cleanArray = this.updatedPowerChannels.map(item=>{
          delete item.locked
          return item
        })
        return !(JSON.stringify(this.selectedSequencePowerChannels) === JSON.stringify(cleanArray));
      },
      isSequencePowerModeUpdated() {
        return this.updatedPowerControlMode !== this.selectedSequencePowerMode
      },
      isAudioMixerVolumeFollowDevice: {
        get() {
          return this.editAudioMixerZoneVolume === 255;
        },
        set(value) {
          this.editAudioMixerZoneVolume = value ? 255 : 50;
        }
      },
      isAudioCollectorVolumeFollowDevice: {
        get() {
          return this.editAudioCollectorTriggerZoneVolume === 255;
        },
        set(value) {
          this.editAudioCollectorTriggerZoneVolume = value ? 255 : 50;
        }
      },
      isPhoneGatewayVolumeFollowDevice: {
        get() {
          return this.editPhoneGatewayZoneVolume === 255;
        },
        set(value) {
          this.editPhoneGatewayZoneVolume = value ? 255 : 50;
        }
      },

      // 功放控制器状态计数统计
      ampStatusCounts() {
          const counts = { idle: 0, normal: 0, fault: 0 };
          
          // 统计主功放状态
          this.editAmpControlerMasterStatus.forEach(status => {
              if (status === 0) counts.idle++;
              else if (status === 1) counts.normal++;
              else if (status === 2) counts.fault++;
          });
          
          // 添加备用功放状态
          if (this.editAmpControlerBackupStatus === 0) counts.idle++;
          else if (this.editAmpControlerBackupStatus === 1) counts.normal++;
          else if (this.editAmpControlerBackupStatus === 2) counts.fault++;
          
          return counts;
      },
      
      // 备用功放状态文本
      backupStatusText() {
          if (this.editAmpControlerBackupStatus === 1) {
              return this.$t('partitionSystem.ampStatus.workingBackup', { id: this.editAmpControlerBackupId });
          }
          return this.statusText(this.editAmpControlerBackupStatus);
      },
      // 响应式设备状态映射，会随语言变化而更新
      deviceStatus() {
        return getDevicePlayStatusMap()
      },
      // 响应式设备类型映射，会随语言变化而更新
      deviceTypeMap() {
        return new Map([
          [deviceModelEnum.NetPagerA, this.$t('partitionSystem.deviceTypes.smartPagerA')],
          [deviceModelEnum.NetPagerB, this.$t('partitionSystem.deviceTypes.smartPagerB')],
          [deviceModelEnum.NetPagerC, this.$t('partitionSystem.deviceTypes.smartPagerC')],
          [deviceModelEnum.NetSpeakerA, this.$t('partitionSystem.deviceTypes.decoderTerminalA')],
          [deviceModelEnum.NetSpeakerB, this.$t('partitionSystem.deviceTypes.decoderTerminalB')],
          [deviceModelEnum.NetSpeakerC, this.$t('partitionSystem.deviceTypes.decoderTerminalC')],
          [deviceModelEnum.NetSpeakerD, this.$t('partitionSystem.deviceTypes.decoderTerminalD')],
          [deviceModelEnum.NetSpeakerE, this.$t('partitionSystem.deviceTypes.decoderTerminalE')],
          [deviceModelEnum.NetFireCollectorA, this.$t('partitionSystem.deviceTypes.fireCollectorA')],
          [deviceModelEnum.NetAudioCollectorA, this.$t('partitionSystem.deviceTypes.audioCollectorA')],
          [deviceModelEnum.NetSequencePowerA, this.$t('partitionSystem.deviceTypes.powerSequencerA')],
          [deviceModelEnum.NetFireCollectorB, this.$t('partitionSystem.deviceTypes.fireCollectorB')],
          [deviceModelEnum.NetAudioCollectorB, this.$t('partitionSystem.deviceTypes.audioCollectorB')],
          [deviceModelEnum.NetSequencePowerB, this.$t('partitionSystem.deviceTypes.powerSequencerB')],
          [deviceModelEnum.NetAudioMixerDecoder, this.$t('partitionSystem.deviceTypes.audioRelay')],
          [deviceModelEnum.NetAudioMixerEncoder, this.$t('partitionSystem.deviceTypes.audioRelay')],
          [deviceModelEnum.NetRemoteControler, this.$t('partitionSystem.deviceTypes.remoteController')],
          [deviceModelEnum.NetPhoneGateway, this.$t('partitionSystem.deviceTypes.phoneGateway')],
          [deviceModelEnum.NetFireCollectorC, this.$t('partitionSystem.deviceTypes.fireCollectorC')],
          [deviceModelEnum.NetAudioCollectorC, this.$t('partitionSystem.deviceTypes.audioCollectorC')],
          [deviceModelEnum.NetSequencePowerC, this.$t('partitionSystem.deviceTypes.powerSequencerC')],
          [deviceModelEnum.NetRemoteControlerC, this.$t('partitionSystem.deviceTypes.remoteControllerC')],
          [deviceModelEnum.NetAudioMixerDecoderC, this.$t('partitionSystem.deviceTypes.audioRelayC')],
          [deviceModelEnum.NetAudioMixerEncoderC, this.$t('partitionSystem.deviceTypes.audioRelayC')],
          [deviceModelEnum.NetSpeakerF, this.$t('partitionSystem.deviceTypes.decoderTerminalF')],
          [deviceModelEnum.NetFireCollectorF, this.$t('partitionSystem.deviceTypes.fireCollectorF')],
          [deviceModelEnum.NetAudioCollectorF, this.$t('partitionSystem.deviceTypes.audioCollectorF')],
          [deviceModelEnum.NetSequencePowerF, this.$t('partitionSystem.deviceTypes.powerSequencerF')],
          [deviceModelEnum.NetRemoteControlerF, this.$t('partitionSystem.deviceTypes.remoteControllerF')],
          [deviceModelEnum.NetSpeakerG, this.$t('partitionSystem.deviceTypes.decoderTerminalG')],
          [deviceModelEnum.NetAmpControler, this.$t('partitionSystem.deviceTypes.ampController')],
          [deviceModelEnum.NetNoiseDetector, this.$t('partitionSystem.deviceTypes.noiseAdaptiveTerminal')]
        ])
      },
    },
    watch: {
      // 监听语言变化
      '$i18n.locale'() {
        this.updateLocalizedTexts()
      },
      intercomSetResult() {
        if (this.intercomSetResult == null) {
          return
        }
        if (this.selected.length === 0 || this.intercomDeviceMac !== this.getDevice.mac) {
          this.$store.commit('updateIntercomQueryResult', null)
          return
        }
        // 对话框打开的情况下才显示
        if(this.editIntercomDialog === true) {
          this.successMessages = this.$t('partitionSystem.messages.intercomSettingsSuccess')
          this.commonSuccessSnackBar = true
        }
        this.editIntercomDialog = false
        this.$store.commit('updateIntercomSetResult', null)
      },
      intercomQueryResult() {
        if (this.intercomQueryResult == null) {
          return
        }
        if (!((this.queryIntercomLoading === true || this.editIntercomDialog === true) && this.selected.length > 0 && this.intercomDeviceMac === this.getDevice.mac)) {
          this.$store.commit('updateIntercomQueryResult', null)
          return
        }
        this.key1MacForIntercom = this.intercomKey1Mac
        this.key2MacForIntercom = this.intercomKey2Mac
        this.intercomMicVolumeLevel = this.intercomMicVolume
        this.intercomFarOutPutVolumeLevel = this.intercomFarOutPutVolume
        // 查询成功，打开对话框
        this.editIntercomDialog = true
        this.queryIntercomLoading = false
        this.$store.commit('updateIntercomQueryResult', null)
      },
      setTriggerResult() {
        if (this.setTriggerResult == null) {
          return
        }
        if (this.selected.length === 0 || this.triggerDeviceMac !== this.getDevice.mac) {
          this.$store.commit('updateSetTriggerResult', null)
          return
        }
        // 对话框打开的情况下才显示
        if(this.editTriggerDialog === true) {
          this.successMessages = this.$t('partitionSystem.messages.triggerSettingsSuccess')
          this.commonSuccessSnackBar = true
        }
        this.editTriggerDialog = false
        this.$store.commit('updateSetTriggerResult', null)
      },
      queryTriggerResult() {
        if (this.queryTriggerResult == null) {
          return
        }
        if (!((this.queryTriggerLoading === true || this.editTriggerDialog === true)
          && this.selected.length > 0 && this.triggerDeviceMac === this.getDevice.mac)) {
          this.$store.commit('updateQueryTriggerResult', null)
          return
        }
        this.editTriggerSwitch = this.triggerSwitch
        this.editTriggerMode = this.triggerMode
        this.editTriggerSongPathName = this.triggerSongPathName
        this.editTriggerVolume = this.triggerVolume
        // 查询成功，打开对话框
        this.editTriggerDialog = true
        this.queryTriggerLoading = false
        this.$store.commit('updateQueryTriggerResult', null)
      },
      setAudioCollectorParmResult() {
        if (this.setAudioCollectorParmResult == null) {
          return
        }
        if (this.selected.length === 0 || this.audioCollectorDeviceMac !== this.getDevice.mac) {
          this.$store.commit('updateSetAudioCollectorParmResult', null)
          return
        }
        // 对话框打开的情况下才显示
        if(this.editAudioCollectorDialog === true) {
          this.successMessages = this.$t('partitionSystem.messages.audioCollectorSettingsSuccess')
          this.commonSuccessSnackBar = true
        }
        this.editAudioCollectorDialog = false
        this.$store.commit('updateSetAudioCollectorParmResult', null)
      },
      queryAudioCollectorParmResult() {
        if (this.queryAudioCollectorParmResult == null) {
          return
        }
        if (!((this.queryAudioCollectorLoading === true || this.editAudioCollectorDialog === true)
          && this.selected.length > 0 && this.audioCollectorDeviceMac === this.getDevice.mac)) {
          this.$store.commit('updateQueryAudioCollectorParmResult', null)
          return
        }
        this.editAudioCollectorPriority = this.audioCollectorPriority
        this.editAudioCollectorTriggerSwitch = this.audioCollectorTriggerSwitch
        this.editAudioCollectorTriggerChannelId = this.audioCollectorTriggerChannelId
        this.editAudioCollectorTriggerZoneVolume = this.audioCollectorTriggerZoneVolume
        this.audioCollectorPartitionSelectTransfer = this.audioCollectorTriggerZoneMacs
        // 查询成功，打开对话框
        this.editAudioCollectorDialog = true
        this.queryAudioCollectorLoading = false
        this.$store.commit('updateQueryAudioCollectorParmResult', null)
      },
      setAudioMixerParmResult() {
        if (this.setAudioMixerParmResult == null) {
          return
        }
        if (this.selected.length === 0 || this.audioMixerDeviceMac !== this.getDevice.mac) {
          this.$store.commit('updateSetAudioMixerParmResult', null)
          return
        }
        // 对话框打开的情况下才显示
        if(this.editAudioMixerDialog === true) {
          this.successMessages = this.$t('partitionSystem.messages.parameterSettingsSuccess')
          this.commonSuccessSnackBar = true
        }
        this.editAudioMixerDialog = false
        this.$store.commit('updateSetAudioMixerParmResult', null)
      },
      queryAudioMixerParmResult() {
        if (this.queryAudioMixerParmResult == null) {
          return
        }
        if (!((this.queryAudioMixerLoading === true || this.editAudioMixerDialog === true)
          && this.selected.length > 0 && this.audioMixerDeviceMac === this.getDevice.mac)) {
          this.$store.commit('updateQueryAudioMixerParmResult', null)
          return
        }
        this.editAudioMixerMasterSwitch = this.audioMixerMasterSwitch
        this.editAudioMixerPriority = this.audioMixerPriority
        this.editAudioMixerTriggerType = this.audioMixerTriggerType
        this.editAudioMixerVolumeFadeLevel = this.audioMixerVolumeFadeLevel
        this.editAudioMixerZoneVolume = this.audioMixerZoneVolume
        this.audioMixerPartitionSelectTransfer = this.audioMixerZoneMacs
        // 查询成功，打开对话框
        this.editAudioMixerDialog = true
        this.queryAudioMixerLoading = false
        this.$store.commit('updateQueryAudioMixerParmResult', null)
      },
      setPhoneGatewayParmResult() {
        if (this.setPhoneGatewayParmResult == null) {
          return
        }
        if (this.selected.length === 0 || this.phoneGatewayDeviceMac !== this.getDevice.mac) {
          this.$store.commit('updateSetPhoneGatewayParmResult', null)
          return
        }
        // 对话框打开的情况下才显示
        if(this.editPhoneGatewayDialog === true) {
          this.successMessages = this.$t('partitionSystem.successMessages.setParametersSuccess')
          this.commonSuccessSnackBar = true
        }
        this.editPhoneGatewayDialog = false
        this.$store.commit('updateSetPhoneGatewayParmResult', null)
      },
      queryPhoneGatewayParmResult() {
        if (this.queryPhoneGatewayParmResult == null) {
          return
        }
        if (!((this.queryPhoneGatewayLoading === true || this.editPhoneGatewayDialog === true)
          && this.selected.length > 0 && this.phoneGatewayDeviceMac === this.getDevice.mac)) {
          this.$store.commit('updateQueryPhoneGatewayParmResult', null)
          return
        }
        this.editPhoneGatewayMasterSwitch = this.phoneGatewayMasterSwitch
        this.editPhoneGatewayZoneVolume = this.phoneGatewayZoneVolume
        this.phoneGatewayPartitionSelectTransfer = this.phoneGatewayZoneMacs
        this.editPhoneGatewayTelWhitelist = this.phoneGatewayTelWhitelist
        // 查询成功，打开对话框
        this.editPhoneGatewayDialog = true
        this.queryPhoneGatewayLoading = false
        this.$store.commit('updateQueryPhoneGatewayParmResult', null)
      },
      queryAmpControlerParmResult() {
        if (this.queryAmpControlerParmResult == null) {
          return
        }
        if (!((this.queryAmpControlerLoading === true || this.editAmpControlerDialog === true)
          && this.selected.length > 0 && this.ampControlerDeviceMac === this.getDevice.mac)) {
          this.$store.commit('updateQueryAmpControlerParmResult', null)
          return
        }
        this.editAmpControlerMasterStatus = this.ampControlerMasterStatus
        this.editAmpControlerBackupStatus = this.ampControlerBackupStatus
        this.editAmpControlerBackupId = this.ampControlerBackupId+1

        // 查询成功，打开对话框
        this.editAmpControlerDialog = true
        this.queryAmpControlerLoading = false
        this.$store.commit('updateQueryAmpControlerParmResult', null)
      },

      setNoiseDetectorParmResult() {
        if (this.setNoiseDetectorParmResult == null) {
          return
        }
        if (this.selected.length === 0 || this.noiseDetectorDeviceMac !== this.getDevice.mac) {
          this.$store.commit('updateSetNoiseDetectorParmResult', null)
          return
        }
        // 对话框打开的情况下才显示
        if(this.editNoiseDetectorDialog === true) {
          this.successMessages = this.$t('partitionSystem.successMessages.setParametersSuccess')
          this.commonSuccessSnackBar = true
        }
        this.editNoiseDetectorDialog = false
        this.$store.commit('updateSetNoiseDetectorParmResult', null)
      },
      queryNoiseDetectorParmResult() {
        if (this.queryNoiseDetectorParmResult == null) {
          return
        }
        if (!((this.queryNoiseDetectorLoading === true || this.editNoiseDetectorDialog === true)
          && this.selected.length > 0 && this.noiseDetectorDeviceMac === this.getDevice.mac)) {
          this.$store.commit('updateQueryNoiseDetectorParmResult', null)
          return
        }
        this.editNoiseDetectorSwitch = this.noiseDetectorSwitch
        this.editNoiseDetectorChannelArray = this.noiseDetectorChannelArray
        //this.editNoiseDetectorChannelArray的值要除以10
        this.editNoiseDetectorChannelArray = this.editNoiseDetectorChannelArray.map(item => item / 10)
        this.editNoiseDetectorVolumeArray = this.noiseDetectorVolumeArray
        this.noiseDetectorPartitionSelectTransfer = this.noiseDetectorZoneMacs

        // 查询成功，打开对话框
        this.editNoiseDetectorDialog = true
        this.queryNoiseDetectorLoading = false
        this.$store.commit('updateQueryNoiseDetectorParmResult', null)
      },

      setRemoteControllerTaskResult() {
        if(this.setRemoteControllerTaskResult === null)
          return
        if (this.isDeleteTask) {
          this.successMessages = this.$t('partitionSystem.successMessages.deleteRemoteControllerTaskSuccess');
          this.commonSuccessSnackBar = true
          this.deleteTaskDialog = false
          this.isDeleteTask = false
          this.deleteTaskActionInTableDialog = false
          this.taskSelected = []
          this.$store.commit('updateSetRemoteControllerTaskResult', null)
          return
        }

        this.successMessages = this.isCreateTask ? this.$t('partitionSystem.successMessages.addRemoteControllerTaskSuccess') : this.$t('partitionSystem.successMessages.editRemoteControllerTaskSuccess');
        this.commonSuccessSnackBar = true
        this.isDeleteTask = false
        this.taskDialog = false
        this.taskSelected = []
        this.taskName = ''
        this.$store.commit('updateSetRemoteControllerTaskResult', null)
      },
      setRemoteControllerKeyResult() {
        if(this.setRemoteControllerKeyResult === null)
          return
        this.successMessages = this.$t('partitionSystem.successMessages.setRemoteControllerKeySuccess')
        this.commonSuccessSnackBar = true
        this.keyManagerDialog = false
        this.$store.commit('updateSetRemoteControllerKeyResult', null)
      },
      audioCollectorActiveIds() {
        if (this.audioCollectorActiveIds.length > 1) {
          this.audioCollectorActiveIds = [this.audioCollectorActiveIds.slice(-1)[0]]
        }
      },
      audioCollectorOpenIds() {
        switch (this.audioCollectorOpenIds.length) {
          case 0:
            // 收起树形结构时，清空选中的采集器
            this.audioCollectorActiveIds = []
            break
          case 1:
            if (this.lastActiveOpenId != null && this.lastActiveOpenId !== this.audioCollectorOpenIds[0]) {
              // 收起树形结构时，清空选中的采集器
              this.audioCollectorActiveIds = []
            }
            this.lastActiveOpenId = this.audioCollectorOpenIds[0];
            break
          default:
            // 只能同时展开一个树形结构
            this.audioCollectorOpenIds = [this.audioCollectorOpenIds.slice(-1)[0]];
        }
      },
      // 关闭编辑任务对话框后，清空选中的编辑任务对象
      taskDialog() {
        if (!this.taskDialog) {
          this.editTaskActionInTable = null
        }
      },
      deleteTaskActionInTableDialog() {
        if (!this.deleteTaskActionInTableDialog) {
          this.deletedTaskActionInTable = null
        }
      },
      subVolumeFromServer() {
        if(this.subVolumeFromServer === null) {
          return
        }
        if (this.subVolumeFromServer !== this.subVolume) {
          this.subVolume = this.subVolumeFromServer
        }
        this.$store.commit('updateSubVolumeFromServer')
      },
      auxVolumeFromServer() {
        if(this.auxVolumeFromServer === null) {
          return
        }
        if (this.auxVolumeFromServer !== this.auxVolume) {
          this.auxVolume = this.auxVolumeFromServer
        }
        this.$store.commit('updateAuxVolumeFromServer')
      },
      setSubVolumeResult() {
        if (this.setSubVolumeResult === 0) {
          this.successMessages = this.$t('partitionSystem.successMessages.deviceAdvancedVolumeSetSuccess')
          this.commonSuccessSnackBar = true
          this.editVolumeDialog = false
          this.$store.commit('updateSetSubVolumeResult')
        }
      },
      queryDeviceNetworkModeResult() {
        this.deviceNetworkMode = this.singleDeviceNetworkMode
        this.deviceNetworkServerIp = this.singleDeviceNetworkServerIp
        this.deviceNetworkServerPort = this.singleDeviceNetworkServerPort == 0 ? "":this.singleDeviceNetworkServerPort
        this.deviceNetworkServerIp2 = this.singleDeviceNetworkServerIp2
        this.deviceNetworkServerPort2 = this.singleDeviceNetworkServerPort2 == 0 ? "":this.singleDeviceNetworkServerPort2
        this.$store.commit('updateQueryDeviceNetworkModeResult', null)
      },
      setDeviceNetworkModeResult() {
        this.successMessages = this.$t('partitionSystem.successMessages.modifyDeviceNetworkModeSuccess')
        this.commonSuccessSnackBar = true
        this.networkModeConfirmDialog = false
        this.$store.commit('updateSetDeviceNetworkModeResult', null)
        this.editNetworkModeDialog = false
      },

      queryDeviceSipInfoResult() {
        this.deviceSipEnable = this.singleDeviceSipEnable
        this.deviceSipProtocol = this.singleDeviceSipProtocol
        this.deviceSipOutPutVolume = this.singleDeviceSipOutPutVolume
        this.deviceSipStatus = this.singleDeviceSipStatus
        this.deviceSipServerIp = this.singleDeviceSipServerIp
        this.deviceSipServerPort = this.singleDeviceSipServerPort == 0 ? "5060":this.singleDeviceSipServerPort
        this.deviceSipAccount = this.singleDeviceSipAccount
        this.deviceSipPassword = this.singleDeviceSipPassword

        this.$store.commit('updateQueryDeviceSipInfoResult', null)
      },
      setDeviceSipInfoResult() {
        this.successMessages = this.$t('partitionSystem.successMessages.setSipAccountInfoSuccess')
        this.commonSuccessSnackBar = true
        this.$store.commit('updateSetDeviceSipInfoResult', null)
      },

      queryDeviceInformationPubResult() {
        this.deviceInformationPubDisplayEnable = this.singleDeviceInformationPubDisplayEnable
        //先将base64编码的字符串解码
        let decodeBase64 = atob(this.singleDeviceInformationPubText)
        //将GBK编码的decodeBase64转换为UTF-8编码
        // 打印十六进制表示
        //const hexString = Buffer.from(decodeBase64, 'binary').toString('hex');
        //console.log("十六进制字符:", hexString);
        let informationPubTextUTF8=gb2312ToUtf8(decodeBase64)
        this.deviceInformationPubText = informationPubTextUTF8
        this.deviceInformationPubEffects = this.singleDeviceInformationEffects
        this.deviceInformationPubSpeed = this.singleDeviceInformationSpeed
        this.deviceInformationPubStayTime = this.singleDeviceInformationStayTime

        this.$store.commit('updateQueryDeviceInformationPubResult', null)
      },
      setDeviceInformationPubResult() {
        this.successMessages = this.$t('partitionSystem.successMessages.setInformationPublishParametersSuccess')
        this.commonSuccessSnackBar = true
        this.$store.commit('updateSetDeviceInformationPubResult', null)
      },

      updatedPowerChannels() {
        this.powerInfoStatusRefreshKey += 1
      },
      selectedSequencePowerChannels() {
        if (this.sequencePowerInfoDialog === true) {
          // 如果已打开对话框，是否更新
        }
        this.updatedPowerChannels = JSON.parse(JSON.stringify(this.selectedSequencePowerChannels));
        this.updatedPowerChannels = this.updatedPowerChannels.map(obj => ({ ...obj, locked: true })) // 默认无法编辑
      },
      // todo 回显模式
      selectedSequencePowerMode() {
        this.updatedPowerControlMode = this.selectedSequencePowerMode
      },
      // 设备管理界面：搜索框变化时，清空已选中的设备
      search() {
        this.selected = []
      },
      // 统一错误处理
      errorId: function () {
        if (this.$route.fullPath !== '/dashboard/device') {
          return;
        }
        if (this.$store.state.errorId !== null) {
          this.errorMessages = this.$store.state.errorWsMessage
          this.commonErrorSnackBar = true
          // 关闭相关loading
          switch (this.commandName) {
            case 'set_device_info': this.updateDeviceInfoLoading = false; break;
            case 'get_sequence_power_info': this.powerInfoStatusRefreshKey += 1; break;
            case 'device_network_mode': this.networkModeConfirmDialog = false; break;
            default: break;
          }
        }
      },
      zoneMacSelectedForSort() {
        if (this.zoneMacSelectedForSort != null) {
          this.zoneMacSelectedForSortIndex = this.zoneListForSort.findIndex(x => x.mac === this.zoneMacSelectedForSort)
          return
        }
        this.zoneMacSelectedForSortIndex = null
      },
      isAllowDrag() {
        // ******** 开启分区排序时，关闭管理界面的vuetify自带表头排序功能
        this.isTableSortingDisabled = this.isAllowDrag;
        if (this.isAllowDrag) {
          // 重置页码
          this.tablePageForSort = 1
          this.sortItemPerPage = -1
          // 复制当前的解码设备分区列表
          if(this.deviceType === 'decode')
            this.zoneListForSort = JSON.parse(JSON.stringify(this.myZones))
          else if(this.deviceType === 'pager')
            this.zoneListForSort = JSON.parse(JSON.stringify(this.myPagers))
        } else {
          // 清空排序分区列表
          this.zoneListForSort = []
          this.$store.commit('updateZoneMacSelectedForSort', null)
        }
      },
      btEncryption () {
        if (this.setBluetoothDialog === false) {
          return
        }
        this.btPin = '0000'
      },
      queryBluetoothResult () {
        if (this.queryBluetoothResult !== 0) {
          this.$store.commit('updateQueryBluetoothResult', null)
          return
        }
        // 查询成功，回显值并打开蓝牙设置对话框
        this.btName = this.bluetoothName
        this.btEncryption = this.bluetoothEncryption
        this.btPin = this.bluetoothPin
        // 设置flag防止收到response就打开对话框
        if (this.queryBluetoothFlag === true) {
          this.setBluetoothDialog = true
        }
        this.queryBluetoothFlag = false
        // 重新赋值
        this.$store.commit('updateQueryBluetoothResult', null)
      },
      setBluetoothResult () {
        if (this.setBluetoothResult !== 0) {
          this.$store.commit('updateSetBluetoothResult', null)
          return
        }
        this.successMessages = this.$t('partitionSystem.successMessages.setPartitionBluetoothParametersSuccess')
        this.commonSuccessSnackBar = true
        this.setBluetoothDialog = false
        this.$store.commit('updateSetBluetoothResult', null)
        this.$refs.bluetoothForm.resetValidation()
      },
      // 20201118 切换上传固件和选择固件升级，清空已选择的固件
      updateFirmwareFromServeSwitch: function () {
        this.selectedFirmware = null
      },
      // 20201118如果设备类型修改，清空已选中的设备
      deviceType: function () {
        this.selected = []
        // 回到第一页
        this.tablePage = 1
      },
      fireChannelSelected: function () {
        // if (this.fireChannelSelected.length > 0) {
        //   // prepare data for fire channel
        //   this.channelName =
        // }
        this.channelName = this.getFireChannel.name
        this.fireChannelTrigger = this.getFireChannel.trigger
        this.fireChannelSound = this.getFireChannel.sound
        this.fireChannelZones = this.getFireChannel.zone_macs
        // if (this.fireChannelSelected.length > 0) {
        //   this.partitionSelectTransfer = this.$ws.getZoneIdsByZoneMacs(this.fireChannelZones)
        // }
      },
      // fireChannelZones: function () {
      //   if (this.fireChannelZones.length === 0) {
      //     this.partitionSelectTransfer = []
      //   } else {
      //     this.partitionSelectTransfer = this.$ws.getZoneIdsByZoneMacs(this.fireChannelZones)
      //   }
      // },
      // 只要dialog一关闭，映射到data
      partitionSelectDialog: function () {
        if (this.partitionSelectDialog) {
          this.partitionSelectTransfer = this.fireChannelZones
        } else {
          this.fireChannelZones = this.partitionSelectTransfer
        }
      },
      setDeviceInfoResult: function () {
        this.updateDeviceInfoLoading = false
        if (this.$store.state.setDeviceInfoResult === 0) {
          this.successMessages = this.$t('partitionSystem.successMessages.modifyDeviceInfoSuccess')
          this.commonSuccessSnackBar = true
          this.renameDeviceDialog = false
          this.$store.commit('updateSetDeviceInfoResult')
        }
      },
      // 该结果未使用，不需要监听
      upgradeDeviceResult: function () {
        if (this.$store.state.upgradeDeviceResult === 0) {
          this.successMessages = this.$t('partitionSystem.successMessages.upgradeFirmwareSuccess')
          this.commonSuccessSnackBar = true
          this.deviceUpdateDialog = false
        } else if (this.$store.state.upgradeDeviceResult !== null) {
          this.errorMessages = this.$t('partitionSystem.errorMessages.upgradeFirmwareFailed')
          this.commonErrorSnackBar = true
        }
        this.$store.commit('updateUpgradeDeviceResult')
      },
      setDeviceIpResult: function () {
        if (this.$store.state.setDeviceIpResult === 0) {
          this.successMessages = this.$t('partitionSystem.successMessages.modifyDeviceIpSuccess')
          this.commonSuccessSnackBar = true
          this.editDeviceIpDialog = false
          this.$store.commit('updateSetDeviceIpResult')
        }
      },
      uuid: function () {
        if (this.$route.fullPath !== '/dashboard/device') {
          return;
        }
        if (this.$store.state.uuid !== null) {
          this.$ws.getServerUploadFirmwares()
        }
      },
      uploadFirmwareResult: function () {
        this.uploadLoading = false
        if (this.$store.state.uploadFirmwareResult === null) {
          return
        }
        if (this.$store.state.uploadFirmwareResult) {
          this.successMessages = this.$t('partitionSystem.successMessages.uploadNewFirmwareSuccess')
          this.commonSuccessSnackBar = true
          this.$ws.getServerUploadFirmwares()
          // 跳转到固件页
          setTimeout(() => {
            this.updateFirmwareFromServeSwitch = true
          }, 300)
        } else {
          this.errorMessages = this.$t('partitionSystem.errorMessages.uploadNewFirmwareFailed')
          this.commonErrorSnackBar = true
        }
        this.$store.commit('updateUploadFirmwareResult', null)
        this.$store.commit('updateUploadFirmwareProgress', 0)
      },
      // 设备上传结果
      upgradeStatus: function () {
        const status = this.$store.state.upgradeStatus
        if (status === -1) {
          this.errorMessages = this.$t('partitionSystem.errorMessages.upgradeFailed')
          this.commonErrorSnackBar = true
        } else if (status === 1) {
          this.successMessages = this.$t('partitionSystem.successMessages.startUpgrade')
          this.commonSuccessSnackBar = true
        } else if (status === 2) {
          this.successMessages = this.$t('partitionSystem.successMessages.currentFirmwareIsLatest')
          this.commonSuccessSnackBar = true
        } else if (status === 3) {
          this.errorMessages = this.$t('partitionSystem.errorMessages.connectServerTimeout')
          this.commonErrorSnackBar = true
        } else if (status === 4) {
          this.errorMessages = this.$t('partitionSystem.errorMessages.upgradeFailed')
          this.commonErrorSnackBar = true
        } else if (status === 5) {
          this.successMessages = this.$t('partitionSystem.successMessages.upgradeCompleteStartingDevice')
          this.commonSuccessSnackBar = true
          this.deviceUpdateDialog = false
        }
      },
      deviceUpgradeProgress: function () {
        if (this.$store.state.deviceUpgradeProgress === 100) {
          this.successMessages = this.$t('partitionSystem.successMessages.upgradeComplete')
          this.commonSuccessSnackBar = true
          this.deviceUpdateDialog = false
        }
      },
      // 重启设备结果
      rebootDeviceResult: function () {
        if (this.$store.state.rebootDeviceResult === 0) {
          this.successMessages = this.$t('partitionSystem.successMessages.restartDeviceSuccess')
          this.commonSuccessSnackBar = true
          this.restartDeviceDialog = false
          this.$store.commit('updateRebootDeviceResult', null)
        }
      },
      // 重置设备数据结果
      resetDeviceDataResult: function () {
        if (this.$store.state.resetDeviceDataResult === 0) {
          this.successMessages = this.$t('partitionSystem.successMessages.resetDeviceDataSuccess')
          this.commonSuccessSnackBar = true
          this.resetDeviceConfirmDialog = false
          this.resetDeviceDialog = false
          this.$store.commit('updateResetDeviceDataResult', null)
        }
      },
      getDeviceIpInfo: function () {
        if (this.$store.state.getDeviceIpInfo === null) {
          return
        }
        this.newIpAddress = this.getDeviceIpInfo.device_ip
        this.subnetMark = this.getDeviceIpInfo.device_subnet
        this.gateway = this.getDeviceIpInfo.device_gatway
        if (this.getDeviceIpInfo.ip_mode === 0) {
          this.autoGetIpAddress = true
        } else if (this.getDeviceIpInfo.ip_mode === 1) {
          this.autoGetIpAddress = false
        }
      },
      setAudioMonitorSpeakerResult: function () {
        if (this.setAudioMonitorSpeakerResult === 0) {
          this.successMessages = this.apiSuccessMsg
          this.commonSuccessSnackBar = true
          this.setMonitorDialog = false
          this.$store.commit('updateSetAudioMonitorSpeakerResult', null)
        }
      },
      setFireChannelResult: function () {
        if (this.setFireChannelResult === 0) {
          this.successMessages = this.apiSuccessMsg
          this.commonSuccessSnackBar = true
          // todo 确定是否本地更新数据还是根据服务器响应更新 12/15
          this.getSelectedFireCollector.forEach(fireChannel => {
            if (fireChannel.channel_id === this.getFireChannel.channel_id) {
              // 更新名称，trigger, sound及macs
              fireChannel.name = this.channelName
              fireChannel.trigger = this.fireChannelTrigger
              fireChannel.sound = this.fireChannelSound
              fireChannel.zone_macs = this.fireChannelZones
            }
          })
          this.$store.commit('updateSetFireChannelResult', null)
        }
      },
      // 查询音效结果
      queryEffectResult () {
        // 2020/01/12 非主动查询音效，不弹出音效设置页面及渲染
        if (this.queryEffectResult == null || !this.querySoundEffectFlag) {
          return
        }
        if (this.queryEffectResult === 0) {
          this.soundSelected = this.effectGainInfo.eqMode
          this.gain1 = this.formatSlideValue(this.effectGainInfo.gain1)
          this.gain2 = this.formatSlideValue(this.effectGainInfo.gain2)
          this.gain3 = this.formatSlideValue(this.effectGainInfo.gain3)
          this.gain4 = this.formatSlideValue(this.effectGainInfo.gain4)
          this.gain5 = this.formatSlideValue(this.effectGainInfo.gain5)
          this.gain6 = this.formatSlideValue(this.effectGainInfo.gain6)
          this.gain7 = this.formatSlideValue(this.effectGainInfo.gain7)
          this.gain8 = this.formatSlideValue(this.effectGainInfo.gain8)
          this.gain9 = this.formatSlideValue(this.effectGainInfo.gain9)
          this.gain10 = this.formatSlideValue(this.effectGainInfo.gain10)
          this.editSoundEffectDialog = true
        }
        this.$store.commit('updateQueryEffectResult', null)
        this.querySoundEffectFlag = false
      },
      // 设置音效结果
      setEffectResult () {
        if (this.setEffectResult == null) {
          return
        }
        if (this.setEffectResult === 0) {
          this.successMessages = this.$t('partitionSystem.successMessages.setSoundEffectSuccess')
          this.commonSuccessSnackBar = true
          this.editSoundEffectDialog = false
        }
        this.$store.commit('updateSetEffectResult', null)
      },
      setMonitorBindingResult () {
        if (this.setMonitorBindingResult === 0) {
          this.successMessages = this.$t('partitionSystem.successMessages.monitorBindingSuccess')
          this.commonSuccessSnackBar = true
          this.monitorBindingDialog = false
          this.$store.commit('updateSetMonitorBindingResult', null)
        }
      },
      sortDeviceCustomResult() {
        if (this.sortDeviceCustomResult === 0) {
          this.successMessages = this.$t('partitionSystem.successMessages.partitionDeviceCustomSortSuccess')
          this.commonSuccessSnackBar = true
          // this.$store.commit('updateSortDeviceCustomResult', null)
          // 返回登录界面
          setTimeout(() => {
            this.$router.push('/')
          }, this.snackbarTimeout)
        }
      },
      deleteDeviceResult () {
        if (this.deleteDeviceResult === 0) {
          this.successMessages = this.$t('partitionSystem.successMessages.deleteDeviceSuccess')
          this.commonSuccessSnackBar = true
          this.deleteDeviceDialog = false
          this.deleteDeviceInfo = null
          this.$store.commit('updateDeleteDeviceResult', null)
        }
      },
      // 关闭删除设备对话框时，移除内存中的变量
      deleteDeviceDialog() {
        if (this.deleteDeviceDialog === false) {
          this.deleteDeviceInfo = null
        }
      },
      //不需要反馈状态，状态变化后WEB会下发，页面自动刷新。
      setSequencePowerDeviceResult() {
        if (this.setSequencePowerDeviceResult === 0) {
          //this.successMessages = '设置电源时序器参数成功'
          //this.commonSuccessSnackBar = true
          //this.sequencePowerInfoDialog = false
          //this.$store.commit('updateSetSequencePowerDeviceResult', null)
        }
      }
    },
    // 声明周期函数
    mounted () {
      this.selected = []
      window.addEventListener('resize', this.reload)
      // 更新国际化文本
      this.updateLocalizedTexts()
      // 初始化噪声/音量数组
      //this.initializeNoiseDetectorVolumeArray()
    },
    beforeDestroy () {
      window.removeEventListener('resize',this.reload)
    },
    methods: {
      // 更新国际化文本
      updateLocalizedTexts() {
        // 更新 data 中的国际化文本
        this.emptyFirmwareArrary = [this.$t('partitionSystem.messages.noFirmwareAvailable')]
        this.successMessages = this.$t('partitionSystem.messages.operationSuccess')
        this.errorMessages = this.$t('partitionSystem.messages.operationFailed')
        this.inputNotAllowErrorMessage = this.$t('partitionSystem.messages.nameConflictOrInvalid')

        // 使用计算属性更新数据结构
        this.fireChannelHeaders = this.localizedFireChannelHeaders
        this.powerInfoHeaders = this.localizedPowerInfoHeaders

        this.taskHeaders = this.localizedTaskHeaders
        this.soundEffectList = this.localizedSoundEffectList
        this.playModes = this.localizedPlayModes
        this.deviceInformationPubEffectsList = this.localizedEffectsList
        this.rules = this.localizedRules
      },
      reload() {
        this.render = false
        this.$nextTick(() => {
          this.winHeight = window.innerHeight
          this.render = true
        })
      },
     calculateTextUTF8ByteLength(ttsContent) {
        return new TextEncoder().encode(ttsContent || '').length;
     },
     informationPubTextLengthRules(index) {
        return [
          (value) => {
            const maxBytes = 240;
            const utf8Bytes = new TextEncoder().encode(value || '').length;
            return utf8Bytes <= maxBytes || this.$t('partitionSystem.messages.textLengthExceedsLimit', { maxBytes });
          }
        ];
    },
     audioCollectorChannelNameRules(index) {
        return [
          (value) => {
            const maxBytes = 32;
            const utf8Bytes = new TextEncoder().encode(value || '').length;
            return utf8Bytes < maxBytes || this.$t('partitionSystem.messages.channelNameExceedsLimit', { maxBytes });
          }
        ];
      },
      customSort(items, sortBy, sortDesc) {
        //console.log("sortBy="+sortBy+",sortDesc="+sortDesc)
        if (sortBy.length > 0 && sortBy[0] === 'ip') {
          return items.sort((a, b) => {
            const sortFactor = sortDesc[0] ? -1 : 1;
            const ipA = this.convertIpToNumber(a.ip);
            const ipB = this.convertIpToNumber(b.ip);
            if (ipA > ipB) {
              return sortFactor;
            } else if (ipA < ipB) {
              return -sortFactor;
            }
            return 0;
          });
        }
        else {
          return items.slice().sort((a, b) => {
          for (let i = 0; i < sortBy.length; i++) {
            const sortOrder = sortDesc[i] ? -1 : 1;
            const sortByField = sortBy[i];
            const columnValueA = a[sortByField];
            const columnValueB = b[sortByField];
            if (columnValueA < columnValueB) return -sortOrder;
            if (columnValueA > columnValueB) return sortOrder;
          }
          return 0;
        });
        }
      },
      isDragDownDisabled() {
        if (this.zoneMacSelectedForSortIndex == null) {
          return true;
        }
        return this.sortItemPerPage === -1
          ? this.zoneMacSelectedForSortIndex >= this.zoneListForSort.length - 1
          : this.zoneMacSelectedForSortIndex >= this.sortItemPerPage - 1
      },
      getBaseHeaders() {
        const header = [
          {text: this.$t('partitionSystem.headerLabels.serialNumber'), align: 'center', value: 'id'},
          {text: this.$t('partitionSystem.headerLabels.deviceName'), value: 'name', align: 'center', sortable: true},
          {text: this.$t('partitionSystem.headerLabels.deviceType'), value: 'device_model', align: 'center', sortable: true},
          {text: this.$t('partitionSystem.headerLabels.deviceIp'), value: 'ip', align: 'center', sortable: true},
          {text: this.$t('partitionSystem.headerLabels.mac'), value: 'mac', align: 'center', sortable: true},
          {text: this.$t('partitionSystem.headerLabels.networkMode'), value: 'network_mode', align: 'center', sortable: true}
        ]
        // 20210809 Windows版本：隐藏分区列表的'绑定摄像头'
        // 20210820 运行于云端时不显示绑定摄像头列
        if (!this.isWindowsServer && !this.isCloudServer) {
          header.push({text: this.$t('partitionSystem.headerLabels.boundCamera'), value: 'monitor_mac', align: 'center', sortable: false})
        }
        // 20230601 C4A0版本显示4G信号质量、ICCID
        if(this.isCloudIpSystem()) {
          header.push(
            {text: this.$t('partitionSystem.headerLabels.signalQuality'), value: 'module4g_csq', align: 'center', sortable: true},
            {text: this.$t('partitionSystem.headerLabels.iccid'), value: 'module4g_iccid', align: 'center', sortable: false}
          )
        }
        header.push(
          {text: this.$t('partitionSystem.headerLabels.firmwareVersion'), value: 'firmware_version', align: 'center', sortable: true},
          {text: this.$t('partitionSystem.headerLabels.runningStatus'), value: 'source', align: 'center'}
        )
        return header;
      },
      getZoneAccountDescription(mac) {
        if (Object.keys(this.zonesMacKeyAndAccountValueObject).length === 0 || !this.zonesMacKeyAndAccountValueObject[mac]) {
          return ''
        }
        const accountArray = this.zonesMacKeyAndAccountValueObject[mac]
        if (accountArray.length === 0) {
          return ''
        } else if (accountArray.length === 1) {
          return this.zonesMacKeyAndAccountValueObject[mac][0]
        } else {
          return this.zonesMacKeyAndAccountValueObject[mac][0] + this.$t('partitionSystem.userLabels.andOthers') + this.zonesMacKeyAndAccountValueObject[mac].length + this.$t('partitionSystem.userLabels.users')
        }
      },
      getZoneAccounts(mac) {
        if (Object.keys(this.zonesMacKeyAndAccountValueObject).length === 0 || !this.zonesMacKeyAndAccountValueObject[mac]) {
          return ''
        }
        const accountArray = this.zonesMacKeyAndAccountValueObject[mac]
        if (accountArray.length === 0) {
          return ''
        }
        return this.zonesMacKeyAndAccountValueObject[mac]
      },
      changePowerControlMode() {
        let mode = this.updatedPowerControlMode
        let updatedPowerChannels=null;
        let counts = 0
        this.$ws.setSequencePowerDevice(this.getDevice.mac, mode, counts, updatedPowerChannels)
      },
      openAllPowerChannel() {
        //this.updatedPowerChannels.forEach(x => x.status = 1)
        // this.powerInfoStatusRefreshKey += 1

        //这里考虑先不主动改变内存里的时序器参数，直接把新的发下去，然后待服务器通知时序器，时序器应答最新状态后再通知WEB,确保数据无误。
        //缺点是可能显示变化稍慢
        let updatedPowerChannels={};
        updatedPowerChannels=JSON.parse(JSON.stringify(this.updatedPowerChannels))
        updatedPowerChannels.forEach(x => x.status = 1)
        let counts = updatedPowerChannels.length
        let mode = null
        this.$ws.setSequencePowerDevice(this.getDevice.mac, mode, counts, updatedPowerChannels)
      },
      disableAllPowerChannel() {
        //this.updatedPowerChannels.forEach(x => x.status = 0)
        // this.powerInfoStatusRefreshKey += 1

        //这里考虑先不主动改变内存里的时序器参数，直接把新的发下去，然后待服务器通知时序器，时序器应答最新状态后再通知WEB,确保数据无误。
        //缺点是可能显示变化稍慢
        let updatedPowerChannels={};
        updatedPowerChannels=JSON.parse(JSON.stringify(this.updatedPowerChannels))
        updatedPowerChannels.forEach(x => x.status = 0)
        let counts = updatedPowerChannels.length
        let mode = null
        this.$ws.setSequencePowerDevice(this.getDevice.mac, mode, counts, updatedPowerChannels)
      },
      updateSequencePowerDevice() {
        let counts = null
        let infos = null
        let mode = null
        if (this.isSequencePowerModeUpdated) {
          mode = this.updatedPowerControlMode
        }
        if (this.isSequencePowerDeviceUpdated) {
          counts = this.updatedPowerChannels.length
          infos = this.updatedPowerChannels
        }
        this.$ws.setSequencePowerDevice(this.getDevice.mac, mode, counts, infos)
      },
      resetSequencePowerDevice() {
        this.updatedPowerChannels = JSON.parse(JSON.stringify(this.selectedSequencePowerChannels));
        this.updatedPowerChannels = this.updatedPowerChannels.map(obj => ({ ...obj, locked: true })) // 默认无法编辑
        this.updatedPowerControlMode = this.selectedSequencePowerMode
      },
      // 更新信道信息
      updatePowerChannelName(channelId, channelName) {
        //console.log('newName::::: ' + channelName)
        let counts = 1
        let mode = null
        const updateChannelSingle = this.updatedPowerChannels.filter(x => x.channel_id === channelId)
        const channel = updateChannelSingle[0]
        channel.name = channelName
        this.$ws.setSequencePowerDevice(this.getDevice.mac, mode, counts, updateChannelSingle)
      },
      updatePowerChannelStatus(channelId, channelStatus) {
        // console.log("channelId:::" + channelId + ":::::" + ":::::" + channelStatus)

        let counts = 1
        let mode = null

        const updateChannelSingle = this.updatedPowerChannels.filter(x => x.channel_id === channelId)
        const channel = updateChannelSingle[0]
        channel.status = (channelStatus === 0 ? 1 : 0) // 取反

        this.$ws.setSequencePowerDevice(this.getDevice.mac, mode, counts, updateChannelSingle)
      },
      powerAppendIconClick(channelId) {
        const channel = this.updatedPowerChannels.find(x => x.channel_id === channelId)
        channel.locked = !channel.locked // 取反
      },
      // 获取电源通道状态
      getValidPowerStatus: function (status) {
        return status === 1;
      },
      updateSortItemPerPage(newValue) {
        this.sortItemPerPage = newValue
      },
      sortDeviceByName() {
        this.zoneListForSort.sort((a, b) => this.sortDeviceByNameAsc ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name))
        this.sortDeviceByNameAsc = !this.sortDeviceByNameAsc
      },
      convertIpToNumber(ip) {
        const parts = ip.split('.');
        return parts.reduce((acc, val) => (acc << 8) + parseInt(val), 0);
      },
      sortDeviceByIp() {
        this.zoneListForSort.sort((a, b) => {
          const ipA = this.convertIpToNumber(a.ip);
          const ipB = this.convertIpToNumber(b.ip);
          return (this.sortDeviceByIpAsc ? ipA - ipB : ipB - ipA);
        });
        this.sortDeviceByIpAsc = !this.sortDeviceByIpAsc;
      },
      /**
       * 通用方法，移动数组中元素从指定index到达指定的index
       * @param data
       * @param from
       * @param to
       */
      moveItem(data, from, to) {
        // remove `from` item and store it
        const removedItem = data.splice(from, 1)[0];
        // insert stored item into position `to`
        data.splice(to, 0, removedItem);
      },
      /**
       * 控制是否允许分区进行排序
       * todo 增加单个分区Lock的选项
       * @param evt
       * @param originalEvent
       */
      onMoveCallback(evt, originalEvent) {
        // item为移动的对象，即zone属性
        // const item = evt.draggedContext.element;
        const originIndex = evt.draggedContext.index;
        const futureIndex = evt.draggedContext.futureIndex;

        // console.log("onMoveCallback:  " + item.id +  " ::::" + futureIndex);
        // this.moveItem(this.zoneListForSort, originIndex, futureIndex)
        // console.log('修改后： ' + JSON.stringify(this.zoneListForSort.map(x => x.mac)))
        return true;
      },
      onCloneCallback(item) {
        // Create a fresh copy of item
        console.log('onCloneCallback')
        return JSON.parse(JSON.stringify(item));
      },
      /**
       * 分区上移
       */
      zoneMoveUp() {
        this.moveItem(this.zoneListForSort, this.zoneMacSelectedForSortIndex, this.zoneMacSelectedForSortIndex - 1)
        this.zoneMacSelectedForSortIndex -= 1
      },
      /**
       * 分区下移
       */
      zoneMoveDown() {
        this.moveItem(this.zoneListForSort, this.zoneMacSelectedForSortIndex, this.zoneMacSelectedForSortIndex + 1)
        this.zoneMacSelectedForSortIndex += 1
      },
      /**
       * 排序后数组发生变化事件
       * 注意： 如果每页数目不为all，则传入的item只有当前每页数目数量，如15
       * @param items
       */
      onZoneSortChange(items) {
        if (items.length < this.zoneListForSort.length) {
          // 当每页数目非all时，替换掉指定index的元素
          this.zoneListForSort.splice(0, items.length, ...items)
        } else {
          this.zoneListForSort = items;
        }
      },
      /**
       * 取消排序修改
       * 暂不支持单次撤销
       */
      revertSorting() {
        this.zoneListForSort = JSON.parse(JSON.stringify(this.myZones))
      },
      /**
       * 确认排序修改并提交
       */
      confirmSorting() {
        if(this.deviceType === 'decode')
          this.$ws.sortDeviceCustom(this.zoneListForSort.map(x => x.mac), 0)
        else if(this.deviceType === 'pager')
          this.$ws.sortDeviceCustom(this.zoneListForSort.map(x => x.mac), 1)
      },
      showTipsForZoneSelection() {
        this.errorMessages = this.$t('partitionSystem.errorMessages.currentDeviceNotSelected')
        this.commonErrorSnackBar = true
      },
      // 设置特性蓝牙播放的颜色显示
      getColorOfBtPlay (feature) {
        return (feature & deviceFeatureEnum.BlueTooth) ? 'primary' : 'grey'
      },
      // 设置特性对讲的颜色显示
      getColorOfIntercom (feature) {
        return (feature & deviceFeatureEnum.Intercom) ? 'primary' : 'grey'
      },
      // 设置特性现场监听的颜色显示
      getColorOfLiveCollection (feature) {
        return (feature & deviceFeatureEnum.LiveMonitor) ? 'primary' : 'grey'
      },
      // 设置删除设备操作的颜色显示
      getColorOfDeletingDevice (source) {
        return source === -1 ? 'primary' : 'grey'
      },
      // 离线分区显示灰色
      itemRowBackground: function (item) {
        if (this.$vuetify.theme.dark) {
          return item.source === -1 ? 'darkOfflineZone' : '';
        }
        return item.source === -1 ? 'offlineZone' : 'onlineZone'
      },
      // 监听已选择的固件
      firmwareChange: function (name) {
        if (this.selectedFirmware == null || this.selectedFirmware !== name) {
          this.selectedFirmware = name
        } else {
          this.selectedFirmware = null
        }
      },
      // 主控制端点击即可选中一行
      rowClick: function (item, row) {
        // 取消离线提示
        if (this.deviceStatus.get(item.source + '') === this.$t('deviceStatus.offline')) {
          // this.errorMessages = '无法修改离线设备，请选择在线设备'
          // this.commonErrorSnackBar = true
          return
        }
        if (row.isSelected) {
          row.select(false)
        } else {
          row.select(true)
        }
      },
      // 消防管理点击即可选中一行
      fireChannelRowClick: function (item, row) {
        if (row.isSelected) {
          row.select(false)
        } else {
          row.select(true)
        }
      },
      // 重命名设备
      renameDevice: function () {
        const zonesName = this.$ws.getAllZonesName()
        if (this.newDeviceName == null || this.newDeviceName === '') {
          this.errorMessages = this.$t('partitionSystem.messages.deviceNameEmpty')
          this.commonErrorSnackBar = true
          return
        } else if (zonesName.indexOf(this.newDeviceName) !== -1) {
          this.errorMessages = this.$t('partitionSystem.messages.deviceNameExists')
          this.commonErrorSnackBar = true
          return
        } else if (getStrLen(this.newDeviceName) > 32) {
          this.errorMessages = this.$t('partitionSystem.messages.deviceNameTooLong')
          this.commonErrorSnackBar = true
          return
        }
        const sendData = {
          uuid: this.$store.state.uuid,
          command: 'set_device_info',
          device_model: this.getDevice.device_model,
          device_mac: this.getDevice.mac,
          device_name: this.newDeviceName,
          monitor_mac: this.getDevice.monitor_mac,
          set_type: 1,
        }
        this.updateDeviceInfoLoading = true
        this.$store.commit('WEBSOCKET_SEND', sendData)
      },
      // 获取运行状态
      getMonitorStatus: function (status) {
        const s = status + ''
        return this.deviceStatus.get(s)
      },
      // 获取类型
      getDeviceType: function (model) {
        const m = model
        if(m === deviceModelEnum.NetAudioMixerDecoder || m === deviceModelEnum.NetAudioMixerEncoder) {
          if(customerVersion === 'C3A0') {
            return this.$t('partitionSystem.deviceTypes.audioProcessor')
          }
          else {
            return this.$t('partitionSystem.deviceTypes.audioRelay')
          }
        }
        return this.deviceTypeMap.get(m)
      },
      // todo 自定义排序函数
      // 设备升级
      upgradeDevice: function () {
        if (this.selectedFirmware == null) {
          this.errorMessages = this.$t('partitionSystem.messages.selectFirmwareForUpgrade')
          this.commonErrorSnackBar = true
          return
        }
        // todo 交互
        this.$ws.upgradeFirmwares(this.selectedFirmware, this.onlyUpdateSelectedDeviceSwitch, this.getDevice.mac, this.getDevice.device_model)
      },
      // 上传新固件
      uploadFirmware: function () {
        if (this.uploadFile == null) {
          this.errorMessages = this.$t('partitionSystem.messages.selectFirmwareForUpload')
          this.commonErrorSnackBar = true
          return
        }
        // 固件上传：限定"*.tar.gz"
        const file = this.uploadFile
        const prefix = deviceFirmwareMap.get(this.getDevice.device_model)
        // 20201222 如果升级文件名与预定义型号头不匹配（若当前设备型号不为消防采集器且文件名包含'DSP'的情况下例外，因为DSP固件对于所有除消防采集器外的终端通用）或者文件扩展名不为tar.gz时，提示固件名称错误。
        // 20210118 如果升级文件名与预定义型号头不匹配（若当前设备型号不为寻呼台且文件名包含'LOADER'的情况下例外，因为LOADER固件对于所有除寻呼台外的终端通用）或者文件扩展名不为tar.gz时，提示固件名称错误。
        // 20220302 如果是解码终端D，不允许上传除预定义型号头外的文件
        // 20220628 如果是消防采集器A,B,电源时序器A,B,音频采集器B,解码终端D,不允许上传除预定义型号头外的文件
        // 20220628 所有终端不允许上传LOADER固件
        // 20220813 如果是解码终端E，不允许上传除预定义型号头外的文件
        // 20220924 如果是音频协处理器和远程遥控器，不允许上传除预定义型号头外的文件
		let valid = false
		if( !file.name.endsWith('.tar.gz') )
			valid = false
		else if(file.name.startsWith(prefix))
			valid = true
		else if(file.name.indexOf('DSP') !== -1) {
			if (this.getDevice.device_model !== deviceModelEnum.NetFireCollectorA
        && this.getDevice.device_model !== deviceModelEnum.NetFireCollectorB
        && this.getDevice.device_model !== deviceModelEnum.NetSequencePowerA
        && this.getDevice.device_model !== deviceModelEnum.NetSequencePowerB
        && this.getDevice.device_model !== deviceModelEnum.NetAudioCollectorB
        && this.getDevice.device_model !== deviceModelEnum.NetSpeakerD
        && this.getDevice.device_model !== deviceModelEnum.NetSpeakerE
        && that.getDevice.device_model !== deviceModelEnum.NetAudioMixerDecoder
        && that.getDevice.device_model !== deviceModelEnum.NetAudioMixerEncoder
        && this.getDevice.device_model !== deviceModelEnum.NetRemoteControler
        && this.getDevice.device_model !== deviceModelEnum.NetPhoneGateway)
				valid = true
		}
    /**
		else if(file.name.indexOf('LOADER') !== -1) {
			if (this.getDevice.device_model !== deviceModelEnum.NetPager && this.getDevice.device_model !== deviceModelEnum.NetSpeakerD)
				valid = true
		}
    */
		if (!valid) {
          this.errorMessages = this.$t('partitionSystem.messages.firmwareNameFormatError', { prefix: prefix })
          this.commonErrorSnackBar = true
          return
        }
        // 重复名称校验 2020.9.19去掉重复名校验
        // if (this.existFirmwareNames.indexOf(file.name) !== -1) {
        //   this.errorMessages = '当前固件服务器已存在，请重试'
        //   this.commonErrorSnackBar = true
        //   return
        // }
        this.uploadLoading = true
        this.$ws.uploadFirmware(file)
        // todo 上传结果使用watch进行渲染
      },
      // 修改IP地址
      updateIpAddress: function (autoGetIpAddress) {
        const sendData = {
          uuid: this.$store.state.uuid,
          command: 'device_ip',
          device_model: this.getDevice.device_model,
          device_mac: this.getDevice.mac,
          set: 1,
          ip_mode: autoGetIpAddress ? 0 : 1,
        }
        if (!autoGetIpAddress) {
          // todo 使用rules进行修改
          if (this.newIpAddress === '' || this.subnetMark === '' || this.gateway === '') {
            this.errorMessages = this.$t('partitionSystem.messages.ipAddressCannotBeEmpty')
            this.commonErrorSnackBar = true
            return
          }
        }
        sendData.device_ip = autoGetIpAddress ? '' : this.newIpAddress
        sendData.device_subnet = autoGetIpAddress ? '' : this.subnetMark
        sendData.device_gatway = autoGetIpAddress ? '' : this.gateway
        sendData.device_dns1 = '0'
        sendData.device_dns2 = '0'
        this.$store.commit('WEBSOCKET_SEND', sendData)
      },
      // 修改网络模式
      preUpdateNetwork() {
        this.networkModeConfirmDialog = true
      },
      updateNetworkMode: function () {
        const isSetAll = this.isSetAllSameModelNetworkMode ? 2 : 1
        this.$ws.setDeviceNetworkMode(isSetAll, this.getDevice.mac, this.getDevice.device_model,
          this.deviceNetworkMode, this.deviceNetworkServerIp, Number(this.deviceNetworkServerPort), this.deviceNetworkServerIp2, Number(this.deviceNetworkServerPort2))
      },
      editVolume: function () {
        const isSetAll = this.isSetAllSameModelVolume ? 2 : 1
        this.$ws.setSubVolume(isSetAll, this.getDevice.mac, this.getDevice.device_model, this.subVolume, this.auxVolume)
      },
      preRestartDevice: function () {
        this.restartSameTypeDeviceSwitch = false
        this.restartDeviceDialog = true
      },
      preResetDevice: function () {
        this.resetSameTypeDeviceSwitch = false
        this.resetDeviceConfirmDialog = false
        this.resetDeviceDialog = true
      },
      preRenameDevice: function () {
        this.newDeviceName = this.getDevice.name
        this.renameDeviceDialog = true
      },
      preEditVolume: function () {
        // 先恢复成默认值100，避免不支持设置子音量的终端显示错误
        this.subVolume = 100
        this.auxVolume = 100
        // 查询当前子音量并回显
        this.$ws.querySubVolume(this.getDevice.mac)
        setTimeout(() => this.editVolumeDialog = true, 50)
      },
      preEditIp: function () {
        this.$store.commit('updateGetDeviceIpInfo', null)
        // this.autoGetIpAddress = false
        // 发送请求获取当前ip
        const sendData = {
          uuid: this.$store.state.uuid,
          command: 'device_ip',
          set: 0, // 查询ip
          ip_mode: 0,
          device_ip: '1',
          device_subnet: '1',
          device_gatway: '1',
          device_dns1: '1',
          device_dns2: '1',
          device_model: this.getDevice.device_model,
          device_mac: this.getDevice.mac,
        }
        this.$store.commit('WEBSOCKET_SEND', sendData)
        setTimeout(() => {
          this.editDeviceIpDialog = true
        }, 50)
      },
      preEditNetworkMode: function () {
        // 每次点击网络模式按钮，均需发送device_network_mode查询该设备的网络模式信息
        this.$ws.queryDeviceNetworkMode(this.getDevice.mac, this.getDevice.device_model)
        // 每次重新进入，默认都是设置单个设备
        this.isSetAllSameModelNetworkMode = false
        // todo 每次重新进入，展示内存数据，待新数据更新后同步刷新页面（如果考虑到存在网络延迟，是否每次重新进入前，清空内存数据较好？）
        setTimeout(() => {
          this.editNetworkModeDialog = true
        }, 50)
      },
      preEditSip: function () {
        // 每次点击SIP设置按钮，均需发送set_sip_info查询该设备的SIP账号信息及状态
        this.$ws.queryDeviceSipInfo(this.getDevice.mac)
        setTimeout(() => {
          this.editSipDialog = true
        }, 50)
      },
      updateSipInfo: function () {
        this.$ws.setDeviceSipInfo(this.getDevice.mac, this.deviceSipEnable, this.deviceSipProtocol, this.deviceSipOutPutVolume,
          this.deviceSipAccount, this.deviceSipPassword, this.deviceSipServerIp, Number(this.deviceSipServerPort))
      },
      getSipStatusStr (status) {
        switch(status) {
          case 0:
            return this.$t('partitionSystem.status.registrationFailed')
          case 1:
            return this.$t('partitionSystem.status.registrationSuccess')
          case 2:
            return this.$t('partitionSystem.status.registering')
          case 3:
            return this.$t('partitionSystem.status.registrationFailed')
          case 4:
            return this.$t('partitionSystem.status.registrationTimeout')
          case 5:
            return this.$t('partitionSystem.status.accountPasswordError')
          case 6:
            return this.$t('partitionSystem.status.inCall')
        }
        // 默认返回注册失败
        return this.$t('partitionSystem.status.registrationFailed')
      },
      preEditInformationPublish: function () {
        // 每次点击SIP设置按钮，均需发送get_information_publish查询该设备的信息发布参数
        this.$ws.queryDeviceInformationPubInfo(this.getDevice.mac)
        setTimeout(() => {
          this.editInformationPubDialog = true
        }, 50)
      },
      updateInformationPublish: function () {
        //将UTF-8编码的deviceInformationPubText转换为GBK编码
        let informationPubTextGBK=utf8ToGb2312(this.deviceInformationPubText)
        // 打印 Buffer 内容及长度
        //console.log(informationPubTextGBK, informationPubTextGBK.length);
        this.$ws.setDeviceInformationPubInfo(this.getDevice.mac, this.deviceInformationPubDisplayEnable, informationPubTextGBK,
          Number(this.deviceInformationPubEffects), Number(this.deviceInformationPubSpeed), Number(this.deviceInformationPubStayTime))
      },
      getInformationPublishEffects (effects) {
        switch(effects) {
          case 0:
            return this.$t('partitionSystem.effects.auto')
          case 1:
            return this.$t('partitionSystem.effects.pageFlip')
          case 2:
            return this.$t('partitionSystem.effects.continuousLeftMove')
          case 3:
            return this.$t('partitionSystem.effects.leftMoveWithStay')
          case 4:
            return this.$t('partitionSystem.effects.continuousDownMove')
          case 5:
            return this.$t('partitionSystem.effects.downMoveWithStay')
          case 6:
            return this.$t('partitionSystem.effects.blink')
          case 7:
            return this.$t('partitionSystem.effects.continuousUpMove')
          case 8:
            return this.$t('partitionSystem.effects.upMoveWithStay')
          case 9:
            return this.$t('partitionSystem.effects.snow')
        }
        return this.$t('partitionSystem.effects.auto')
      },
      preEditTrigger() {
        // 每次进入触发设置，先发送查询
        this.$ws.queryTrigger(this.getDevice.mac, this.getDevice.device_model)
        // 每次重新进入，默认都是设置单个设备
        this.isSetTriggerToAllModelDevice = false
        this.editTriggerDialog = true
      },
      // 触发歌曲修改
      preUpdateTriggerSong: function () {
        // 回显
        this.editTriggerCurrentList = null
        this.expandCollector = false
        if (this.editTriggerSongPathName === null || this.editTriggerSongPathName === '') {
          this.editTriggerCurrentSong = null
          this.editTriggerCurrentSongPathName = null
        } else {
          this.editTriggerCurrentSong = this.editTriggerSongPathName.substring(this.editTriggerSongPathName.lastIndexOf('/') + 1)
          this.editTriggerCurrentSongPathName = this.editTriggerSongPathName
        }
        // this.editTriggerCurrentSongPathName = this.editTriggerSongPathName
        this.editTriggerSongSelectDialog = true
      },
      preUpdateDevice: function () {
        // this.$ws.getServerUploadFirmwares()  todo 是否需要每次打开设备升级检查有新固件包
        this.uploadFile = null
        this.uploadLoading = false
        this.$store.commit('updateUploadFirmwareProgress', 0)
        this.$store.commit('resetDeviceUpdateState')
        // 每次重新打开对话框，清空已选择的固件
        this.selectedFirmware = null
        this.deviceUpdateDialog = true
      },
      // 音效设置
      updateSoundEffect: function () {
        if (this.soundSelected === this.effectGainInfo.eqMode && this.soundSelected !== 1) {
          this.errorMessages = this.$t('partitionSystem.messages.soundEffectNotChanged')
          this.commonErrorSnackBar = true
          return
        }
        const soundEffect = {
          gain1: 0,
          gain2: 0,
          gain3: 0,
          gain4: 0,
          gain5: 0,
          gain6: 0,
          gain7: 0,
          gain8: 0,
          gain9: 0,
          gain10: 0,
        }
        if (this.soundSelected === 1) {
          soundEffect.gain1 = this.formatSlideValue(this.gain1)
          soundEffect.gain2 = this.formatSlideValue(this.gain2)
          soundEffect.gain3 = this.formatSlideValue(this.gain3)
          soundEffect.gain4 = this.formatSlideValue(this.gain4)
          soundEffect.gain5 = this.formatSlideValue(this.gain5)
          soundEffect.gain6 = this.formatSlideValue(this.gain6)
          soundEffect.gain7 = this.formatSlideValue(this.gain7)
          soundEffect.gain8 = this.formatSlideValue(this.gain8)
          soundEffect.gain9 = this.formatSlideValue(this.gain9)
          soundEffect.gain10 = this.formatSlideValue(this.gain10)
        }
        const eqType = this.isSetAllSameModelSoundEffect ? 2 : 1
        this.$ws.deviceEqMode(1, this.getDevice.mac, this.getDevice.device_model, eqType, this.soundSelected, soundEffect)
      },
      // 监控绑定
      updateBinding: function () {
        // todo 交互
        this.monitorBindingDialog = false
      },
      /**
       * 设置消防通道
       * @param allChannel 是否设置所有通道
       */
      updateFireCollector: function (allChannel) {
        if (this.fireChannelSelected.length === 0) {
          this.errorMessages = this.$t('partitionSystem.messages.selectFireCollectorChannel')
          this.commonErrorSnackBar = true
          return
        }
        // 12/15 前置判断
        if (this.channelName === '') {
          this.errorMessages = this.$t('partitionSystem.messages.channelNameCannotBeEmpty')
          this.commonErrorSnackBar = true
          return
        }
        if (this.fireChannelSound === null || this.fireChannelSound === '') {
          this.errorMessages = this.$t('partitionSystem.messages.channelAlarmSoundNotSet')
          this.commonErrorSnackBar = true
          return
        }
        if (this.fireChannelZones.length === 0) {
          this.errorMessages = this.$t('partitionSystem.messages.channelNotBoundToPartition')
          this.commonErrorSnackBar = true
        }
        // 1. 设置消防采集器
        // 2. 设置消防通道
        const uuid = this.$ws.getUuid()
        const channelCount = allChannel ? 32 : 1
        this.$ws.setFireDevice(uuid, this.getDevice.mac, channelCount)
        const fireChannel = {
          channel_id: this.getFireChannel.channel_id,
          name: this.channelName,
          trigger: this.fireChannelTrigger,
          sound: this.fireChannelSound,
          zone_macs: this.fireChannelZones,
        }
        this.$ws.setFireChannel(uuid, this.getDevice.mac, fireChannel, allChannel)
      },
      // 重启设备
      restartDevice: function () {
        const sendData = {
          uuid: this.$store.state.uuid,
          command: 'reboot_device',
          reboot_device: this.restartSameTypeDeviceSwitch ? 2 : 1, // 1 代表重启单个设备  2 代表重启同一类型设备
          device_model: this.getDevice.device_model,
          device_mac: this.restartSameTypeDeviceSwitch ? null : this.getDevice.mac,
        }
        this.$store.commit('WEBSOCKET_SEND', sendData)
        this.restartDeviceDialog = false
      },
      // 删除设备
      deleteDevice: function () {
        this.$ws.deleteDevice(this.deleteDeviceInfo.mac, this.deleteDeviceInfo.device_model)
      },
      // 重置设备数据
      resetDevice: function () {
        const sendData = {
          uuid: this.$store.state.uuid,
          command: 'reset_device_data',
          reset_device: this.resetSameTypeDeviceSwitch ? 2 : 1, // 1 代表重启单个设备  2 代表重启同一类型设备
          device_model: this.getDevice.device_model,
          device_mac: this.restartSameTypeDeviceSwitch ? null : this.getDevice.mac,
          file_type: 255,
        }
        this.$store.commit('WEBSOCKET_SEND', sendData)
        this.resetDeviceConfirmDialog = false
      },
      // 获取对应的分区
      getEventListByType: function (type) {
        const list = []
        this.eventList.forEach((e) => {
          if (e.device_model === type) {
            list.push(e)
          }
        })
        return list
      },
      // 选中的是解码分区
      checkIfDecodeZone: function () {
        if (this.selected.length === 0) {
          return false
        }
		return getDeviceModelMappingList(0).includes(this.selected[0].device_model)
      },
      // 选中的分区支持蓝牙播放
      checkIfSupportBluetooth () {
        if (this.selected.length === 0) {
          return false
        }
        return (this.getDevice.feature & deviceFeatureEnum.BlueTooth)
      },
      // 选中的分区支持SIP
      checkIfSupportSip () {
        if (this.selected.length === 0) {
          return false
        }
        return (this.getDevice.feature & deviceFeatureEnum.Sip)
      },
      // 选中的分区支持信息发布
      checkIfSupportInformationPublish () {
        if (this.selected.length === 0) {
          return false
        }
        return (this.getDevice.feature & deviceFeatureEnum.InformationPublish)
      },
      // 只有解码分区E 才支持对讲设置
      checkIfSupportIntercom () {
        if (this.selected.length === 0) {
          return false
        }
        // return this.deviceType === 'decode' && this.getDevice.device_model === deviceModelEnum.NetSpeakerE
        return (this.getDevice.feature & deviceFeatureEnum.Intercom)
      },
      // 暂时限制只有C4A0显示触发设置按钮
      checkIfShowTrigger () {
        return this.deviceType === 'decode' && this.isCloudIpSystem()
      },
      // 只有解码分区D、解码终端E 才支持触发设置
      checkIfSupportTrigger () {
        if (this.selected.length === 0) {
          return false
        }
        return this.getDevice.device_model === deviceModelEnum.NetSpeakerE || this.getDevice.device_model === deviceModelEnum.NetSpeakerD
      },
      // 只有音频采集器B才支持采播设置
      checkIfSupportAudioCollectorParmConfig () {
        if (this.selected.length === 0) {
          return false
        }
        return this.getDevice.device_model === deviceModelEnum.NetAudioCollectorB || this.getDevice.device_model === deviceModelEnum.NetAudioCollectorC || this.getDevice.device_model === deviceModelEnum.NetAudioCollectorF
      },
      // 消防管理预处理
      preEditFireCollector: function () {
        // 查询消防信息并存储
        this.$ws.getFireCollectorInfo()
        this.fireChannelSelected = []
        this.fireCollectorDialog = true
      },
      // 消防通道告警音效修改
      preUpdateFireChannelSound: function () {
        // 回显
        this.currentList = null
        this.expandCollector = false
        if (this.fireChannelSound === null || this.fireChannelSound === '') {
          this.currentSong = null
          this.currentSongPathName = null
        } else {
          this.currentSong = this.fireChannelSound.substring(this.fireChannelSound.lastIndexOf('/') + 1)
          this.currentSongPathName = this.fireChannelSound
        }
        this.currentSongPathName = this.fireChannelSound
        this.channelSongSelectDialog = true
      },
      // 消防通道分区修改
      preUpdateFireChannelZones: function () {
        // 使用穿梭框
        this.partitionSelectDialog = true
      },
      // 电源管理
      preEditPower() {
        if (this.sequencePowerInfo.length === 0) {
          this.$ws.getSequencePowerInfo()
        }
        // 每次打开对话框，复制原始数据
        this.updatedPowerChannels = JSON.parse(JSON.stringify(this.selectedSequencePowerChannels))
        this.updatedPowerChannels = this.updatedPowerChannels.map(obj => ({ ...obj, locked: true })) // 默认无法编辑
        this.updatedPowerControlMode = this.selectedSequencePowerMode
        this.sequencePowerInfoDialog = true
      },
      // 对讲设置
      preEditIntercom() {
        this.isSetIntercomToAllModelDevice = false
        this.$ws.setIntercomBasic(this.getDevice.mac, false, false, this.getDevice.device_model, '', '')
        this.queryIntercomLoading = true
      },
      // 采播设置
      preEditAudioCollector() {
        this.$ws.queryAudioCollectorParm(this.getDevice.mac)
        this.queryAudioCollectorLoading = true
        this.audioCollectorPartitionSelectTransfer = []

        //设置通道名称
        const audioCollector = this.audioCollectorInfo.filter(info => info.device_mac === this.getDevice.mac)
        if (Array.isArray(audioCollector) && audioCollector.length !== 0) {
          //console.log(JSON.stringify(audioCollector));
          for (let i = 0; i < 4; i++) {
            this.audioCollectorChannels[i].channel_name = audioCollector[0].channels[i].channel_name
            //console.log("Channel:"+i+",name="+this.audioCollectorChannels[i].channel_name)
          }
        }
      },
      // 混音设置
      preEditAudioMixer() {
        this.$ws.queryAudioMixerParm(this.getDevice.mac)
        this.queryAudioMixerLoading = true
        this.audioMixerPartitionSelectTransfer = []
      },
      //电话网关设置
      preEditPhoneGateway() {
        this.$ws.queryPhoneGatewayParm(this.getDevice.mac)
        this.queryPhoneGatewayLoading = true
        this.phoneGatewayPartitionSelectTransfer = []
      },
      //功放控制器设置
      preEditAmpControler() {
        this.$ws.queryAmpControlerParm(this.getDevice.mac)
        this.queryAmpControlerLoading = true
      },

      //噪声自适应器设置
      preEditNoiseDetector() {
        this.$ws.queryNoiseDetectorParm(this.getDevice.mac)
        this.queryNoiseDetectorLoading = true
      },

      // 任务管理
      preEditRemoteController() {
        // show dialog
        this.taskManagerDialog = true
      },
      preCreateTask() {
        this.isCreateTask = true
        this.taskName = null
        this.taskVolume = 50
        this.taskPartitionSelectTransfer = []
        this.groupSelectTransfer = []
        this.selectedPlayMode = 3 // 默认显示顺序播放
        if (this.playList.length > 0) {
          this.selectedPlayList = this.playList.length > 1 ? this.playList[1].list_id:this.playList[0].list_id// 默认显示第二个播放列表
        }
        this.singleSelectedSongIndex = null
        this.audioCollectorSearch = null
        this.selectedSongs = []
        this.audioCollectorActiveIds = []
        this.audioCollectorOpenIds = []
        this.taskSourceType = 0
        // show dialog
        this.taskDialog = true
      },
      preEditTask() {
        this.isCreateTask = false
        const task = this.taskSelected[0]
        this.taskName = task.name
        this.taskVolume = task.volume
        this.taskPartitionSelectTransfer = task.zone_macs
        this.groupSelectTransfer = task.group_ids
        this.selectedPlayMode = task.play_mode
        if (this.playList.length > 0) {
          this.selectedPlayList = this.playList.length > 1 ? this.playList[1].list_id:this.playList[0].list_id// 默认显示第二个播放列表
        }
        // 回显音源数据
        this.taskSourceType = task.source_type == null ? 0 : task.source_type
        if (this.taskSourceType === 0) {
          // 回显歌曲数据
          this.prepareSongDataSelected(task.song_pathnames);
        } else if (this.taskSourceType === 1) {
          // 回显音频采集数据
          this.audioCollectorSearch = null
          this.audioCollectorActiveIds = []
          this.audioCollectorOpenIds = []
          this.prepareAudioCollectorInfo(task.audio_collector)
        }
        this.taskDialog = true
      },
      preDeleteTask() {
        this.isDeleteTask = true
        this.deleteTaskDialog = true
      },
      // 选中任务
      rowTaskClick: function (item, row) {
        if (row.isSelected) {
          row.select(false)
        } else {
          row.select(true)
        }
      },
      // 蓝牙设置准备
      preSetBlueTooth: function () {
        // 先查询
        this.queryBluetoothFlag = true
        this.$ws.setBluetooth(0, this.getDevice.mac, this.getDevice.device_model, null, null, null)
        // this.setBluetoothDialog = true
      },
      setBluetooth () {
        if (!this.bluetoothForm) {
          return
        }
        const btPin = this.btEncryption === 1 ? this.btPin : '0000'
        this.$ws.setBluetooth(1, this.getDevice.mac, this.getDevice.device_model, this.btName, this.btEncryption, btPin)
      },
      // 监听音箱准备
      preMonitorDevice: function () {
        // 回显
        if (this.audioMonitorDeviceMac !== '') {
          this.monitorPartitionSelectTransfer = [this.audioMonitorDeviceMac]
        } else {
          this.monitorPartitionSelectTransfer = []
        }
        this.setMonitorDialog = true
      },
      // 选中的是消防管理
      checkIfFireCollector: function () {
        if (this.selected.length === 0) {
          return false
        }
        getDeviceModelMappingList(4).includes(this.selected[0].device_model)
      },
      // 音效查询预处理
      preEditSoundEffect: function () {
        this.querySoundEffectFlag = true
        this.$store.commit('updateQueryEffectResult', null)
        this.$store.commit('updateEffectGainInfo', null)
        const soundEffect = {
          gain1: 0,
          gain2: 0,
          gain3: 0,
          gain4: 0,
          gain5: 0,
          gain6: 0,
          gain7: 0,
          gain8: 0,
          gain9: 0,
          gain10: 0,
        }
        // gain0-10需要传0来查询
        this.$ws.deviceEqMode(0, this.getDevice.mac, this.getDevice.device_model, 1, null, soundEffect)
      },
      // 监听选中歌曲的更改
      songChanged: function (name, pathName) {
        if (this.currentSong == null || this.currentSong !== name) {
          this.currentSong = name
          this.currentSongPathName = pathName
        }
      },
      updateFireSong: function () {
        if (this.currentSongPathName === null) {
          return
        }
        this.fireChannelSound = this.currentSongPathName
        this.channelSongSelectDialog = false
        // 重置展开状态
        this.playList.forEach(list => {
          list.active = false
        })
      },
      // 设置监听音箱
      setMonitorDevice: function () {
        if (this.monitorPartitionSelectTransfer.length > 1) {
          this.errorMessages = this.$t('partitionSystem.messages.canOnlySetOneMonitorSpeaker')
          this.commonErrorSnackBar = true
          return
        }
        const mac = this.monitorPartitionSelectTransfer.length === 1 ? this.monitorPartitionSelectTransfer[0] : null
        if (mac === this.audioMonitorDeviceMac) {
          this.errorMessages = this.$t('partitionSystem.messages.monitorSpeakerSameAsCurrent')
          this.commonErrorSnackBar = true
          return
        }
        this.$ws.setAudioMonitorSpeaker(1, mac)
      },
      // 格式化消防通道管理的显示
      getChannelTriggerMode: function (trigger) {
        if (trigger === 0) {
          return this.$t('partitionSystem.triggerModes.levelTrigger')
        } else if (trigger === 1) {
          return this.$t('partitionSystem.triggerModes.shortCircuitTrigger')
        }
      },
      getChannelFireSound: function (sound) {
        return sound === '' ? this.$t('common.none') : sound.substring(sound.lastIndexOf('/') + 1)
      },
      getChannelFireZoneCount: function (zoneMacs) {
        return zoneMacs.length
      },
      getPresentEffect () {
        let soundEffectText = this.$t('partitionSystem.soundEffects.off')
        if (this.effectGainInfo == null) {
          return soundEffectText
        }
        const presentEffect = this.effectGainInfo.eqMode
        this.localizedSoundEffectList.forEach(effect => {
          if (effect.value === presentEffect) {
            soundEffectText = effect.text
          }
        })
        return soundEffectText
      },
      // 格式化滑动条刻度值
      formatSlideValue (value) {
        // int to string
        if (Number.isInteger(value)) {
          if (value <= 0) {
            return value + ''
          }
          return '+' + value
        }
        // string to int
        if (value >= 0) {
          return Number(value)
        }
        return 256 + Number(value)
      },
      getNetworkMode (networkMode) {
        if (networkMode === 1) {
          return 'UDP'
        }
        if (networkMode === 2) {
          return 'TCP'
        }
        // 默认返回UDP
        return 'UDP'
      },
      getMonitorNameByMac (mac) {
        if (this.monitors.length === 0 || mac === '') {
          return ''
        }
        let monitorName = ''
        this.monitors.forEach(monitor => {
          if (monitor.monitor_mac === mac) {
            monitorName = monitor.name
          }
        })
        return monitorName
      },
      getMoudle4GCSQLevel (csq) {
        switch(csq) {
          case 1:
            return this.$t('partitionSystem.signalQuality.veryPoor')
          case 2:
            return this.$t('partitionSystem.signalQuality.poor')
          case 3:
            return this.$t('partitionSystem.signalQuality.average')
          case 4:
            return this.$t('partitionSystem.signalQuality.good')
          case 5:
            return this.$t('partitionSystem.signalQuality.excellent')
        }
        // 默认返回未知
        return this.$t('partitionSystem.signalQuality.unknown')
      },
      // 监听选中歌曲的更改
      monitorChanged: function (name) {
        // console.log(':::::: ' + this.getMonitorNameByMac(this.getDevice.monitor_mac))
        if (this.getMonitorNameByMac(this.getDevice.monitor_mac) === '' ||
          this.getMonitorNameByMac(this.getDevice.monitor_mac) !== name) {
          this.currentMonitorName = name
          this.currentMonitorMac = this.getMonitorMacByName(name)
        }
      },
      getMonitorMacByName (name) {
        if (this.monitors.length === 0 || name === '') {
          return
        }
        let monitorMac = ''
        this.monitors.forEach(monitor => {
          if (monitor.name === name) {
            monitorMac = monitor.monitor_mac
          }
        })
        return monitorMac
      },
      getMonitorMacByIndex (index) {
        return this.monitors[index].monitor_mac
      },
      preMonitorBind () {
        this.monitorSelected = null
        this.monitorBindingDialog = true
      },
      // 监控绑定
      setMonitorInfo () {
        /*
        if (this.monitorSelected == null || this.monitorSelected > this.monitors.length) {
          this.errorMessages = this.$t('partitionSystem.messages.monitorDeviceNotSelected')
          this.commonErrorSnackBar = true
          return
        }
        */
        const sendData = {
          uuid: this.$store.state.uuid,
          command: 'set_device_info',
          device_model: this.getDevice.device_model,
          device_mac: this.getDevice.mac,
          device_name: this.getDevice.name,
          monitor_mac: (this.monitorSelected == null || this.monitorSelected > this.monitors.length) ? "":this.getMonitorMacByIndex(this.monitorSelected),
          set_type: 2,
        }
        this.$store.commit('WEBSOCKET_SEND', sendData)
      },
      preDeleteDevice(device) {
        if (device == null) {
          return
        }
        // 非离线设备不弹出对话框，直接弹出错误提示
        if (device.source !== -1) {
          this.errorMessages = this.$t('partitionSystem.messages.canOnlyDeleteOfflineDevice')
          this.commonErrorSnackBar = true
          return
        }
        this.deleteDeviceInfo = device
        this.deleteDeviceDialog = true
      },
      // 重置选中的分区为null
      resetSelectedZone() {
        this.selected = []
      },
      getAudioCollectorClass(id) {
        const mac = id.includes(id) ? id.split('|')[0] : id
        const zone = this.$ws.getZoneByZoneMac(mac)
        // todo 字体过大，待优化
        return zone.source === -1 ? 'text-lg-h3 darkOfflineZone' : 'text-lg-h3'
      },
      // 回显音频采集器数据
      prepareAudioCollectorInfo(audioCollector) {
        // 展开树
        if (audioCollector == null || audioCollector.device_mac == null || audioCollector.channel == null) {
          return
        }
        this.audioCollectorOpenIds = [audioCollector.device_mac]
        // 选中已选择的项目
        this.audioCollectorActiveIds = [audioCollector.device_mac + '|' + audioCollector.channel]
      },
      // 音源列表不存在歌曲使用红色字体表示
      songStyleInPlaylist(alive) {
        if (alive) {
          return {}
        }
        return {
          color: 'red',
        }
      },
      // 获取音源列表歌曲提示（歌名+时长）, html格式，需使用v-html消费
      getSongNameAndDurationForSongTooltip(song) {
        const songDurationNumber = song.duration != null ? song.duration : 0
        let duration = new Date(songDurationNumber * 1000).toISOString();
        if (duration.substr(11, 2) !== '00') {
          // 超过1h的歌曲，显示小时
          duration = duration.substr(11, 8)
        } else {
          // 不超过1h的歌曲，不显示小时
          duration = duration.substr(14, 5)
        }
        return song.song_name + '</br>' + this.$t('partitionSystem.songInfo.duration') + ': ' + duration
      },
      addAllLeftSongs() {
        this.getSelectedPlayListSongs.forEach(song => this.selectedSongs.push(song))
      },
      removeAllRightSongs() {
        this.selectedSongs = []
      },
      dbClickLeftSongList(song) {
        this.selectedSongs.push(song)
      },
      dbClickRightSongList(index) {
        this.selectedSongs.splice(index, 1)
        this.singleSelectedSongIndex = null
      },
      songMoveUp() {
        if (this.singleSelectedSongIndex == null || this.singleSelectedSongIndex === 0) {
          return
        }
        this.moveItem(this.selectedSongs, this.singleSelectedSongIndex, this.singleSelectedSongIndex - 1)
        this.singleSelectedSongIndex -= 1
      },
      songMoveDown() {
        if (this.singleSelectedSongIndex == null || this.singleSelectedSongIndex === this.selectedSongs.length - 1) {
          return
        }
        this.moveItem(this.selectedSongs, this.singleSelectedSongIndex, this.singleSelectedSongIndex + 1)
        this.singleSelectedSongIndex += 1
      },
      removeSelectedSongs() {
        // 当选中单个时，删除单个，未选中时删除全部
        if (this.singleSelectedSongIndex != null) {
          this.selectedSongs.splice(this.singleSelectedSongIndex, 1)
        } else {
          this.selectedSongs = []
        }
        this.singleSelectedSongIndex = null
      },
      changeSelected(index) {
        this.singleSelectedSongIndex = (this.singleSelectedSongIndex === index) ? null : index
        this.$emit('change', this.singleSelectedSongIndex);
      },
      // 获取预估播放时长
      getTitleOfTaskSelectedSongs() {
        const chosenSongsTips = this.$t('partitionSystem.songInfo.selectedSongs') + ':  ' + this.selectedSongs.length
        if (this.selectedPlayMode !== 3 || this.selectedSongs.length === 0) {
          return chosenSongsTips
        }
        return chosenSongsTips + '&nbsp;&nbsp;&nbsp;&nbsp;' + this.$t('partitionSystem.songInfo.estimatedPlayDuration') + '：' + this.getAllDurationOfSelectSongs()
      },
      // 获取顺序播放下所有已选择歌曲的总时长
      getAllDurationOfSelectSongs() {
        let durationNumber = 0
        if (this.selectedSongs.length === 0) {
          return durationNumber
        }
        this.selectedSongs.forEach(song => {
          // 每首歌曲加1s
          durationNumber += (song.duration + 1)
        })
        let duration = new Date(durationNumber * 1000).toISOString();
        const hour = duration.substr(11, 2)
        const minute = duration.substr(14, 2)
        const second = duration.substr(17, 2)
        const mandatoryTime = hour + this.$t('timeTips.hours') + minute + this.$t('timeTips.minutes') + second + this.$t('timeTips.seconds')
        if (duration.substr(8, 2) !== '01') {
          // 超过1天的歌曲，显示天数 （注：超过30天的播放时长不支持显示）
          const day = parseInt(duration.substr(8, 2)) - 1
          const dayString = day < 10 ? ('0' + day) : day
          return dayString + this.$t('timeTips.days') + mandatoryTime
        }
        return mandatoryTime
      },
      // 获取所有已选择歌曲的绝对路径
      getAllPathOfSelectSongs: function () {
        return this.selectedSongs.length === 0 ? [] : this.selectedSongs.map(m => m.song_path_name)
      },
      isEmpty (str) {
        return (!str || str.length === 0)
      },
      /**
       * 创建/编辑任务
       * 1. 前置检查
       * 1.1 任务名称
       * 1.2 任务是否有效 （删除 -- 任务有效只有在任务编辑中显示，因为同时只能有一个任务有效）
       * 1.3 日期选择
       * 1.4 时间选择
       * 1.5 分区选择/分组选择
       * 1.6 歌曲选择（播放模式/播放歌曲）
       */
      createOrEditTask: function (isCreateTask) {
        // 传入的参数为指示，当前请求为创建任务，如果为false，则为编辑任务
        if (this.isEmpty(this.taskName)) {
          this.errorMessages = this.$t('partitionSystem.messages.taskNameEmptyOrInvalid')
          this.commonErrorSnackBar = true
          return
        }

        if (this.taskPartitionSelectTransfer.length === 0 && this.groupSelectTransfer.length === 0 ) {
          this.errorMessages = this.$t('partitionSystem.messages.taskNeedsPartitionOrGroup')
          this.commonErrorSnackBar = true
          return
        }
        if (this.selectedPlayMode == null) {
          this.errorMessages = this.$t('partitionSystem.messages.taskNeedsPlayMode')
          this.commonErrorSnackBar = true
          return
        }

        if (this.taskSourceType === 0 && this.selectedSongs.length === 0) {
          this.errorMessages = this.$t('partitionSystem.messages.noTaskSongsSelected')
          this.commonErrorSnackBar = true
          return
        }
        if (this.taskSourceType === 1 && this.audioCollectorActiveIds.length === 0) {
          this.errorMessages = this.$t('partitionSystem.messages.noAudioCollectorSelected')
          this.commonErrorSnackBar = true
          return
        }

        let id
        const taskList = this.remoteControllerTasks
        if (isCreateTask) {
          id = taskList.length > 0 ? taskList[taskList.length - 1].task_id + 1 : 0
        } else {
          // 修改获取任务id方式为从点击编辑按钮的任务获取，或者从选中行获取
          id = this.editTaskActionInTable ? this.editTaskActionInTable.task_id : this.taskSelected[0].task_id
        }
        const task = {
          task_id: id,
          name: this.taskName,
          volume: this.taskVolume,
          play_mode: this.selectedPlayMode,
          source_type: this.taskSourceType,
          zone_macs: this.taskPartitionSelectTransfer,
          group_ids: this.groupSelectTransfer,
        }
        if (this.taskSourceType === 0) {
          task.song_pathnames = this.getAllPathOfSelectSongs()
        } else if (this.taskSourceType === 1) {
          task.audio_collector = {
            device_mac: this.audioCollectorActiveIds[0].split('|')[0],
            channel: Number(this.audioCollectorActiveIds[0].split('|')[1])
          }
        }

        if (isCreateTask) {
          this.$ws.setRemoteControllerTask(1, this.getDevice.mac, task)
        } else {
          this.$ws.setRemoteControllerTask(2, this.getDevice.mac, task)
        }
      },
      preEditTaskActionInTable(item) {
        if (item == null) {
          return
        }
        this.editTaskActionInTable = item
        this.preEditTaskCommon(item)
      },
      preDeleteTaskActionInTable(item) {
        if (item == null) {
          return
        }
        this.isDeleteTask = true
        this.deletedTaskActionInTable = item
        this.deleteTaskActionInTableDialog = true
      },
      deleteTaskActionInTable() {
        const taskObject = {
          task_id: this.deletedTaskActionInTable.task_id
        }
        this.deleteTaskIntegration(taskObject)
      },
      deleteTask: function () {
        if (this.taskSelected.length === 0) {
          this.errorMessages = this.$t('partitionSystem.messages.selectTaskForDelete')
          this.commonErrorSnackBar = true
          return
        }
        const taskObject = {
          task_id: this.taskSelected[0].task_id
        }
        this.deleteTaskIntegration(taskObject)
      },
      deleteTaskIntegration(taskObject) {
        this.$ws.setRemoteControllerTask(3, this.getDevice.mac, taskObject)
      },
      // 编辑任务
      preEditTaskCommon: function (item) {
        this.isCreateTask = false
        const taskSelectedElement = item
        this.taskName = taskSelectedElement.name
        this.taskVolume = taskSelectedElement.volume

        this.taskPartitionSelectTransfer = taskSelectedElement.zone_macs
        this.groupSelectTransfer = taskSelectedElement.group_ids
        this.selectedPlayMode = taskSelectedElement.play_mode
        if (this.playList.length > 0) {
          this.selectedPlayList = this.playList.length > 1 ? this.playList[1].list_id:this.playList[0].list_id// 默认显示第二个播放列表
        }

        this.audioCollectorSearch = null
        this.audioCollectorActiveIds = []
        this.audioCollectorOpenIds = []
        this.singleSelectedSongIndex = null

        // 回显音源数据
        this.taskSourceType = taskSelectedElement.source_type == null ? 0 : taskSelectedElement.source_type
        if (this.taskSourceType === 0) {
          // 回显歌曲数据
          this.prepareSongDataSelected(taskSelectedElement.song_pathnames);
        } else if (this.taskSourceType === 1) {
          // 回显音频采集数据
          this.audioCollectorSearch = null
          this.audioCollectorActiveIds = []
          this.audioCollectorOpenIds = []
          this.prepareAudioCollectorInfo(taskSelectedElement.audio_collector)
        }
        this.taskDialog = true
      },
      /**
       * 用于编辑任务前组装已选中的数据
       * 20210904 从树形列表修改为拖拽列表
       */
      prepareSongDataSelected: function (songs) {
        if (songs == null || songs.length === 0) {
          this.selectedSongs = []
          return
        }
        // 依据song_path_name去通过playlist获取歌曲的信息
        const list = []
        out:for (let i = 0; i < songs.length; i++) {
          for (let j = 0; j < this.playList.length; j++) {
            for (let k = 0; k < this.playList[j].songs.length; k++) {
              if (this.playList[j].songs[k].song_path_name === songs[i]) {
                list.push(this.playList[j].songs[k])
                continue out
              }
            }
          }
        }
        this.selectedSongs = list
      },

      // ##########################  按键管理
      preEditRemoteControllerKey() {
        // 回显数据
        this.remoteControllerKeys.forEach(key => {
          this.keys['key' + key.key_id] = this.getKeyNameByEventId(key.event)
        })
        this.keyManagerDialog = true
      },
      // 从key名获取eventId
      getEventIdByKeyName(keyName) {
        const baseKeyIndex = baseItems.findIndex(item => item === keyName)
        if (baseKeyIndex !== -1) {
          return baseKeyIndex
        }
        const taskId = this.remoteControllerTaskNames.findIndex(name => name === keyName);
        return 21 + taskId
      },
      // 从eventId获取keyName
      getKeyNameByEventId(eventId) {
        const numberEventId = Number(eventId)
        if (numberEventId < 7) {
          return baseItems[numberEventId]
        }
        else if (numberEventId < 21) { // 7-20预留
          return baseItems[0]
        }
        const remoteTaskNameIndex = numberEventId - 21
        return this.remoteControllerTaskNames[remoteTaskNameIndex]
      },
      // 保存按键信息
      saveKeySettings() {
        // 暂时限定key_id为1-12
        const keyInfo = []
        for (let i = 1; i < 13; i++) {
          keyInfo.push({
            key_id: i,
            event: this.getEventIdByKeyName(this.keys['key' + i])
          })
        }
        this.$ws.setRemoteControllerKey(this.getDevice.mac, keyInfo)
      },
      // 保存对讲信息
      saveIntercomSettings() {
        this.$ws.setIntercomBasic(this.getDevice.mac, true, this.isSetIntercomToAllModelDevice,
          this.getDevice.device_model, this.key1MacForIntercom, this.key2MacForIntercom, this.intercomMicVolumeLevel, this.intercomFarOutPutVolumeLevel)
      },
      // 保存触发设置
      saveTriggerSettings() {
        const isTrigger = this.editTriggerSwitch !== 0
        const isShortCircuit = this.editTriggerMode !== 0
        this.$ws.setTrigger(true, this.getDevice.mac, this.getDevice.device_model, this.isSetTriggerToAllModelDevice,
          isTrigger, isShortCircuit, this.editTriggerSongPathName, this.editTriggerVolume)
      },
      // 保存采播设置
      saveAudioCollectorSettings() {
        let channels = [];
        for (let i = 0; i < 4; i++) {
          const channelInfo = this.audioCollectorChannels[i];
          channels.push({
            channel_id: channelInfo.channel_id,
            channel_name: channelInfo.channel_name,
          });
          //console.log("channel_id:"+channelInfo.channel_id+",channel_name:"+channelInfo.channel_name)
        }
        this.$ws.setAudioCollectorParm(true, this.getDevice.mac, channels, this.editAudioCollectorPriority, this.editAudioCollectorTriggerSwitch , this.editAudioCollectorTriggerChannelId, this.editAudioCollectorTriggerZoneVolume,
          this.audioCollectorPartitionSelectTransfer)
      },
      // 保存混音设置
      saveAudioMixerSettings() {
        this.$ws.setAudioMixerParm(true, this.getDevice.mac, this.editAudioMixerMasterSwitch , this.editAudioMixerPriority, this.editAudioMixerTriggerType,
          this.editAudioMixerVolumeFadeLevel, this.editAudioMixerZoneVolume, this.audioMixerPartitionSelectTransfer)
      },
      //保存电话网关设置
      savePhoneGatewaySettings() {
        this.$ws.setPhoneGatewayParm(true, this.getDevice.mac, this.editPhoneGatewayMasterSwitch, this.editPhoneGatewayZoneVolume,
          this.editPhoneGatewayTelWhitelist, this.phoneGatewayPartitionSelectTransfer)
      },

      // 保存噪声自适应终端设置
      saveNoiseDetectorSettings() {
        // 验证音量值
        const validationResult = this.validateVolumeSettings()
        if (!validationResult.isValid) {
          this.errorMessages = validationResult.message
          this.commonErrorSnackBar = true
          return
        }

        this.$ws.setNoiseDetectorParm(true, this.getDevice.mac, this.editNoiseDetectorSwitch,
          this.editNoiseDetectorVolumeArray, this.noiseDetectorPartitionSelectTransfer)
      },
      minusTriggerVolume() {
        this.editTriggerVolume = (this.editTriggerVolume - 1) >= 0 ? (this.editTriggerVolume - 1) : 0
      },
      plusTriggerVolume() {
        this.editTriggerVolume = (this.editTriggerVolume + 1) <= 100 ? (this.editTriggerVolume + 1) : 100
      },
      // 监听选中歌曲的更改
      editTriggerSongChanged: function (name, pathName) {
        if (this.editTriggerCurrentSong == null || this.editTriggerCurrentSong !== name) {
          this.editTriggerCurrentSong = name
          this.editTriggerCurrentSongPathName = pathName
        }
      },
      updateEditTriggerSong: function () {
        if (this.editTriggerCurrentSongPathName === null) {
          return
        }
        this.editTriggerSongPathName = this.editTriggerCurrentSongPathName
        this.editTriggerSongSelectDialog = false
        // 重置展开状态
        this.playList.forEach(list => {
          list.active = false
        })
      },

      // 状态类名映射
      statusClass(status) {
          if (status === 0) return 'status-idle';
          if (status === 1) return 'status-normal';
          if (status === 2) return 'status-fault';
          return '';
      },
      
      // 状态文本映射
      statusText(status) {
          if (status === 0) return this.$t('partitionSystem.statusLabels.idle');
          if (status === 1) return this.$t('partitionSystem.statusLabels.normal');
          if (status === 2) return this.$t('partitionSystem.statusLabels.fault');
          return this.$t('partitionSystem.statusLabels.unknown');
      },

      // 噪声自适应终端相关方法
      // 获取通道颜色
      getChannelColor(index) {
        return this.isChannelValid(index) ? 'success' : 'grey lighten-2'
      },

      // 获取通道样式类
      getChannelClass(index) {
        return this.isChannelValid(index) ? 'white--text' : 'grey--text'
      },

      // 判断通道是否有效
      isChannelValid(index) {
        if (!this.editNoiseDetectorChannelArray || this.editNoiseDetectorChannelArray.length <= index) {
          return false
        }
        const value = this.editNoiseDetectorChannelArray[index]
        return value != null && value > 0 && value >= 30 && value <= 130
      },

      // 获取通道噪声值
      getChannelNoiseValue(index) {
        if (!this.editNoiseDetectorChannelArray || this.editNoiseDetectorChannelArray.length <= index) {
          return 0
        }
        return this.editNoiseDetectorChannelArray[index] || 0
      },

      // 获取有效通道数量
      getValidChannelsCount() {
        if (!this.editNoiseDetectorChannelArray) return 0
        return this.editNoiseDetectorChannelArray.filter((value, index) => this.isChannelValid(index)).length
      },

      // 计算平均噪声值
      getAverageNoiseLevel() {
        if (!this.editNoiseDetectorChannelArray) return 0
        const validValues = this.editNoiseDetectorChannelArray.filter((value, index) => this.isChannelValid(index))
        if (validValues.length === 0) return 0
        const sum = validValues.reduce((acc, val) => acc + val, 0)
        return Math.round(sum / validValues.length)
      },

      // 获取固定的噪声值
      getFixedNoiseValue(index) {
        // 8段固定噪声值：30dB, 40dB, 50dB, 60dB, 70dB, 80dB, 90dB, 100dB
        return 30 + (index * 10)
      },

      // 验证音量设置
      validateVolumeSettings() {
        if (!this.editNoiseDetectorVolumeArray || this.editNoiseDetectorVolumeArray.length !== 8) {
          return {
            isValid: false,
            message: this.$t('partitionSystem.validation.invalidVolumeArrayLength')
          }
        }

        // 检查每段音量值是否在有效范围内
        for (let i = 0; i < 8; i++) {
          const volume = this.editNoiseDetectorVolumeArray[i]
          if (volume == null || volume < 1 || volume > 100) {
            return {
              isValid: false,
              message: this.$t('partitionSystem.validation.invalidVolumeRange', {
                segment: i + 1,
                noiseLevel: this.getFixedNoiseValue(i)
              })
            }
          }
        }

        // 检查音量值是否递增（下一段不能比上一段低）
        for (let i = 1; i < 8; i++) {
          const currentVolume = this.editNoiseDetectorVolumeArray[i]
          const previousVolume = this.editNoiseDetectorVolumeArray[i - 1]
          if (currentVolume < previousVolume) {
            return {
              isValid: false,
              message: this.$t('partitionSystem.validation.volumeNotIncreasing', {
                currentSegment: i + 1,
                currentNoise: this.getFixedNoiseValue(i),
                currentVolume: currentVolume,
                previousSegment: i,
                previousNoise: this.getFixedNoiseValue(i - 1),
                previousVolume: previousVolume
              })
            }
          }
        }

        return { isValid: true }
      },

      // 初始化噪声/音量数组
      initializeNoiseDetectorVolumeArray() {
        if (!this.editNoiseDetectorVolumeArray || this.editNoiseDetectorVolumeArray.length === 0) {
          // 初始化8个音量值（噪声值固定为30,40,50,60,70,80,90,100dB）
          this.editNoiseDetectorVolumeArray = []
          for (let i = 0; i < 8; i++) {
            // 默认音量值：10, 20, 30, 40, 50, 60, 70, 80
            this.editNoiseDetectorVolumeArray.push((i + 1) * 10)
          }
        }
      },
    },
  }

</script>

<style scoped>
.segment-control {
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin: 4px;
  background-color: #fafafa;
}

.segment-control .text-caption {
  color: #666;
}

/* 噪声检测器通道状态样式 */
.status-idle {
  background-color: #9e9e9e !important;
  color: white !important;
}

.status-normal {
  background-color: #4caf50 !important;
  color: white !important;
}

.status-fault {
  background-color: #f44336 !important;
  color: white !important;
}

/* 通道卡片动画效果 */
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}
</style>
