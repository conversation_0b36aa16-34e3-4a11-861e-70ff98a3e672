module.exports = {
  // 关闭源代码在chrome中source显示
  productionSourceMap: false,
  devServer: {
    disableHostCheck: true,
  },

  transpileDependencies: ['vuetify'],

  pluginOptions: {
    i18n: {
      // 读取.env配置作为默认语言显示
      locale: process.env.VUE_APP_I18N_LOCALE || 'zh',
      fallbackLocale: process.env.VUE_APP_I18N_FALLBACK_LOCALE || 'zh',
      localeDir: 'locales',
      enableInSFC: false,
    },
  },

  chainWebpack: config => {
    config.plugin('copy').tap(options => {
      // 1. 控制config.json文件的外置与否
      if (process.env.VUE_APP_IS_OUTPUT_CONFIG_JSON !== 'true') {
        options[0][0].ignore.push('config.json');
      }
      return options
    });
  },

  configureWebpack: {
    output: {
      filename: '[name].[hash].bundle.js',
    },
  },

  /* 定制化index.html文件 */
  pages: {
    index: {
      // entry for the page
      entry: 'src/main.js',
      // template title tag needs to be <title><%= htmlWebpackPlugin.options.title %></title>
      title: process.env.VUE_APP_PAGE_TITLE || 'IP广播系统',
    },
  }
}
