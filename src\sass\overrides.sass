// =========================================================
// * Vuetify Material Dashboard - v2.1.0
// =========================================================
//
// * Product Page: https://www.creative-tim.com/product/vuetify-material-dashboard
// * Copyright 2019 Creative Tim (https://www.creative-tim.com)
//
// * Coded by Creative Tim
//
// =========================================================
//
// * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

// Creative Tim refine style code
@import vuetify-material/sidebar
@import vuetify-material/appbar
@import vuetify-material/buttons
@import vuetify-material/pagination
@import vuetify-material/footer
@import vuetify-material/view
@import vuetify-material/settings
@import vuetify-material/card
@import vuetify-material/table
@import vuetify-material/tab
@import vuetify-material/notification
@import vuetify-material/modal
@import vuetify-material/map
@import vuetify-material/chip

.v-btn.v-size--default,
.v-btn.v-size--large
  &:not(.v-btn--icon):not(.v-btn--fab)
    padding: 0 30px !important

.theme--light.v-list-item .v-list-item__action-text,
.theme--light.v-list-item .v-list-item__subtitle
  color: #999

.theme--light.v-text-field>.v-input__control>.v-input__slot:before
  border-color: #d2d2d2

.v-label.v-label,
.v-alert.v-alert
  font-size: $font-size-root

.theme--light .v-content
  background-color: #eee
