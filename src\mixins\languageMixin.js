export default {
  data() {
    return {
      availableLanguages: [
        { code: 'zh', name: '简体中文' },
        { code: 'en', name: 'English' },
        { code: 'zh-TW', name: '繁體中文' }
      ]
    }
  },
  methods: {
    changeLanguage(langCode) {
      this.$i18n.locale = langCode
      // 保存语言选择到本地存储
      localStorage.setItem('selectedLanguage', langCode)
      // 更新 Vuetify 的语言设置
      if (langCode === 'zh-TW') {
        this.$vuetify.lang.current = 'zhHant'
      } else if (langCode === 'en') {
        this.$vuetify.lang.current = 'en'
      } else {
        this.$vuetify.lang.current = 'zh'
      }

      // 更新 Element UI 的语言设置
      if (this.$setElementLocale) {
        this.$setElementLocale(langCode)
      }
    },
    getCurrentLanguage() {
      return this.$i18n.locale
    },
    initLanguage() {
      // 从本地存储恢复语言设置
      const savedLanguage = localStorage.getItem('selectedLanguage')
      if (savedLanguage && ['zh', 'en', 'zh-TW'].includes(savedLanguage)) {
        this.changeLanguage(savedLanguage)
      }
    }
  },
  mounted() {
    this.initLanguage()
  }
}
