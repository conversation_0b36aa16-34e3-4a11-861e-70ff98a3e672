/**
 * 工具类
 */
import CryptoJS from 'crypto-js'
import * as ws from './websocket'
import iconv from 'iconv-lite';
import { Buffer } from 'buffer';
import i18n from '@/i18n';

export function encrypt (word, keyStr) {
  keyStr = keyStr || 'abcdefgabcdefg12'
  var key = CryptoJS.enc.Utf8.parse(keyStr)// Latin1 w8m31+Yy/Nw6thPsMpO5fg==
  var srcs = CryptoJS.enc.Utf8.parse(word)
  var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 })
  return encrypted.toString()
}

export function decrypt (word, keyStr) {
  keyStr = keyStr || 'abcdefgabcdefg12'
  var key = CryptoJS.enc.Utf8.parse(keyStr)// Latin1 w8m31+Yy/Nw6thPsMpO5fg==
  var decrypt = CryptoJS.AES.decrypt(word, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 })
  return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}

// 获取字符串的字节长度(utf-8)
export function getStrLen (str) {
  if (str == null) return 0
  if (typeof str !== 'string') {
    return 0
  }
  var total = 0; var charCode; var i; var len
  for (i = 0, len = str.length; i < len; i++) {
    charCode = str.charCodeAt(i)
    if (charCode <= 0x007f) {
      total += 1// 字符代码在000000 – 00007F之间的，用一个字节编码
    } else if (charCode <= 0x07ff) {
      total += 2// 000080 – 0007FF之间的字符用两个字节
    } else if (charCode <= 0xffff) {
      total += 3// 000800 – 00D7FF 和 00E000 – 00FFFF之间的用三个字节，注: Unicode在范围 D800-DFFF 中不存在任何字符
    } else {
      total += 4// 010000 – 10FFFF之间的用4个字节
    }
  }
  return total
}

// 获取字符串的字节长度(gbk)
export function getStringLenForGbk (str) {
  var i, len, code
  if (str == null || str === '') return 0
  len = str.length
  for (i = 0; i < str.length; i++) {
    code = str.charCodeAt(i)
    if (code > 255) {
      len += 1
    }
  }
  return len
}

// 获取字符串的charArray
export function stringToCharArray (str) {
  const arr = []
  for (let index = 0; index < str.length; index++) {
    arr[index] = Number(str.charAt(index))
  }
  return arr
}

// 获取int数组的和
export function getSumOfIntArray (array) {
  let sum = 0
  array.forEach(value => {
    sum += value
  })
  return sum
}

// 获取播放模式
export function getPlayMode(status) {
  switch (status) {
    case 1:
      return i18n.t('utils.playModes.single')
    case 2:
      return i18n.t('utils.playModes.singleLoop')
    case 3:
      return i18n.t('utils.playModes.sequential')
    case 4:
      return i18n.t('utils.playModes.loop')
    case 5:
      return i18n.t('utils.playModes.random')
    default:
      return i18n.t('utils.playModes.single')
  }
}

export function getAudioCollectorDescription(prefix, mac, channel) {
  if (prefix == null || mac == null || channel == null) {
    return ''
  }
  const zone = ws.getZoneByZoneMac(mac);
  const channelDescription = i18n.t('utils.channelFormat', { channel })
  const tip = zone == null ? mac + channelDescription : zone.name + channelDescription
  return prefix + tip
}

// 获取定时点歌曲
export function getTimerSongs(songs) {
  let showSong = ''
  if (songs.length === 1) {
    showSong = songs[0].substring(songs[0].lastIndexOf('/') + 1, songs[0].lastIndexOf('.'))
  } else if (songs.length > 1) {
    showSong = songs[0].substring(songs[0].lastIndexOf('/') + 1, songs[0].lastIndexOf('.')) +
      i18n.t('utils.andMoreSongs', { count: songs.length })
  }
  return showSong
}

// 获取歌曲的实际名称
export function getSongNames(songs) {
  let showSong = ''
  if (songs.length === 1) {
    showSong = songs[0].substring(songs[0].lastIndexOf('/') + 1, songs[0].lastIndexOf('.'))
  } else if (songs.length > 1) {
    // 先组成列表，然后使用join
    const songList = []
    songs.forEach((song) => {
      songList.push(song.substring(song.lastIndexOf('/') + 1, song.lastIndexOf('.')))
    })
    showSong = songList.join(' , ')
  }
  return showSong
}


// 获取定时点电源时序器
export function getTimerSequencePowerNames(prefix, powers) {
  let showPower = ''
  if (powers.length === 1) {
    const zone = ws.getZoneByZoneMac(powers[0].device_mac);
    let channelsDescription = ''
    let channels = []
    powers[0].channels.forEach((ch) => {
      channels.push(ch)
    })
    //20230814 暂不显示具体通道
    //channels_separate = channels.join(' , ')
    //channelsDescription = '（' +channels_separate + '）'
    showPower = zone == null ? powers[0].device_mac + channelsDescription : zone.name + channelsDescription

  } else if (powers.length > 1) {

    const zone = ws.getZoneByZoneMac(powers[0].device_mac);

    showPower = (zone == null ? powers[0].device_mac : zone.name) +
      i18n.t('utils.andMoreDevices', { count: powers.length })
  }

  return prefix + showPower
}

// 获取电源时序器的实际名称
export function getSequencePowerNames(prefix, powers) {
  let showPower = ''
  if (powers.length === 1) {
    const zone = ws.getZoneByZoneMac(powers[0].device_mac);
    let channelsDescription = ''
    let channels = []
    let channels_separate = ''
    powers[0].channels.forEach((ch) => {
      channels.push(ch)
    })
    channels_separate = channels.join(' , ')
    channelsDescription = i18n.t('utils.channelsFormat', { channels: channels_separate })
    showPower = zone == null ? powers[0].device_mac + channelsDescription : zone.name + channelsDescription
  } else if (powers.length > 1) {
    // 先组成列表，然后使用join
    let powerList = []
    powers.forEach((power) => {
      const zone = ws.getZoneByZoneMac(power.device_mac);
      let channels_separate = ''
      let channelsDescription
      let channels = []
      power.channels.forEach((ch) => {
        channels.push(ch)
      })
      channels_separate = channels.join(' , ')
      channelsDescription = i18n.t('utils.channelsFormat', { channels: channels_separate })

      let powerDescription = zone == null ? power.device_mac + channelsDescription : zone.name + channelsDescription
      powerList.push(powerDescription)
    })

    showPower = powerList.join(' , ')
  }
  return prefix + showPower
}

// 定时点分区显示优化
export function getTimerSections(sections) {
  const zoneNames = ws.getZoneNamesByZoneMacs(sections)
  let showSection
  if (sections.length === 1) {
    showSection = zoneNames[0]
  } else if (sections.length > 1) {
    showSection = zoneNames[0] + i18n.t('utils.andMoreZones', { count: sections.length })
  }
  return showSection
}
// 获取分区的实际名称
export function getTimerSectionsNames(sections) {
  return ws.getZoneNamesByZoneMacs(sections).join(' , ')
}
// 定时点分组显示优化
export function getTimerGroups(groups) {
  const groupsNames = ws.getGroupNameByGroupId(groups)
  let showGroup
  if (groups.length === 1) {
    showGroup = groupsNames[0]
  } else if (groups.length > 1) {
    showGroup = groupsNames[0] + i18n.t('utils.andMoreGroups', { count: groups.length })
  }
  return showGroup
}
// 获取分组的实际名称
export function getTimerGroupsName(groups) {
  return ws.getGroupNameByGroupId(groups).join(' , ')
}

/**
 * Add two string time values (HH:mm:ss) with javascript
 *
 * Usage:
 *  > addTimes('04:20:10', '21:15:10');
 *  > "25:35:20"
 *  > addTimes('04:35:10', '21:35:10');
 *  > "26:10:20"
 *  > addTimes('30:59', '17:10');
 *  > "48:09:00"
 *  > addTimes('19:30:00', '00:30:00');
 *  > "20:00:00"
 *
 * @param {String} startTime  String time format
 * @param {String} endTime  String time format
 * @returns {String}
 */
export function addTimes(startTime, endTime) {
  let i;
  const times = [0, 0, 0];
  const max = times.length;

  const a = (startTime || '').split(':');
  const b = (endTime || '').split(':');

  // normalize time values
  for (i = 0; i < max; i++) {
    a[i] = isNaN(parseInt(a[i])) ? 0 : parseInt(a[i])
    b[i] = isNaN(parseInt(b[i])) ? 0 : parseInt(b[i])
  }

  // store time values
  for (i = 0; i < max; i++) {
    times[i] = a[i] + b[i]
  }

  let hours = times[0];
  let minutes = times[1];
  let seconds = times[2];

  if (seconds >= 60) {
    const m = (seconds / 60) << 0;
    minutes += m
    seconds -= 60 * m
  }

  if (minutes >= 60) {
    const h = (minutes / 60) << 0;
    hours += h
    minutes -= 60 * h
  }

  return ('0' + hours).slice(-2) + ':' + ('0' + minutes).slice(-2) + ':' + ('0' + seconds).slice(-2)
}

/**
 * 格式化文件大小byte至可读格式
 * formatBytes(1024);       // 1 KB
 * formatBytes('1024');     // 1 KB
 * formatBytes(1234);       // 1.21 KB
 * formatBytes(1234, 3);    // 1.205 KB
 * @param bytes
 * @param decimals
 * @returns {string}
 */
export function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}



// UTF-8 转 GB2312
export function utf8ToGb2312(utf8Str) {
  const gb2312Buffer = iconv.encode(utf8Str, 'gb2312');
  return gb2312Buffer.toString('binary');
}

// GB2312 转 UTF-8
export function gb2312ToUtf8(gb2312Str) {
  const gb2312Buffer = Buffer.from(gb2312Str, 'binary');
  return iconv.decode(gb2312Buffer, 'gb2312');
}
