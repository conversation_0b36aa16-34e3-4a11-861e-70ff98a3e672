############## BUG FIXED TODO
1. 所有对话框的max-width修改为width: "unset"，使宽度自适应
2. 对话框的标题栏样式优化
3. 缩放问题




############## 优化：
1. 文本框的rule应用，参考https://v2.vuetifyjs.com/zh-Hans/components/textarea/
    :rules="[rules.password, rules.length(6)]"
            filled
            color="deep-purple"
            counter="6"
2. 控制页面的各分区音量显示由v-text-field修改为v-chip
3. 移动端无法显示表格选中css




############## 完成
1. setTimeout全部更新为watch --20211226 不能全部修改为watch，仍需个例查看
2. 检查穿梭框及其他，getMacs方法应该从store的decodeZones取，而非eventList（因为包含了消防、采集等设备）
    dashboard页面，检查获取mac是否正确（现从eventList取）-- 2021.01.10因mac唯一，可以从eventList中取
3. 密码格式校验增加，5-16位字符
4. 设置播放模式watch优化，如果失败回滚图标