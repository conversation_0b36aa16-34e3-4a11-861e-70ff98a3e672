<template>
  <div id="settings-wrapper" v-show="isShowSetting">
    <v-tooltip left open-delay="500">
      <template v-slot:activator="{ attrs, on }">
        <v-card
          id="settings"
          class="py-2 px-4"
          color="rgba(0, 0, 0, .3)"
          dark
          flat
          link
          min-width="100"
          style="position: fixed; top: 25px; right: -35px; border-radius: 8px;"
          v-bind="attrs"
          v-on="on"
        >
          <v-icon large>
            mdi-cog
          </v-icon>
        </v-card>
      </template>
      <span>{{ $t('settings.themeSettings') }}</span>
    </v-tooltip>
    <v-menu
      v-model="menu"
      :close-on-content-click="false"
      activator="#settings"
      bottom
      content-class="v-settings"
      left
      nudge-left="8"
      offset-x
      origin="top right"
      transition="scale-transition"
    >
      <v-card
        class="text-center mb-0"
        width="300"
      >
        <v-card-text>
          <strong class="mb-3 d-inline-block text-lg-h4">{{ $t('settings.themeSettings') }}</strong>

          <v-item-group v-model="color">
            <v-item
              v-for="color in colors"
              :key="color"
              :value="color"
            >
              <template v-slot="{ active, toggle }">
                <v-avatar
                  :class="active && 'v-settings__item--active'"
                  :color="color"
                  class="v-settings__item"
                  size="25"
                  @click="toggle"
                />
              </template>
            </v-item>
          </v-item-group>

          <v-divider class="my-4 secondary" />

          <v-row
            align="center"
            no-gutters
          >
            <v-col cols="auto">
              {{ $t('settings.darkMode') }}
            </v-col>

            <v-spacer />

            <v-col cols="auto">
              <v-switch
                v-model="$vuetify.theme.dark"
                class="ma-0 pa-0"
                color="primary"
                hide-details
              />
            </v-col>
          </v-row>

          <v-divider class="my-4 secondary" />

          <v-row
            align="center"
            no-gutters
          >
            <v-col cols="auto">
              {{ $t('settings.sidebarBackground') }}
            </v-col>

            <v-spacer />

            <v-col cols="auto">
              <v-switch
                v-model="showImg"
                class="ma-0 pa-0"
                color="primary"
                hide-details
              />
            </v-col>
          </v-row>

          <v-divider class="my-4 secondary" />

          <strong class="mb-3 d-inline-block">{{ $t('settings.backgroundImage') }}</strong>

          <v-item-group
            v-model="image"
            class="d-flex justify-space-between mb-3"
          >
            <v-item
              v-for="image in images"
              :key="image"
              :value="image"
              class="mx-1"
            >
              <template v-slot="{ active, toggle }">
                <v-sheet
                  :class="active && 'v-settings__item--active'"
                  class="d-inline-block v-settings__item"
                  @click="toggle"
                >
                  <v-img
                    :src="getSideBarPath(image)"
                    height="100"
                    width="50"
                  />
                </v-sheet>
              </template>
            </v-item>
          </v-item-group>

          <v-btn
            block
            class="mb-3"
            color="success"
            default
            rel="noopener"
            target="_blank"
          >
            {{ $t('settings.guideSettings') }}
          </v-btn>

          <v-btn
            block
            class="mb-3"
            color="grey darken-1"
            dark
            default
            rel="noopener"
            target="_blank"
          >
            {{ $t('settings.documentation') }}
          </v-btn>

          <div class="my-12" />

          <div>
            <strong class="mb-3 d-inline-block">THANK YOU FOR TRUSTING!</strong>
          </div>

        </v-card-text>
      </v-card>
    </v-menu>
  </div>
</template>

<script>
  // Mixins
  import Proxyable from 'vuetify/lib/mixins/proxyable'
  import { mapMutations, mapState } from 'vuex'

  export default {
    name: 'DashboardCoreSettings',

    mixins: [Proxyable],

    data: () => ({
      color: '#E91E63',
      colors: [
        '#9C27b0',
        '#00CAE3',
        '#4CAF50',
        '#ff9800',
        '#E91E63',
        '#ea5119',
      ],
      image: 'sidebar1.jpg',
      images: [
        'sidebar1.jpg',
        'sidebar2.jpg',
        'sidebar3.jpg',
        'sidebar4.jpg',
      ],
      menu: false,
      showImg: true,
    }),

    computed: {
      ...mapState(['barImage']),
      isShowSetting() {
        return process.env.VUE_APP_IS_SHOW_SETTING === 'true' && this.$route.name !== '控制中心'
      }
    },

    watch: {
      color (val) {
        this.$vuetify.theme.themes[this.isDark ? 'dark' : 'light'].primary = val
      },
      showImg (val) {
        this.setShowBarImage(val)
      },
      image (val) {
        if (val == null) {
          val = ''
        }
        this.setBarImage(val)
      },
    },

    methods: {
      ...mapMutations({
        setBarImage: 'SET_BAR_IMAGE',
        setShowBarImage: 'SET_SHOW_BAR_IMAGE'
      }),
      getSideBarPath (image) {
        return require('@/assets/' + image)
      },
    },
  }
</script>

<style lang="sass">
  .v-settings
    .v-item-group > *
      cursor: pointer

    &__item
      border-width: 3px
      border-style: solid
      border-color: transparent !important

      &--active
        border-color: #00cae3 !important
</style>
